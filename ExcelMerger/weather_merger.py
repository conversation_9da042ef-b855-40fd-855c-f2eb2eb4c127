#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气数据合并工具
将天气Excel文件中的天气信息根据地区和日期合并到用电量数据中
"""

import pandas as pd
import os
from datetime import datetime, timedelta
import numpy as np

class WeatherMerger:
    def __init__(self):
        """初始化天气合并器"""
        # 地区名称映射（处理可能的名称差异）
        self.region_mapping = {
            '海宁': ['海宁', '海宁市'],
            '衢州': ['衢州', '衢州市'],
            '诸暨': ['诸暨', '诸暨市'],
            '温州': ['温州', '温州市'],
            '杭州': ['杭州', '杭州市'],
            '宁波': ['宁波', '宁波市'],
            '嘉兴': ['嘉兴', '嘉兴市'],
            '湖州': ['湖州', '湖州市'],
            '绍兴': ['绍兴', '绍兴市'],
            '金华': ['金华', '金华市'],
            '台州': ['台州', '台州市'],
            '丽水': ['丽水', '丽水市']
        }
    
    def normalize_region_name(self, region):
        """
        标准化地区名称
        
        Args:
            region: 原始地区名称
            
        Returns:
            str: 标准化后的地区名称
        """
        if pd.isna(region):
            return None
            
        region = str(region).strip()
        
        # 查找匹配的标准地区名称
        for standard_name, variants in self.region_mapping.items():
            if region in variants:
                return standard_name
        
        return region
    
    def normalize_date(self, date_value):
        """
        标准化日期格式
        
        Args:
            date_value: 原始日期值
            
        Returns:
            str: 标准化后的日期字符串 (YYYY-MM-DD)
        """
        if pd.isna(date_value):
            return None
            
        try:
            # 如果已经是datetime对象
            if isinstance(date_value, datetime):
                return date_value.strftime('%Y-%m-%d')
            
            # 如果是字符串，尝试解析
            date_str = str(date_value).strip()
            
            # 尝试不同的日期格式
            date_formats = [
                '%Y-%m-%d',
                '%Y/%m/%d',
                '%m/%d/%Y',
                '%d/%m/%Y',
                '%Y年%m月%d日',
                '%m月%d日'
            ]
            
            for fmt in date_formats:
                try:
                    if fmt == '%m月%d日':
                        # 对于只有月日的格式，假设是2025年
                        parsed_date = datetime.strptime(f'2025年{date_str}', '%Y年%m月%d日')
                    else:
                        parsed_date = datetime.strptime(date_str, fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue
            
            # 如果都失败了，尝试pandas的日期解析
            parsed_date = pd.to_datetime(date_value)
            return parsed_date.strftime('%Y-%m-%d')
            
        except Exception as e:
            print(f"日期解析失败: {date_value} - {e}")
            return None
    
    def read_and_prepare_data(self, usage_file, weather_file):
        """
        读取并预处理两个Excel文件
        
        Args:
            usage_file: 用电量文件路径
            weather_file: 天气文件路径
            
        Returns:
            tuple: (用电量DataFrame, 天气DataFrame)
        """
        try:
            # 读取用电量文件
            print(f"正在读取用电量文件: {os.path.basename(usage_file)}")
            usage_df = pd.read_excel(usage_file)
            print(f"用电量文件读取成功，共 {len(usage_df)} 行数据")
            print(f"用电量文件列名: {list(usage_df.columns)}")
            
            # 读取天气文件
            print(f"\n正在读取天气文件: {os.path.basename(weather_file)}")
            weather_df = pd.read_excel(weather_file)
            print(f"天气文件读取成功，共 {len(weather_df)} 行数据")
            print(f"天气文件列名: {list(weather_df.columns)}")
            
            # 预处理用电量数据
            print(f"\n正在预处理用电量数据...")
            usage_df['标准化地区'] = usage_df['地区'].apply(self.normalize_region_name)
            usage_df['标准化日期'] = usage_df['时间'].apply(self.normalize_date)
            
            # 预处理天气数据
            print(f"正在预处理天气数据...")
            weather_df['标准化地区'] = weather_df['城市'].apply(self.normalize_region_name)
            weather_df['标准化日期'] = weather_df['日期'].apply(self.normalize_date)
            
            # 显示预处理结果
            print(f"\n用电量数据中的唯一地区: {sorted(usage_df['标准化地区'].dropna().unique())}")
            print(f"天气数据中的唯一地区: {sorted(weather_df['标准化地区'].dropna().unique())}")
            
            return usage_df, weather_df
            
        except Exception as e:
            print(f"读取文件时发生错误: {e}")
            return None, None
    
    def merge_weather_data(self, usage_df, weather_df):
        """
        合并天气数据到用电量数据中

        Args:
            usage_df: 用电量DataFrame
            weather_df: 天气DataFrame

        Returns:
            DataFrame: 合并后的数据
        """
        try:
            print(f"\n正在合并天气数据...")

            # 创建天气数据的映射字典
            weather_mapping = {}
            for _, row in weather_df.iterrows():
                key = (row['标准化地区'], row['标准化日期'])
                weather_mapping[key] = row['天气情况']

            print(f"天气映射字典创建完成，共 {len(weather_mapping)} 个条目")

            # 为用电量数据添加天气信息
            def get_weather(row):
                key = (row['标准化地区'], row['标准化日期'])
                return weather_mapping.get(key, None)

            usage_df['新天气'] = usage_df.apply(get_weather, axis=1)

            # 统计匹配情况
            matched_count = usage_df['新天气'].notna().sum()
            total_count = len(usage_df)

            print(f"\n天气数据匹配结果:")
            print(f"总记录数: {total_count}")
            print(f"成功匹配天气: {matched_count}")
            print(f"未匹配天气: {total_count - matched_count}")
            print(f"天气匹配率: {matched_count/total_count*100:.2f}%")

            # 显示未匹配的地区和日期组合
            unmatched_df = usage_df[usage_df['新天气'].isna()]
            if len(unmatched_df) > 0:
                print(f"\n未匹配天气的地区-日期组合:")
                unmatched_combinations = unmatched_df[['标准化地区', '标准化日期']].drop_duplicates()
                for _, row in unmatched_combinations.head(10).iterrows():
                    print(f"  地区: {row['标准化地区']}, 日期: {row['标准化日期']}")
                if len(unmatched_combinations) > 10:
                    print(f"  ... 还有 {len(unmatched_combinations) - 10} 个未匹配的组合")

                # 尝试填充缺失的天气数据
                print(f"\n正在尝试填充缺失的天气数据...")
                usage_df = self.fill_missing_weather(usage_df, weather_df)

                # 更新匹配统计
                filled_matched_count = usage_df['新天气'].notna().sum()
                print(f"\n填充后的天气数据匹配结果:")
                print(f"总记录数: {total_count}")
                print(f"成功匹配天气: {filled_matched_count}")
                print(f"未匹配天气: {total_count - filled_matched_count}")
                print(f"天气匹配率: {filled_matched_count/total_count*100:.2f}%")

            return usage_df

        except Exception as e:
            print(f"合并数据时发生错误: {e}")
            return None

    def fill_missing_weather(self, usage_df, weather_df):
        """
        填充缺失的天气数据

        Args:
            usage_df: 用电量DataFrame
            weather_df: 天气DataFrame

        Returns:
            DataFrame: 填充后的DataFrame
        """
        try:
            # 获取所有未匹配的记录
            unmatched_df = usage_df[usage_df['新天气'].isna()].copy()

            # 按地区和日期分组处理
            for (region, date), group in unmatched_df.groupby(['标准化地区', '标准化日期']):
                if pd.isna(region) or region is None:
                    continue  # 跳过没有地区信息的记录

                print(f"  尝试填充 {region} 在 {date} 的天气数据...")

                # 策略1: 使用相邻日期的天气数据
                nearby_weather = self.get_nearby_date_weather(weather_df, region, date)
                if nearby_weather:
                    print(f"    找到相邻日期的天气: {nearby_weather}")
                    usage_df.loc[(usage_df['标准化地区'] == region) &
                                (usage_df['标准化日期'] == date), '新天气'] = nearby_weather
                    continue

                # 策略2: 使用相同地区的平均天气
                common_weather = self.get_common_weather_for_region(weather_df, region)
                if common_weather:
                    print(f"    使用地区常见天气: {common_weather}")
                    usage_df.loc[(usage_df['标准化地区'] == region) &
                                (usage_df['标准化日期'] == date), '新天气'] = common_weather
                    continue

                # 策略3: 使用相同日期其他地区的天气
                similar_weather = self.get_similar_region_weather(weather_df, date)
                if similar_weather:
                    print(f"    使用相似地区天气: {similar_weather}")
                    usage_df.loc[(usage_df['标准化地区'] == region) &
                                (usage_df['标准化日期'] == date), '新天气'] = similar_weather

            return usage_df

        except Exception as e:
            print(f"填充缺失天气数据时发生错误: {e}")
            return usage_df

    def get_nearby_date_weather(self, weather_df, region, date):
        """
        获取相邻日期的天气数据

        Args:
            weather_df: 天气DataFrame
            region: 地区
            date: 日期

        Returns:
            str: 相邻日期的天气情况，如果没有找到则返回None
        """
        try:
            # 将日期转换为datetime对象
            target_date = datetime.strptime(date, '%Y-%m-%d')

            # 查找前后3天的天气数据
            for days_diff in [1, -1, 2, -2, 3, -3]:
                nearby_date = target_date + timedelta(days=days_diff)
                nearby_date_str = nearby_date.strftime('%Y-%m-%d')

                # 在天气数据中查找
                nearby_weather = weather_df[(weather_df['标准化地区'] == region) &
                                          (weather_df['标准化日期'] == nearby_date_str)]

                if len(nearby_weather) > 0:
                    return nearby_weather.iloc[0]['天气情况']

            return None

        except Exception as e:
            print(f"获取相邻日期天气时发生错误: {e}")
            return None

    def get_common_weather_for_region(self, weather_df, region):
        """
        获取地区的常见天气

        Args:
            weather_df: 天气DataFrame
            region: 地区

        Returns:
            str: 常见天气情况，如果没有找到则返回None
        """
        try:
            # 获取该地区的所有天气记录
            region_weather = weather_df[weather_df['标准化地区'] == region]

            if len(region_weather) > 0:
                # 获取最常见的天气
                weather_counts = region_weather['天气情况'].value_counts()
                return weather_counts.index[0]

            return None

        except Exception as e:
            print(f"获取地区常见天气时发生错误: {e}")
            return None

    def get_similar_region_weather(self, weather_df, date):
        """
        获取相同日期其他地区的天气

        Args:
            weather_df: 天气DataFrame
            date: 日期

        Returns:
            str: 其他地区的天气情况，如果没有找到则返回None
        """
        try:
            # 获取该日期的所有天气记录
            date_weather = weather_df[weather_df['标准化日期'] == date]

            if len(date_weather) > 0:
                # 获取最常见的天气
                weather_counts = date_weather['天气情况'].value_counts()
                return weather_counts.index[0]

            return None

        except Exception as e:
            print(f"获取相似地区天气时发生错误: {e}")
            return None
    
    def save_result(self, merged_df, output_file):
        """
        保存合并结果
        
        Args:
            merged_df: 合并后的DataFrame
            output_file: 输出文件路径
        """
        try:
            # 清理不需要的辅助列
            columns_to_drop = ['标准化地区', '标准化日期']
            cleaned_df = merged_df.drop(columns=[col for col in columns_to_drop if col in merged_df.columns])
            
            # 重新排列列的顺序，将新天气列放在合适的位置
            cols = list(cleaned_df.columns)
            if '新天气' in cols:
                cols.remove('新天气')
                # 在地区列后面插入新天气列
                if '地区' in cols:
                    region_index = cols.index('地区')
                    cols.insert(region_index + 1, '新天气')
                else:
                    cols.append('新天气')
            
            cleaned_df = cleaned_df[cols]
            
            # 保存到Excel文件
            cleaned_df.to_excel(output_file, index=False)
            print(f"\n结果已保存到: {output_file}")
            
            # 显示预览
            print(f"\n数据预览 (前5行):")
            preview_cols = ['地区', '时间', '新天气', '总电量(kWh)']
            available_cols = [col for col in preview_cols if col in cleaned_df.columns]
            print(cleaned_df[available_cols].head())
            
            return True
            
        except Exception as e:
            print(f"保存文件时发生错误: {e}")
            return False
    
    def process_files(self, usage_file, weather_file, output_file):
        """
        处理文件的主函数
        
        Args:
            usage_file: 用电量文件路径
            weather_file: 天气文件路径
            output_file: 输出文件路径
        """
        print("天气数据合并工具")
        print("=" * 50)
        
        # 检查文件是否存在
        if not os.path.exists(usage_file):
            print(f"错误: 用电量文件不存在 - {usage_file}")
            return False
        
        if not os.path.exists(weather_file):
            print(f"错误: 天气文件不存在 - {weather_file}")
            return False
        
        # 读取和预处理数据
        usage_df, weather_df = self.read_and_prepare_data(usage_file, weather_file)
        if usage_df is None or weather_df is None:
            return False
        
        # 合并天气数据
        merged_df = self.merge_weather_data(usage_df, weather_df)
        if merged_df is None:
            return False
        
        # 保存结果
        success = self.save_result(merged_df, output_file)
        
        if success:
            print(f"\n处理完成！")
        else:
            print(f"\n处理失败！")
        
        return success

def main():
    """主函数"""
    # 文件路径
    usage_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/合并结果_6月用电量信息含地区.xlsx"
    weather_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/6月份全部地方天气.xlsx"
    output_file = "/Users/<USER>/RiderProjects/Solution3/合并结果_6月用电量含完整天气信息.xlsx"
    
    # 创建天气合并器并处理文件
    merger = WeatherMerger()
    merger.process_files(usage_file, weather_file, output_file)

if __name__ == "__main__":
    main()
