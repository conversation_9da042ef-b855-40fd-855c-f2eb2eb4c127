#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超精度用电量预测模型
解决MAE过大和R²为负的问题，实现个体用户级别的精准预测
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score, mean_absolute_percentage_error
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

class UltraPrecisionModel:
    def __init__(self):
        """
        初始化超精度预测模型
        """
        self.user_models = {}  # 为每个用户群体建立专门模型
        self.user_clusters = {}
        self.weather_factors = {}
        self.time_factors = {}
        
        print("🚀 超精度用电量预测模型")
        print("🎯 解决个体预测精度问题")
    
    def deep_problem_analysis(self):
        """
        深度分析当前预测问题
        """
        print("\n🔍 深度分析预测问题...")
        
        # 加载所有数据
        train_file = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        actual_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250718-20250719.xlsx'
        
        df_train = pd.read_excel(train_file)
        df_actual = pd.read_excel(actual_file)
        
        # 时间处理
        df_train['时间'] = pd.to_datetime(df_train['时间'])
        df_actual['时间'] = pd.to_datetime(df_actual['时间'])
        
        # 分析用电量分布
        print(f"\n📊 用电量分布分析:")
        print(f"训练数据用电量: {df_train['总电量(kWh)'].describe()}")
        
        df_actual_18 = df_actual[df_actual['时间'].dt.date == pd.to_datetime('2025-07-18').date()]
        df_actual_19 = df_actual[df_actual['时间'].dt.date == pd.to_datetime('2025-07-19').date()]
        
        print(f"18号实际用电量: {df_actual_18['总电量(kWh)'].describe()}")
        print(f"19号实际用电量: {df_actual_19['总电量(kWh)'].describe()}")
        
        # 分析用户用电模式的稳定性
        user_stability = df_train.groupby('户号')['总电量(kWh)'].agg(['mean', 'std', 'count']).reset_index()
        user_stability['变异系数'] = user_stability['std'] / (user_stability['mean'] + 1)
        user_stability['稳定性'] = pd.cut(user_stability['变异系数'], 
                                      bins=[0, 0.2, 0.5, 1.0, np.inf], 
                                      labels=['极稳定', '稳定', '波动', '极波动'])
        
        print(f"\n👥 用户稳定性分析:")
        print(user_stability['稳定性'].value_counts())
        
        # 分析18-19号的用电变化
        if len(df_actual_18) > 0 and len(df_actual_19) > 0:
            df_change = pd.merge(df_actual_18[['户号', '总电量(kWh)']], 
                               df_actual_19[['户号', '总电量(kWh)']], 
                               on='户号', suffixes=('_18', '_19'))
            df_change['日变化率'] = (df_change['总电量(kWh)_19'] - df_change['总电量(kWh)_18']) / (df_change['总电量(kWh)_18'] + 1)
            
            print(f"\n📈 18-19号用电变化:")
            print(f"平均变化率: {df_change['日变化率'].mean():.3f}")
            print(f"变化率标准差: {df_change['日变化率'].std():.3f}")
            print(f"变化率范围: {df_change['日变化率'].min():.3f} ~ {df_change['日变化率'].max():.3f}")
        
        return df_train, df_actual_18, df_actual_19, user_stability
    
    def create_user_clusters(self, df_train, user_stability):
        """
        创建用户聚类，为不同类型用户建立专门模型
        """
        print("\n👥 创建用户聚类...")
        
        # 计算用户特征
        user_features = df_train.groupby('户号').agg({
            '总电量(kWh)': ['mean', 'std', 'min', 'max'],
            '尖电量(kWh)': 'mean',
            '峰电量(kWh)': 'mean',
            '平电量(kWh)': 'mean',
            '谷电量(kWh)': 'mean',
            '公司名称': 'first',
            '地区': 'first'
        }).round(2)
        
        # 扁平化列名
        user_features.columns = ['_'.join(col).strip() for col in user_features.columns]
        user_features = user_features.reset_index()
        
        # 合并稳定性信息
        user_features = user_features.merge(user_stability[['户号', '变异系数', '稳定性']], on='户号')
        
        # 计算聚类特征
        clustering_features = [
            '总电量(kWh)_mean', '总电量(kWh)_std', '变异系数',
            '尖电量(kWh)_mean', '峰电量(kWh)_mean', '平电量(kWh)_mean', '谷电量(kWh)_mean'
        ]
        
        X_cluster = user_features[clustering_features].fillna(0)
        
        # 标准化
        scaler = StandardScaler()
        X_cluster_scaled = scaler.fit_transform(X_cluster)
        
        # K-means聚类 - 增加聚类数量以获得更精细的分组
        kmeans = KMeans(n_clusters=8, random_state=42, n_init=10)
        user_features['用户群体'] = kmeans.fit_predict(X_cluster_scaled)
        
        # 分析每个群体的特征
        print(f"✅ 创建了8个用户群体:")
        for cluster in range(8):
            cluster_data = user_features[user_features['用户群体'] == cluster]
            avg_power = cluster_data['总电量(kWh)_mean'].mean()
            avg_stability = cluster_data['变异系数'].mean()
            count = len(cluster_data)
            
            print(f"  群体{cluster}: {count}个用户, 平均用电{avg_power:.0f}kWh, 变异系数{avg_stability:.3f}")
        
        self.user_clusters = user_features
        
        return user_features
    
    def train_cluster_specific_models(self, df_train, df_actual_18):
        """
        为每个用户群体训练专门的模型
        """
        print("\n🤖 为每个用户群体训练专门模型...")
        
        if not hasattr(self, 'user_clusters'):
            print("❌ 请先创建用户聚类")
            return False
        
        # 准备训练数据
        df_train_with_cluster = df_train.merge(
            self.user_clusters[['户号', '用户群体']], on='户号', how='left'
        )
        
        # 添加18号真实数据作为目标
        if len(df_actual_18) > 0:
            df_actual_18_target = df_actual_18[['户号', '总电量(kWh)']].copy()
            df_actual_18_target.columns = ['户号', '目标电量']
            
            df_train_with_target = df_train_with_cluster.merge(
                df_actual_18_target, on='户号', how='inner'
            )
        else:
            print("❌ 没有18号数据作为训练目标")
            return False
        
        print(f"有效训练数据: {len(df_train_with_target)} 条")
        
        # 为每个群体训练模型
        for cluster in range(8):
            cluster_data = df_train_with_target[df_train_with_target['用户群体'] == cluster]
            
            if len(cluster_data) < 10:  # 数据太少的群体跳过
                print(f"  群体{cluster}: 数据不足({len(cluster_data)}条)，跳过")
                continue
            
            print(f"  训练群体{cluster}模型: {len(cluster_data)}条数据")
            
            # 按用户聚合特征
            user_data = cluster_data.groupby('户号').agg({
                '总电量(kWh)': ['mean', 'std', 'count'],
                '最高温度(°C)': 'mean',
                '最低温度(°C)': 'mean',
                '湿度(%)': 'mean',
                'AQI': 'mean',
                '目标电量': 'first'
            }).round(2)
            
            # 扁平化列名
            user_data.columns = ['_'.join(col).strip() for col in user_data.columns]
            user_data = user_data.reset_index()
            
            # 特征选择
            feature_columns = [
                '总电量(kWh)_mean', '总电量(kWh)_std', '总电量(kWh)_count',
                '最高温度(°C)_mean', '最低温度(°C)_mean', '湿度(%)_mean', 'AQI_mean'
            ]
            
            X = user_data[feature_columns].fillna(0)
            y = user_data['目标电量_first']
            
            if len(X) < 5:
                print(f"    群体{cluster}: 用户数不足({len(X)}个)，跳过")
                continue
            
            # 选择模型 - 根据数据量选择
            if len(X) < 20:
                model = Ridge(alpha=1.0)
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X)
                model.fit(X_scaled, y)
                self.user_models[cluster] = {'model': model, 'scaler': scaler, 'features': feature_columns}
            else:
                model = RandomForestRegressor(n_estimators=50, max_depth=8, random_state=42)
                model.fit(X, y)
                self.user_models[cluster] = {'model': model, 'scaler': None, 'features': feature_columns}
            
            # 评估
            if len(X) >= 5:
                if cluster in self.user_models and self.user_models[cluster]['scaler'] is not None:
                    y_pred = model.predict(X_scaled)
                else:
                    y_pred = model.predict(X)
                
                r2 = r2_score(y, y_pred)
                mae = mean_absolute_error(y, y_pred)
                
                print(f"    群体{cluster}: R²={r2:.4f}, MAE={mae:.1f}kWh")
        
        print(f"✅ 完成 {len(self.user_models)} 个群体模型训练")
        
        return True
    
    def predict_with_cluster_models(self, target_date='2025-07-19'):
        """
        使用群体模型进行预测
        """
        print(f"\n🎯 使用群体模型预测 {target_date}...")
        
        if not self.user_models:
            print("❌ 请先训练群体模型")
            return None
        
        # 获取所有用户的群体信息
        all_users = self.user_clusters.copy()
        
        # 默认天气条件（可以用真实爬取的数据替换）
        default_weather = {
            '最高温度(°C)_mean': 35.0,
            '最低温度(°C)_mean': 27.0,
            '湿度(%)_mean': 75.0,
            'AQI_mean': 40.0
        }
        
        predictions = []
        
        for _, user_row in all_users.iterrows():
            user_id = user_row['户号']
            cluster = user_row['用户群体']
            
            if cluster not in self.user_models:
                # 使用全局平均值
                pred_power = user_row['总电量(kWh)_mean']
            else:
                # 使用群体模型预测
                model_info = self.user_models[cluster]
                model = model_info['model']
                scaler = model_info['scaler']
                features = model_info['features']
                
                # 准备预测特征
                pred_features = {
                    '总电量(kWh)_mean': user_row['总电量(kWh)_mean'],
                    '总电量(kWh)_std': user_row['总电量(kWh)_std'],
                    '总电量(kWh)_count': user_row.get('count', 30)  # 默认30天数据
                }
                pred_features.update(default_weather)
                
                X_pred = pd.DataFrame([pred_features])[features].fillna(0)
                
                if scaler is not None:
                    X_pred_scaled = scaler.transform(X_pred)
                    pred_power = model.predict(X_pred_scaled)[0]
                else:
                    pred_power = model.predict(X_pred)[0]
                
                pred_power = max(pred_power, 0)  # 确保非负
            
            predictions.append({
                '表计号': user_id,
                '户号': user_id,
                '时间': target_date,
                '总电量(kWh)': round(pred_power, 1),
                '用户群体': cluster
            })
        
        df_pred = pd.DataFrame(predictions)
        
        # 添加分时电量预测
        df_pred = self.add_time_segment_predictions(df_pred)
        
        print(f"✅ 群体模型预测完成: {len(df_pred)} 个用户")
        print(f"📊 总预测电量: {df_pred['总电量(kWh)'].sum():.1f} kWh")
        print(f"📊 平均用电量: {df_pred['总电量(kWh)'].mean():.1f} kWh")
        
        return df_pred

    def add_time_segment_predictions(self, df_pred):
        """
        添加分时电量预测
        """
        # 基于真实数据的最优比例
        optimal_ratios = {
            '尖电量': 0.1940,
            '峰电量': 0.2807,
            '平电量': 0.1380,
            '谷电量': 0.3873
        }

        # 为每个用户分配分时电量
        df_pred['尖电量(kWh)'] = (df_pred['总电量(kWh)'] * optimal_ratios['尖电量']).round(1)
        df_pred['峰电量(kWh)'] = (df_pred['总电量(kWh)'] * optimal_ratios['峰电量']).round(1)
        df_pred['平电量(kWh)'] = (df_pred['总电量(kWh)'] * optimal_ratios['平电量']).round(1)
        df_pred['谷电量(kWh)'] = (df_pred['总电量(kWh)'] * optimal_ratios['谷电量']).round(1)

        return df_pred

    def evaluate_ultra_precision(self, df_pred, df_actual_19):
        """
        评估超精度模型效果
        """
        print(f"\n📈 评估超精度模型效果...")

        if len(df_actual_19) == 0:
            print("❌ 没有19号真实数据用于评估")
            return None

        # 合并预测和真实数据
        df_compare = pd.merge(df_pred, df_actual_19, on='户号', suffixes=('_pred', '_actual'))

        print(f"匹配用户数: {len(df_compare)}")

        if len(df_compare) == 0:
            print("❌ 没有匹配的用户")
            return None

        # 计算总体误差指标
        mae = mean_absolute_error(df_compare['总电量(kWh)_actual'], df_compare['总电量(kWh)_pred'])
        rmse = np.sqrt(mean_squared_error(df_compare['总电量(kWh)_actual'], df_compare['总电量(kWh)_pred']))
        r2 = r2_score(df_compare['总电量(kWh)_actual'], df_compare['总电量(kWh)_pred'])

        # 计算MAPE时处理零值
        actual_nonzero = df_compare['总电量(kWh)_actual'] != 0
        if actual_nonzero.sum() > 0:
            mape = np.mean(np.abs((df_compare.loc[actual_nonzero, '总电量(kWh)_actual'] -
                                 df_compare.loc[actual_nonzero, '总电量(kWh)_pred']) /
                                df_compare.loc[actual_nonzero, '总电量(kWh)_actual'])) * 100
        else:
            mape = 0

        # 总量对比
        pred_total = df_compare['总电量(kWh)_pred'].sum()
        actual_total = df_compare['总电量(kWh)_actual'].sum()
        total_error_pct = (pred_total - actual_total) / actual_total * 100 if actual_total > 0 else 0

        print(f"\n📊 超精度模型评估结果:")
        print(f"   MAE: {mae:.1f} kWh")
        print(f"   RMSE: {rmse:.1f} kWh")
        print(f"   R²: {r2:.4f}")
        print(f"   MAPE: {mape:.1f}%")
        print(f"   总量误差: {total_error_pct:.1f}%")

        # 按群体分析误差
        if '用户群体_pred' in df_compare.columns:
            print(f"\n📊 按群体分析误差:")
            for cluster in sorted(df_compare['用户群体_pred'].unique()):
                cluster_data = df_compare[df_compare['用户群体_pred'] == cluster]
                if len(cluster_data) > 0:
                    cluster_mae = mean_absolute_error(cluster_data['总电量(kWh)_actual'],
                                                    cluster_data['总电量(kWh)_pred'])
                    cluster_r2 = r2_score(cluster_data['总电量(kWh)_actual'],
                                        cluster_data['总电量(kWh)_pred'])
                    print(f"   群体{cluster}: {len(cluster_data)}个用户, MAE={cluster_mae:.1f}kWh, R²={cluster_r2:.4f}")

        # 分析预测误差分布
        df_compare['绝对误差'] = abs(df_compare['总电量(kWh)_pred'] - df_compare['总电量(kWh)_actual'])
        df_compare['相对误差'] = df_compare['绝对误差'] / (df_compare['总电量(kWh)_actual'] + 1) * 100

        print(f"\n📊 误差分布分析:")
        print(f"   误差 < 1000kWh: {(df_compare['绝对误差'] < 1000).sum()}个用户 ({(df_compare['绝对误差'] < 1000).mean()*100:.1f}%)")
        print(f"   误差 < 2000kWh: {(df_compare['绝对误差'] < 2000).sum()}个用户 ({(df_compare['绝对误差'] < 2000).mean()*100:.1f}%)")
        print(f"   误差 < 5000kWh: {(df_compare['绝对误差'] < 5000).sum()}个用户 ({(df_compare['绝对误差'] < 5000).mean()*100:.1f}%)")

        return {
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'mape': mape,
            'total_error_pct': total_error_pct,
            'comparison_data': df_compare
        }

    def save_ultra_precision_results(self, df_pred, evaluation, target_date='2025-07-19'):
        """
        保存超精度预测结果
        """
        print(f"\n💾 保存超精度预测结果...")

        date_str = target_date.replace('-', '')
        output_file = f"/Users/<USER>/RiderProjects/Solution3/超精度预测_{date_str}.xlsx"

        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 1. 预测结果
                result_cols = ['表计号', '户号', '时间', '总电量(kWh)', '尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']
                df_pred[result_cols].to_excel(writer, sheet_name='超精度预测结果', index=False)

                # 2. 模型评估
                if evaluation:
                    eval_data = {
                        '指标': ['MAE (kWh)', 'RMSE (kWh)', 'R²', 'MAPE (%)', '总量误差 (%)'],
                        '数值': [
                            evaluation['mae'],
                            evaluation['rmse'],
                            evaluation['r2'],
                            evaluation['mape'],
                            evaluation['total_error_pct']
                        ]
                    }
                    pd.DataFrame(eval_data).to_excel(writer, sheet_name='模型评估', index=False)

                # 3. 用户群体信息
                if hasattr(self, 'user_clusters'):
                    cluster_summary = self.user_clusters.groupby('用户群体').agg({
                        '户号': 'count',
                        '总电量(kWh)_mean': 'mean',
                        '变异系数': 'mean'
                    }).round(2)
                    cluster_summary.columns = ['用户数', '平均用电量', '平均变异系数']
                    cluster_summary.to_excel(writer, sheet_name='用户群体分析', index=True)

                # 4. 详细对比（如果有评估数据）
                if evaluation and 'comparison_data' in evaluation:
                    comparison_cols = ['户号', '总电量(kWh)_pred', '总电量(kWh)_actual', '绝对误差', '相对误差']
                    available_cols = [col for col in comparison_cols if col in evaluation['comparison_data'].columns]
                    evaluation['comparison_data'][available_cols].to_excel(writer, sheet_name='详细对比', index=False)

            print(f"✅ 超精度结果已保存到: {output_file}")
            return output_file

        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return None

def main():
    """
    主函数
    """
    print("🚀 超精度用电量预测模型")
    print("🎯 解决个体预测精度问题")
    print("="*70)

    model = UltraPrecisionModel()

    try:
        # 1. 深度问题分析
        print("步骤1: 深度问题分析")
        df_train, df_actual_18, df_actual_19, user_stability = model.deep_problem_analysis()

        # 2. 创建用户聚类
        print("\n步骤2: 创建用户聚类")
        user_clusters = model.create_user_clusters(df_train, user_stability)

        # 3. 训练群体专门模型
        print("\n步骤3: 训练群体专门模型")
        model_success = model.train_cluster_specific_models(df_train, df_actual_18)

        if model_success:
            # 4. 使用群体模型预测
            print("\n步骤4: 使用群体模型预测")
            df_pred = model.predict_with_cluster_models('2025-07-19')

            if df_pred is not None:
                # 5. 评估超精度效果
                print("\n步骤5: 评估超精度效果")
                evaluation = model.evaluate_ultra_precision(df_pred, df_actual_19)

                # 6. 保存结果
                print("\n步骤6: 保存超精度结果")
                output_file = model.save_ultra_precision_results(df_pred, evaluation, '2025-07-19')

                if output_file:
                    print(f"\n🎉 超精度预测完成！")
                    print(f"📁 结果文件: {output_file}")

                    if evaluation:
                        print(f"\n🏆 超精度效果:")
                        print(f"   MAE: {evaluation['mae']:.1f} kWh")
                        print(f"   R²: {evaluation['r2']:.4f}")
                        print(f"   MAPE: {evaluation['mape']:.1f}%")
                        print(f"   总量误差: {evaluation['total_error_pct']:.1f}%")

                        # 显示改进效果
                        print(f"\n📈 相比之前的改进:")
                        print(f"   MAE: 6253.9 → {evaluation['mae']:.1f} kWh")
                        print(f"   R²: -0.7706 → {evaluation['r2']:.4f}")
                        print(f"   总量误差: 5.8% → {evaluation['total_error_pct']:.1f}%")

        else:
            print("❌ 群体模型训练失败")

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
