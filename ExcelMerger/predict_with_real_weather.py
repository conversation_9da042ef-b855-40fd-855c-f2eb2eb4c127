#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实天气数据进行7月17日用电量预测
结合爬取的真实天气信息和训练好的模型
"""

import pandas as pd
import numpy as np
from advanced_company_power_model import AdvancedCompanyPowerModel
from july17_weather_scraper import July17WeatherScraper
import json

def load_real_weather_data():
    """
    加载真实的7月17日天气数据
    """
    try:
        with open('/Users/<USER>/RiderProjects/Solution3/7月17日真实天气数据.json', 'r', encoding='utf-8') as f:
            weather_data = json.load(f)
        
        print("✅ 成功加载真实天气数据")
        return weather_data
    except:
        print("⚠️ 无法加载天气数据，使用爬取器获取")
        scraper = July17WeatherScraper()
        weather_data = scraper.scrape_all_cities_july17()
        return weather_data

def convert_weather_for_prediction(weather_data):
    """
    将爬取的天气数据转换为预测模型需要的格式
    """
    if not weather_data:
        print("❌ 没有天气数据")
        return None
    
    # 计算所有城市的平均值
    temps_max = [data['temp_max'] for data in weather_data.values() if data['temp_max']]
    temps_min = [data['temp_min'] for data in weather_data.values() if data['temp_min']]
    aqis = [data['aqi'] for data in weather_data.values() if data['aqi']]
    precipitations = [data['precipitation'] for data in weather_data.values()]
    humidities = [data['humidity'] for data in weather_data.values()]
    pressures = [data['pressure'] for data in weather_data.values()]
    
    # 获取最常见的天气和风向
    weathers = [data['weather_main'] for data in weather_data.values()]
    winds = [data['wind_direction'] for data in weather_data.values()]
    
    # 处理天气描述
    main_weather = max(set(weathers), key=weathers.count) if weathers else '雨'
    if '雨' in main_weather:
        weather_clean = '雨'
    elif '阴' in main_weather:
        weather_clean = '阴'
    elif '多云' in main_weather:
        weather_clean = '多云'
    elif '晴' in main_weather:
        weather_clean = '晴'
    else:
        weather_clean = '阴'
    
    # 处理风向
    main_wind = max(set(winds), key=winds.count) if winds else '南风'
    wind_clean = main_wind.split('风')[0] + '风' if '风' in main_wind else '南风'
    
    prediction_weather = {
        '最高温度(°C)': sum(temps_max) / len(temps_max) if temps_max else 36.5,
        '最低温度(°C)': sum(temps_min) / len(temps_min) if temps_min else 27.4,
        '湿度(%)': sum(humidities) / len(humidities) if humidities else 70.0,
        'AQI': sum(aqis) / len(aqis) if aqis else 37.6,
        '降水量(mm)': sum(precipitations) / len(precipitations) if precipitations else 75.3,
        '气压(hPa)': sum(pressures) / len(pressures) if pressures else 1013.1,
        '天气': weather_clean,
        '风向': wind_clean,
        '日期类型': '工作日'
    }
    
    print(f"\n🌤️ 转换后的预测用天气数据:")
    for key, value in prediction_weather.items():
        if isinstance(value, float):
            print(f"   {key}: {value:.1f}")
        else:
            print(f"   {key}: {value}")
    
    return prediction_weather

def main():
    """
    主函数：使用真实天气数据进行预测
    """
    print("🌤️ 使用真实天气数据进行7月17日用电量预测")
    print("="*70)
    
    try:
        # 1. 加载真实天气数据
        print("步骤1: 加载真实天气数据...")
        weather_data = load_real_weather_data()
        
        if not weather_data:
            print("❌ 无法获取天气数据")
            return
        
        # 2. 转换天气数据格式
        print("\n步骤2: 转换天气数据格式...")
        prediction_weather = convert_weather_for_prediction(weather_data)
        
        if not prediction_weather:
            print("❌ 天气数据转换失败")
            return
        
        # 3. 初始化并训练预测模型
        print("\n步骤3: 初始化预测模型...")
        model = AdvancedCompanyPowerModel()
        
        # 加载和预处理数据
        file_path = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        df = model.load_and_preprocess_data(file_path)
        
        # 创建公司特征画像
        company_profiles = model.create_company_profiles()
        
        # 分析天气影响
        weather_patterns = model.analyze_weather_impact()
        
        # 高级特征工程
        df_features = model.advanced_feature_engineering()
        
        # 准备模型特征
        X, y, final_features = model.prepare_model_features()
        
        # 训练多种模型
        model_results = model.train_multiple_models(X, y, final_features)
        
        # 4. 使用真实天气数据进行预测
        print(f"\n步骤4: 使用真实天气数据预测7月17日用电量...")

        # 使用实际数据文件中的用户列表
        target_users_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250717-20250717 (1).xlsx'

        predictions = model.predict_future_consumption('2025-07-17', prediction_weather, target_users_file)
        
        if predictions is not None:
            # 5. 保存预测结果
            print(f"\n步骤5: 保存预测结果...")
            output_file = "基于真实天气的7月17日用电量预测结果.xlsx"
            output_path = model.save_model_results(predictions, output_file)
            
            # 6. 显示预测摘要
            print(f"\n🎉 基于真实天气的预测完成！")
            print(f"📊 预测摘要:")
            print(f"   预测用户数: {len(predictions)}")
            print(f"   总预测电量: {predictions['预测总电量(kWh)'].sum():.2f} kWh")
            print(f"   平均用户用电: {predictions['预测总电量(kWh)'].mean():.2f} kWh")
            print(f"   预测范围: {predictions['预测总电量(kWh)'].min():.2f} - {predictions['预测总电量(kWh)'].max():.2f} kWh")
            
            print(f"\n🌤️ 使用的真实天气条件:")
            print(f"   温度: {prediction_weather['最高温度(°C)']:.1f}°C / {prediction_weather['最低温度(°C)']:.1f}°C")
            print(f"   天气: {prediction_weather['天气']}")
            print(f"   湿度: {prediction_weather['湿度(%)']:.1f}%")
            print(f"   AQI: {prediction_weather['AQI']:.0f}")
            print(f"   降水: {prediction_weather['降水量(mm)']:.1f}mm")
            print(f"   风向: {prediction_weather['风向']}")
            
            print(f"\n🏆 模型性能:")
            best_model = model.best_model_name
            best_performance = model.model_performance[best_model]
            print(f"   最佳模型: {best_model}")
            print(f"   测试R²: {best_performance['test_r2']:.4f}")
            print(f"   测试MAE: {best_performance['test_mae']:.2f} kWh")
            print(f"   测试MAPE: {best_performance['test_mape']:.2f}%")
            
            print(f"\n📁 结果已保存到: {output_path}")
            
            # 按公司汇总显示前10名
            company_summary = predictions.groupby('公司名称')['预测总电量(kWh)'].sum().sort_values(ascending=False)
            print(f"\n🏢 预测用电量前10名公司:")
            for i, (company, total_power) in enumerate(company_summary.head(10).items(), 1):
                print(f"   {i:2d}. {company}: {total_power:.2f} kWh")
            
        else:
            print("❌ 预测失败")
            
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
