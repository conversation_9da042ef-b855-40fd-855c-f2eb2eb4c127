#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件合并工具
将售电签约客户.xlsx中的地区信息匹配到110kV以下用户历史用电量信息查询文件中
"""

import pandas as pd
import os
import sys
from pathlib import Path

def read_excel_files(customer_file, usage_file):
    """
    读取两个Excel文件
    
    Args:
        customer_file: 售电签约客户.xlsx文件路径
        usage_file: 110kV以下用户历史用电量信息查询文件路径
    
    Returns:
        tuple: (客户数据DataFrame, 用电量数据DataFrame)
    """
    try:
        # 读取售电签约客户文件
        print(f"正在读取客户文件: {os.path.basename(customer_file)}")
        customer_df = pd.read_excel(customer_file)
        print(f"客户文件读取成功，共 {len(customer_df)} 行数据")
        print(f"客户文件列名: {list(customer_df.columns)}")
        
        # 读取用电量信息文件
        print(f"\n正在读取用电量文件: {os.path.basename(usage_file)}")
        usage_df = pd.read_excel(usage_file)
        print(f"用电量文件读取成功，共 {len(usage_df)} 行数据")
        print(f"用电量文件列名: {list(usage_df.columns)}")
        
        return customer_df, usage_df
        
    except FileNotFoundError as e:
        print(f"错误: 文件未找到 - {e}")
        return None, None
    except Exception as e:
        print(f"错误: 读取文件时发生异常 - {e}")
        return None, None

def find_matching_columns(customer_df, usage_df):
    """
    查找可能的匹配列（户号相关的列）
    
    Args:
        customer_df: 客户数据DataFrame
        usage_df: 用电量数据DataFrame
    
    Returns:
        tuple: (客户文件中的户号列名, 用电量文件中的户号列名, 客户文件中的地区列名)
    """
    # 常见的户号列名关键词
    account_keywords = ['户号', '用户号', '客户号', '账号', 'account', 'customer_id', 'user_id']
    # 常见的地区列名关键词
    region_keywords = ['地区', '区域', '地址', '位置', 'region', 'area', 'location', '供电所', '所属']
    
    customer_account_col = None
    usage_account_col = None
    customer_region_col = None
    
    # 查找客户文件中的户号列
    for col in customer_df.columns:
        for keyword in account_keywords:
            if keyword in str(col).lower():
                customer_account_col = col
                break
        if customer_account_col:
            break
    
    # 查找用电量文件中的户号列
    for col in usage_df.columns:
        for keyword in account_keywords:
            if keyword in str(col).lower():
                usage_account_col = col
                break
        if usage_account_col:
            break
    
    # 查找客户文件中的地区列
    for col in customer_df.columns:
        for keyword in region_keywords:
            if keyword in str(col).lower():
                customer_region_col = col
                break
        if customer_region_col:
            break
    
    print(f"\n自动识别的列名:")
    print(f"客户文件户号列: {customer_account_col}")
    print(f"用电量文件户号列: {usage_account_col}")
    print(f"客户文件地区列: {customer_region_col}")
    
    return customer_account_col, usage_account_col, customer_region_col

def merge_data(customer_df, usage_df, customer_account_col, usage_account_col, customer_region_col):
    """
    合并数据，将地区信息添加到用电量数据中
    
    Args:
        customer_df: 客户数据DataFrame
        usage_df: 用电量数据DataFrame
        customer_account_col: 客户文件中的户号列名
        usage_account_col: 用电量文件中的户号列名
        customer_region_col: 客户文件中的地区列名
    
    Returns:
        DataFrame: 合并后的数据
    """
    try:
        # 创建客户数据的映射字典
        customer_mapping = customer_df.set_index(customer_account_col)[customer_region_col].to_dict()
        
        # 在用电量数据中添加地区列
        usage_df['地区'] = usage_df[usage_account_col].map(customer_mapping)
        
        # 统计匹配情况
        matched_count = usage_df['地区'].notna().sum()
        total_count = len(usage_df)
        
        print(f"\n匹配结果:")
        print(f"总记录数: {total_count}")
        print(f"成功匹配: {matched_count}")
        print(f"未匹配: {total_count - matched_count}")
        print(f"匹配率: {matched_count/total_count*100:.2f}%")
        
        # 显示所有未匹配的户号
        unmatched_df = usage_df[usage_df['地区'].isna()]
        if len(unmatched_df) > 0:
            print(f"\n所有未匹配的记录:")
            unique_unmatched = unmatched_df[usage_account_col].unique()
            print(f"未匹配的唯一户号数量: {len(unique_unmatched)}")
            for account in unique_unmatched:
                count = len(unmatched_df[unmatched_df[usage_account_col] == account])
                print(f"  户号: {account} (出现 {count} 次)")

            # 显示未匹配记录的详细信息
            print(f"\n未匹配记录详情:")
            print(unmatched_df[[usage_account_col, '时间', '总电量(kWh)']].to_string(index=False))
        
        return usage_df
        
    except Exception as e:
        print(f"错误: 合并数据时发生异常 - {e}")
        return None

def save_result(merged_df, output_file):
    """
    保存合并结果到新的Excel文件
    
    Args:
        merged_df: 合并后的DataFrame
        output_file: 输出文件路径
    """
    try:
        merged_df.to_excel(output_file, index=False)
        print(f"\n结果已保存到: {output_file}")
        
        # 显示前几行数据作为预览
        print(f"\n数据预览 (前5行):")
        print(merged_df.head())
        
    except Exception as e:
        print(f"错误: 保存文件时发生异常 - {e}")

def main():
    """
    主函数
    """
    print("Excel文件合并工具")
    print("=" * 50)
    
    # 指定文件路径
    customer_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/售电签约客户(2).xlsx"
    usage_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/110kV以下用户历史用电量信息查询20250601-20250630(1).xlsx"
    output_file = "/Users/<USER>/RiderProjects/Solution3/合并结果_6月用电量信息含地区.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(customer_file):
        print(f"错误: 找不到客户文件")
        print(f"路径: {customer_file}")
        return
    
    if not os.path.exists(usage_file):
        print(f"错误: 找不到用电量文件")
        print(f"路径: {usage_file}")
        return
    
    # 读取Excel文件
    customer_df, usage_df = read_excel_files(customer_file, usage_file)
    if customer_df is None or usage_df is None:
        return
    
    # 查找匹配列
    customer_account_col, usage_account_col, customer_region_col = find_matching_columns(customer_df, usage_df)
    
    if not all([customer_account_col, usage_account_col, customer_region_col]):
        print("\n错误: 无法自动识别必要的列名")
        print("请检查文件格式或使用交互版本手动指定列名")
        return
    
    # 合并数据
    merged_df = merge_data(customer_df, usage_df, customer_account_col, usage_account_col, customer_region_col)
    if merged_df is None:
        return
    
    # 保存结果
    save_result(merged_df, output_file)
    
    print(f"\n处理完成！")

if __name__ == "__main__":
    main()
