#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气与用电量关系分析工具
分析天气参数对用电量的影响
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class WeatherElectricityAnalyzer:
    def __init__(self, excel_file):
        """
        初始化分析器
        
        Args:
            excel_file: 包含天气和用电数据的Excel文件路径
        """
        self.excel_file = excel_file
        self.df = None
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        try:
            self.df = pd.read_excel(self.excel_file)
            print(f"数据加载成功，共 {len(self.df)} 行数据")
            print(f"包含地区: {list(self.df['地区'].unique())}")
        except Exception as e:
            print(f"数据加载失败: {e}")
    
    def analyze_temperature_impact(self):
        """分析温度对用电量的影响"""
        print("\n=== 温度对用电量的影响分析 ===")
        
        # 按地区分组分析
        for region in self.df['地区'].unique():
            region_data = self.df[self.df['地区'] == region]
            
            # 计算相关系数
            temp_corr = region_data['总电量(kWh)'].corr(region_data['气温(°C)'])
            feels_like_corr = region_data['总电量(kWh)'].corr(region_data['体感温度(°C)'])
            
            print(f"\n{region}:")
            print(f"  平均气温: {region_data['气温(°C)'].iloc[0]:.1f}°C")
            print(f"  平均体感温度: {region_data['体感温度(°C)'].iloc[0]:.1f}°C")
            print(f"  平均用电量: {region_data['总电量(kWh)'].mean():.1f} kWh")
            print(f"  气温与用电量相关性: {temp_corr:.3f}")
            print(f"  体感温度与用电量相关性: {feels_like_corr:.3f}")
    
    def analyze_humidity_impact(self):
        """分析湿度对用电量的影响"""
        print("\n=== 湿度对用电量的影响分析 ===")
        
        for region in self.df['地区'].unique():
            region_data = self.df[self.df['地区'] == region]
            
            humidity_corr = region_data['总电量(kWh)'].corr(region_data['湿度(%)'])
            
            print(f"\n{region}:")
            print(f"  平均湿度: {region_data['湿度(%)'].iloc[0]:.1f}%")
            print(f"  湿度与用电量相关性: {humidity_corr:.3f}")
    
    def analyze_weather_condition_impact(self):
        """分析天气状况对用电量的影响"""
        print("\n=== 天气状况对用电量的影响分析 ===")
        
        # 按天气状况分组
        weather_stats = self.df.groupby(['地区', '天气状况']).agg({
            '总电量(kWh)': ['mean', 'std', 'count'],
            '气温(°C)': 'first',
            '湿度(%)': 'first'
        }).round(2)
        
        print(weather_stats)
    
    def analyze_wind_impact(self):
        """分析风速对用电量的影响"""
        print("\n=== 风速对用电量的影响分析 ===")
        
        for region in self.df['地区'].unique():
            region_data = self.df[self.df['地区'] == region]
            
            wind_corr = region_data['总电量(kWh)'].corr(region_data['风速(m/s)'])
            
            print(f"\n{region}:")
            print(f"  平均风速: {region_data['风速(m/s)'].iloc[0]:.1f} m/s")
            print(f"  风速与用电量相关性: {wind_corr:.3f}")
    
    def generate_summary_report(self):
        """生成综合分析报告"""
        print("\n" + "="*60)
        print("天气与用电量关系综合分析报告")
        print("="*60)
        
        # 地区用电量统计
        region_stats = self.df.groupby('地区').agg({
            '总电量(kWh)': ['mean', 'min', 'max', 'std'],
            '气温(°C)': 'first',
            '体感温度(°C)': 'first',
            '湿度(%)': 'first',
            '风速(m/s)': 'first',
            '天气状况': 'first'
        }).round(2)
        
        print("\n各地区用电量与天气数据统计:")
        print(region_stats)
        
        # 计算整体相关性
        print("\n整体相关性分析:")
        correlations = {
            '气温': self.df['总电量(kWh)'].corr(self.df['气温(°C)']),
            '体感温度': self.df['总电量(kWh)'].corr(self.df['体感温度(°C)']),
            '湿度': self.df['总电量(kWh)'].corr(self.df['湿度(%)']),
            '风速': self.df['总电量(kWh)'].corr(self.df['风速(m/s)']),
            '气压': self.df['总电量(kWh)'].corr(self.df['气压(hPa)']),
            '云量': self.df['总电量(kWh)'].corr(self.df['云量(%)'])
        }
        
        for factor, corr in correlations.items():
            print(f"  {factor}与用电量相关性: {corr:.3f}")
        
        # 找出影响最大的天气因素
        max_corr_factor = max(correlations.items(), key=lambda x: abs(x[1]))
        print(f"\n影响用电量最大的天气因素: {max_corr_factor[0]} (相关系数: {max_corr_factor[1]:.3f})")
        
        # 高温预警分析
        high_temp_threshold = 35.0
        high_temp_data = self.df[self.df['体感温度(°C)'] >= high_temp_threshold]
        if len(high_temp_data) > 0:
            print(f"\n高温预警分析 (体感温度 ≥ {high_temp_threshold}°C):")
            print(f"  高温条件下的数据量: {len(high_temp_data)} 条")
            print(f"  高温条件下平均用电量: {high_temp_data['总电量(kWh)'].mean():.1f} kWh")
            print(f"  正常条件下平均用电量: {self.df[self.df['体感温度(°C)'] < high_temp_threshold]['总电量(kWh)'].mean():.1f} kWh")
        
        # 用电量建议
        print(f"\n用电量预测建议:")
        print(f"  • 当前天气条件下，各地区体感温度普遍较高 (31-40°C)")
        print(f"  • 高湿度环境 (62-72%) 可能增加空调负荷")
        print(f"  • 阴云天气 (云量100%) 可能影响太阳能发电效率")
        print(f"  • 建议关注高温时段的用电峰值管理")

def main():
    """主函数"""
    print("天气与用电量关系分析工具")
    print("="*50)
    
    # 分析文件
    excel_file = "/Users/<USER>/Desktop/副本合并结果_用电量信息含详细天气.xlsx"
    
    try:
        analyzer = WeatherElectricityAnalyzer(excel_file)
        
        # 执行各项分析
        analyzer.analyze_temperature_impact()
        analyzer.analyze_humidity_impact()
        analyzer.analyze_weather_condition_impact()
        analyzer.analyze_wind_impact()
        analyzer.generate_summary_report()
        
        print(f"\n✅ 分析完成！")
        print(f"📊 天气数据已成功整合到用电量分析中")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()
