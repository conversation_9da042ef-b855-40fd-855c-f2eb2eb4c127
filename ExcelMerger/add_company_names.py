#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将户号对应的公司名称添加到用电量数据中
"""

import pandas as pd
import numpy as np
import os

class CompanyNameMerger:
    def __init__(self):
        """
        初始化公司名称合并器
        """
        self.company_data = None
        self.main_data = None
        self.company_mapping = {}
    
    def load_company_data(self, company_file_path):
        """
        加载售电签约客户数据
        """
        print("正在加载售电签约客户数据...")
        
        try:
            self.company_data = pd.read_excel(company_file_path)
            print(f"✅ 成功加载公司数据: {len(self.company_data)} 条记录")
            
            # 显示数据概况
            print(f"   公司数量: {self.company_data['公司名称'].nunique()}")
            print(f"   户号数量: {self.company_data['户号'].nunique()}")
            print(f"   地区分布: {dict(self.company_data['地区'].value_counts())}")
            
            # 创建户号到公司名称的映射
            # 处理户号格式，确保一致性
            self.company_data['户号_标准'] = self.company_data['户号'].astype(str).str.replace('.0', '').str.replace('e+12', '').apply(self.standardize_account_number)
            
            # 创建映射字典，如果一个户号对应多个公司，取第一个
            self.company_mapping = {}
            for _, row in self.company_data.iterrows():
                account_num = row['户号_标准']
                if account_num not in self.company_mapping:
                    self.company_mapping[account_num] = {
                        '公司名称': row['公司名称'],
                        '居间/销售': row['居间/销售'],
                        '合同状态': row['2025年合同签约状态'],
                        '签约地区': row['地区']
                    }
            
            print(f"   创建户号映射: {len(self.company_mapping)} 个")
            
            # 显示一些映射示例
            print(f"\n📋 户号-公司映射示例:")
            for i, (account, info) in enumerate(list(self.company_mapping.items())[:5]):
                print(f"   {account} -> {info['公司名称']}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载公司数据失败: {e}")
            return False
    
    def standardize_account_number(self, account_str):
        """
        标准化户号格式
        """
        try:
            # 移除科学计数法，转换为整数字符串
            if 'e+' in str(account_str):
                account_num = int(float(account_str))
            else:
                account_num = int(float(str(account_str).replace('.0', '')))
            return str(account_num)
        except:
            return str(account_str)
    
    def load_main_data(self, main_file_path):
        """
        加载主用电量数据
        """
        print("\n正在加载主用电量数据...")
        
        try:
            self.main_data = pd.read_excel(main_file_path, sheet_name='完整数据')
            print(f"✅ 成功加载主数据: {len(self.main_data)} 条记录")
            
            # 标准化主数据中的户号
            self.main_data['户号_标准'] = self.main_data['户号'].astype(str).str.replace('.0', '').str.replace('e+12', '').apply(self.standardize_account_number)
            
            print(f"   户号数量: {self.main_data['户号'].nunique()}")
            print(f"   地区分布: {dict(self.main_data['地区'].value_counts())}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载主数据失败: {e}")
            return False
    
    def merge_company_names(self):
        """
        合并公司名称到主数据中
        """
        print("\n正在合并公司名称...")
        
        # 添加新列
        self.main_data['公司名称'] = None
        self.main_data['居间/销售'] = None
        self.main_data['合同状态'] = None
        self.main_data['签约地区'] = None
        
        # 合并数据
        matched_count = 0
        unmatched_accounts = set()
        
        for idx, row in self.main_data.iterrows():
            account_std = row['户号_标准']
            
            if account_std in self.company_mapping:
                company_info = self.company_mapping[account_std]
                self.main_data.at[idx, '公司名称'] = company_info['公司名称']
                self.main_data.at[idx, '居间/销售'] = company_info['居间/销售']
                self.main_data.at[idx, '合同状态'] = company_info['合同状态']
                self.main_data.at[idx, '签约地区'] = company_info['签约地区']
                matched_count += 1
            else:
                unmatched_accounts.add(account_std)
        
        print(f"✅ 合并完成:")
        print(f"   匹配成功: {matched_count}/{len(self.main_data)} 条记录 ({matched_count/len(self.main_data)*100:.1f}%)")
        print(f"   未匹配户号: {len(unmatched_accounts)} 个")
        
        if unmatched_accounts and len(unmatched_accounts) <= 10:
            print(f"   未匹配户号示例: {list(unmatched_accounts)[:10]}")
        
        # 显示匹配统计
        matched_companies = self.main_data['公司名称'].nunique()
        print(f"   匹配到公司数: {matched_companies}")
        
        return matched_count > 0
    
    def save_enhanced_data(self, output_file="户号用电量含公司名称数据分表.xlsx"):
        """
        保存增强后的数据
        """
        print(f"\n正在保存增强后的数据...")
        
        try:
            output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 1. 保存完整数据（移除辅助列）
                main_data_clean = self.main_data.drop(columns=['户号_标准'])
                main_data_clean.to_excel(writer, sheet_name='完整数据', index=False)
                print(f"  ✅ 完整数据工作表: {len(main_data_clean)} 条记录，{len(main_data_clean.columns)} 列")
                
                # 2. 按地区分表
                print(f"  正在生成地区分表...")
                for region in main_data_clean['地区'].unique():
                    if pd.notna(region):
                        region_df = main_data_clean[main_data_clean['地区'] == region].copy()
                        sheet_name = f"地区_{region}"
                        region_df.to_excel(writer, sheet_name=sheet_name, index=False)
                        print(f"    {region}: {len(region_df)} 条记录")
                
                # 3. 按公司分表（前30个公司）
                print(f"  正在生成公司分表...")
                company_counts = main_data_clean['公司名称'].value_counts()
                top_companies = company_counts.head(30)
                
                for company_name in top_companies.index:
                    if pd.notna(company_name):
                        company_df = main_data_clean[main_data_clean['公司名称'] == company_name].copy()
                        # 处理公司名称，避免Excel工作表名称问题
                        sheet_name = f"公司_{company_name[:20]}"  # 限制长度
                        try:
                            company_df.to_excel(writer, sheet_name=sheet_name, index=False)
                            print(f"    {company_name}: {len(company_df)} 条记录")
                        except Exception as e:
                            print(f"    ⚠️ {company_name} 保存失败: {e}")
                            continue
                
                # 4. 按户号分表（前20个户号）
                print(f"  正在生成户号分表...")
                household_counts = main_data_clean['户号'].value_counts()
                top_households = household_counts.head(20)
                
                for household_id in top_households.index:
                    household_df = main_data_clean[main_data_clean['户号'] == household_id].copy()
                    sheet_name = f"户号_{str(int(household_id))[-8:]}"
                    try:
                        household_df.to_excel(writer, sheet_name=sheet_name, index=False)
                        print(f"    户号 {str(int(household_id))[-8:]}: {len(household_df)} 条记录")
                    except Exception as e:
                        print(f"    ⚠️ 户号 {str(int(household_id))[-8:]} 保存失败: {e}")
                        continue
                
                # 5. 生成统计摘要
                print(f"  正在生成统计摘要...")
                
                # 公司统计
                company_summary = main_data_clean.groupby('公司名称').agg({
                    '户号': 'nunique',
                    '总电量(kWh)': ['sum', 'mean', 'count'],
                    '地区': 'first',
                    '合同状态': 'first',
                    '居间/销售': 'first'
                }).round(2)
                company_summary.columns = ['户号数量', '总用电量', '平均用电量', '记录数', '地区', '合同状态', '销售人员']
                company_summary = company_summary.reset_index()
                company_summary.to_excel(writer, sheet_name='公司统计', index=False)
                
                # 户号统计（包含公司信息）
                household_summary = main_data_clean.groupby('户号').agg({
                    '总电量(kWh)': ['sum', 'mean', 'count'],
                    '最高温度(°C)': 'mean',
                    '地区': 'first',
                    '公司名称': 'first',
                    '合同状态': 'first'
                }).round(2)
                household_summary.columns = ['总用电量', '平均用电量', '记录天数', '平均最高温度', '地区', '公司名称', '合同状态']
                household_summary = household_summary.reset_index()
                household_summary.to_excel(writer, sheet_name='户号统计', index=False)
                
                # 地区统计
                region_summary = main_data_clean.groupby('地区').agg({
                    '户号': 'nunique',
                    '公司名称': 'nunique',
                    '总电量(kWh)': ['sum', 'mean'],
                    '最高温度(°C)': 'mean'
                }).round(2)
                region_summary.columns = ['户号数量', '公司数量', '总用电量', '平均用电量', '平均最高温度']
                region_summary = region_summary.reset_index()
                region_summary.to_excel(writer, sheet_name='地区统计', index=False)
            
            print(f"✅ 增强数据已保存到: {output_path}")
            
            # 显示统计信息
            print(f"\n📊 保存统计:")
            total_sheets = 4 + len(main_data_clean['地区'].unique()) + min(30, company_counts.nunique()) + min(20, household_counts.nunique())
            print(f"   总工作表数: {total_sheets}")
            print(f"   完整数据: {len(main_data_clean)} 条记录，{len(main_data_clean.columns)} 列")
            print(f"   地区分表: {main_data_clean['地区'].nunique()} 个")
            print(f"   公司分表: {min(30, company_counts.nunique())} 个")
            print(f"   户号分表: {min(20, household_counts.nunique())} 个")
            print(f"   统计表: 3 个")
            
            # 显示公司信息统计
            print(f"\n🏢 公司信息统计:")
            matched_records = main_data_clean['公司名称'].notna().sum()
            print(f"   有公司信息记录: {matched_records}/{len(main_data_clean)} ({matched_records/len(main_data_clean)*100:.1f}%)")
            print(f"   匹配到公司数: {main_data_clean['公司名称'].nunique()}")
            print(f"   记录最多公司: {company_counts.index[0]} ({company_counts.iloc[0]}条)")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False

def main():
    """
    主函数
    """
    print("户号公司名称合并工具")
    print("="*60)
    
    # 文件路径
    company_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/售电签约客户(1).xlsx"
    main_file = "/Users/<USER>/RiderProjects/Solution3/户号用电量含天气数据分表_已删除温度列.xlsx"
    
    # 检查文件存在性
    if not os.path.exists(company_file):
        print(f"❌ 公司数据文件不存在: {company_file}")
        return
    
    if not os.path.exists(main_file):
        print(f"❌ 主数据文件不存在: {main_file}")
        return
    
    merger = CompanyNameMerger()
    
    # 1. 加载公司数据
    if not merger.load_company_data(company_file):
        return
    
    # 2. 加载主数据
    if not merger.load_main_data(main_file):
        return
    
    # 3. 合并公司名称
    if not merger.merge_company_names():
        print("❌ 公司名称合并失败")
        return
    
    # 4. 保存增强后的数据
    success = merger.save_enhanced_data()
    
    if success:
        print(f"\n🎉 处理完成！")
        print(f"📊 已成功将公司名称信息合并到用电量数据中")
        print(f"🏢 新增了公司名称、销售人员、合同状态等信息")
        print(f"📁 输出文件: 户号用电量含公司名称数据分表.xlsx")
        print(f"📋 包含公司分表、地区分表、户号分表和统计表")
    else:
        print(f"\n❌ 处理失败！")

if __name__ == "__main__":
    main()
