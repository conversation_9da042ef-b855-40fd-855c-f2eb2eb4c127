#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版：周天天气数据格式化工具
将整理好的同一周天相同天气数据按指定格式添加到现有Excel文件的各个分表中
包含具体日期和对应用电量

完整格式示例：
日期	        2025-06-23						
日期类型	    星期一	星期二	星期三	星期四	星期五	星期六	星期日
天气	        雨	    雨	    雨	    雨	    雨	    雨	    雨
用电量	        57032.93 45123.45 67890.12 54321.98 43210.87 76543.21 65432.10
具体日期	    2025-06-23 2025-06-24 2025-06-25 2025-06-26 2025-06-27 2025-06-28 2025-06-29
具体用电量	    57032.93 45123.45 67890.12 54321.98 43210.87 76543.21 65432.10
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from collections import defaultdict
from openpyxl import load_workbook

class EnhancedWeekdayWeatherFormatter:
    def __init__(self):
        """
        初始化增强版格式化器
        """
        self.source_data = None
        self.target_workbook = None
        self.grouped_data = {}
        
        # 中文周天映射
        self.weekday_names = {
            0: '星期一',
            1: '星期二', 
            2: '星期三',
            3: '星期四',
            4: '星期五',
            5: '星期六',
            6: '星期日'
        }
    
    def load_and_process_data(self, source_file, target_file):
        """
        加载并处理数据
        """
        try:
            print(f"正在读取源数据文件: {os.path.basename(source_file)}")
            
            # 读取完整数据表
            self.source_data = pd.read_excel(source_file, sheet_name='完整数据')
            print(f"源数据读取成功，共 {len(self.source_data)} 行数据")
            
            # 读取目标文件
            print(f"正在读取目标文件: {os.path.basename(target_file)}")
            self.target_workbook = load_workbook(target_file)
            print(f"发现工作表: {self.target_workbook.sheetnames}")
            
            # 预处理数据
            self.preprocess_data()
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def preprocess_data(self):
        """
        预处理数据，按地区和天气类型分组，保留详细的日期和用电量信息
        """
        print(f"\n=== 预处理数据 ===")
        
        # 按地区分组
        for region in self.source_data['地区'].unique():
            region_data = self.source_data[self.source_data['地区'] == region]
            self.grouped_data[region] = {}
            
            # 按天气类型分组
            for weather in region_data['天气'].unique():
                weather_data = region_data[region_data['天气'] == weather]
                
                # 按周天收集详细数据
                weekday_details = {}
                for _, row in weather_data.iterrows():
                    weekday = row['周天']
                    power = row['总电量(kWh)']
                    date = row['日期']
                    
                    if weekday not in weekday_details:
                        weekday_details[weekday] = []
                    
                    weekday_details[weekday].append({
                        '日期': date,
                        '用电量': power
                    })
                
                # 为每个周天计算汇总数据
                weekday_summary = {}
                for weekday, details in weekday_details.items():
                    # 按日期排序
                    details.sort(key=lambda x: x['日期'])
                    
                    total_power = sum(item['用电量'] for item in details)
                    earliest_date = min(item['日期'] for item in details)
                    
                    weekday_summary[weekday] = {
                        '总用电量': total_power,
                        '代表日期': earliest_date,
                        '详细数据': details
                    }
                
                self.grouped_data[region][weather] = {
                    '周天汇总': weekday_summary,
                    '代表日期': weather_data['日期'].iloc[0]
                }
        
        print(f"数据预处理完成，共处理 {len(self.grouped_data)} 个地区")
        for region, data in self.grouped_data.items():
            print(f"  {region}: {len(data)} 种天气类型")
    
    def add_enhanced_data_to_sheet(self, sheet_name, region_data):
        """
        将增强格式的数据添加到指定工作表
        """
        try:
            if sheet_name not in self.target_workbook.sheetnames:
                print(f"警告: 工作表 {sheet_name} 不存在")
                return False
            
            worksheet = self.target_workbook[sheet_name]
            last_row = worksheet.max_row
            
            print(f"在工作表 {sheet_name} 的第 {last_row + 2} 行开始添加数据")
            
            # 添加标题分隔行
            worksheet.cell(row=last_row + 2, column=1, value="=== 同一周天相同天气数据汇总（含具体日期和用电量）===")
            current_row = last_row + 4
            
            # 为每种天气类型添加数据
            for weather_type, weather_info in region_data.items():
                weekday_summary = weather_info['周天汇总']
                weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
                
                # 1. 日期行（使用代表日期）
                worksheet.cell(row=current_row, column=1, value="日期")
                worksheet.cell(row=current_row, column=2, value=weather_info['代表日期'].strftime('%Y-%m-%d'))
                current_row += 1
                
                # 2. 日期类型行（周天标题）
                worksheet.cell(row=current_row, column=1, value="日期类型")
                for i, weekday in enumerate(weekdays, 2):
                    worksheet.cell(row=current_row, column=i, value=weekday)
                current_row += 1
                
                # 3. 天气行
                worksheet.cell(row=current_row, column=1, value="天气")
                for i in range(2, 9):  # B到H列
                    worksheet.cell(row=current_row, column=i, value=weather_type)
                current_row += 1
                
                # 4. 用电量行（汇总）
                worksheet.cell(row=current_row, column=1, value="用电量")
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weekday_summary:
                        total_power = weekday_summary[weekday]['总用电量']
                        worksheet.cell(row=current_row, column=i, value=round(total_power, 2))
                current_row += 1
                
                # 5. 具体日期行
                worksheet.cell(row=current_row, column=1, value="具体日期")
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weekday_summary:
                        rep_date = weekday_summary[weekday]['代表日期']
                        worksheet.cell(row=current_row, column=i, value=rep_date.strftime('%Y-%m-%d'))
                current_row += 1
                
                # 6. 具体用电量行（与具体日期对应）
                worksheet.cell(row=current_row, column=1, value="具体用电量")
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weekday_summary:
                        total_power = weekday_summary[weekday]['总用电量']
                        worksheet.cell(row=current_row, column=i, value=round(total_power, 2))
                current_row += 1
                
                # 7. 如果有多个日期的数据，添加详细信息
                max_records = max(len(weekday_summary[wd]['详细数据']) for wd in weekday_summary if wd in weekday_summary)
                if max_records > 1:
                    # 添加详细数据标题
                    worksheet.cell(row=current_row, column=1, value="详细数据:")
                    current_row += 1
                    
                    # 为每个记录添加一行
                    for record_idx in range(max_records):
                        worksheet.cell(row=current_row, column=1, value=f"记录{record_idx + 1}")
                        
                        for i, weekday in enumerate(weekdays, 2):
                            if weekday in weekday_summary:
                                details = weekday_summary[weekday]['详细数据']
                                if record_idx < len(details):
                                    date_str = details[record_idx]['日期'].strftime('%Y-%m-%d')
                                    power_val = round(details[record_idx]['用电量'], 2)
                                    worksheet.cell(row=current_row, column=i, value=f"{date_str}({power_val})")
                        current_row += 1
                
                current_row += 1  # 空一行分隔不同天气类型
            
            print(f"成功添加 {len(region_data)} 种天气类型的增强数据到工作表 {sheet_name}")
            return True
            
        except Exception as e:
            print(f"添加数据到工作表 {sheet_name} 失败: {e}")
            return False
    
    def process_all_regions(self):
        """
        处理所有地区的数据
        """
        regions = ['杭州', '金华', '宁波', '台州', '衢州']
        
        for region in regions:
            print(f"\n处理地区: {region}")
            
            if region in self.grouped_data:
                region_data = self.grouped_data[region]
                self.add_enhanced_data_to_sheet(region, region_data)
            else:
                print(f"警告: 未找到地区 {region} 的数据")
    
    def save_result(self, output_file):
        """
        保存结果文件
        """
        try:
            self.target_workbook.save(output_file)
            print(f"\n✅ 处理完成！结果已保存到: {output_file}")
            return True
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False
    
    def process(self, source_file, target_file, output_file):
        """
        完整的处理流程
        """
        print(f"增强版：周天天气数据格式化工具")
        print(f"=" * 80)
        print(f"源数据文件: {source_file}")
        print(f"目标文件: {target_file}")
        print(f"输出文件: {output_file}")
        
        # 执行处理步骤
        if not self.load_and_process_data(source_file, target_file):
            return False
        
        self.process_all_regions()
        
        if not self.save_result(output_file):
            return False
        
        return True

def main():
    """主函数"""
    # 源数据文件（之前生成的周天天气分组文件）
    source_file = "城市售电表（含气温+日期类型+天气）_周天天气分组.xlsx"
    
    # 目标文件
    target_file = "/Users/<USER>/Desktop/副本城市售电表（含气温+日期类型+天气）(1).xlsx"
    
    # 输出文件
    output_file = "增强版_副本城市售电表_含详细周天天气汇总.xlsx"
    
    if not os.path.exists(source_file):
        print(f"错误: 源数据文件不存在 - {source_file}")
        print("请先运行 weekday_weather_grouper.py 生成源数据文件")
        return
    
    if not os.path.exists(target_file):
        print(f"错误: 目标文件不存在 - {target_file}")
        return
    
    formatter = EnhancedWeekdayWeatherFormatter()
    success = formatter.process(source_file, target_file, output_file)
    
    if success:
        print(f"\n🎉 任务完成！")
        print(f"已成功将同一周天相同天气的数据（含具体日期和用电量）按指定格式添加到各个地区分表中")
        print(f"文件保存位置: {output_file}")
        
        # 显示详细数据汇总
        print(f"\n📊 详细数据汇总:")
        for region, data in formatter.grouped_data.items():
            print(f"\n{region}:")
            for weather, info in data.items():
                weekday_summary = info['周天汇总']
                total_power = sum(ws['总用电量'] for ws in weekday_summary.values())
                print(f"  {weather}: {len(weekday_summary)} 个周天, 总用电量 {total_power:.2f} kWh")
                
                for weekday, ws_info in weekday_summary.items():
                    detail_count = len(ws_info['详细数据'])
                    print(f"    {weekday}: {ws_info['总用电量']:.2f} kWh ({detail_count} 条记录)")

if __name__ == "__main__":
    main()
