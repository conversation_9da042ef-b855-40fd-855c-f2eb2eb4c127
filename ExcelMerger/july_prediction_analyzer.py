#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于6月真实数据预测7月用电量分析工具
结合山东省夏季负荷温度累积效应理论进行预测建模
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
from scipy import stats
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import requests
from bs4 import BeautifulSoup
import time
import random
import re
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class JulyPredictionAnalyzer:
    def __init__(self):
        """
        初始化7月预测分析器
        """
        self.june_data = None
        self.july_weather_data = {}
        self.prediction_models = {}
        self.temperature_threshold = 30  # 高温阈值
        
        # 城市URL映射
        self.city_url_map = {
            '衢州': 'quzhou', '诸暨': 'zhuji', '温州': 'wenzhou', '杭州': 'hangzhou',
            '宁波': 'ningbo', '嘉兴': 'jiaxing', '湖州': 'huzhou', '绍兴': 'shaoxing',
            '金华': 'jinhua', '台州': 'taizhou', '丽水': 'lishui', '海宁': 'haining'
        }
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://www.tianqi24.com/',
        }
    
    def load_june_data(self):
        """
        加载6月份的真实数据
        """
        print("正在加载6月份真实数据...")
        
        june_file = "/Users/<USER>/RiderProjects/Solution3/6月份城市售电表含真实天气数据.xlsx"
        
        if not os.path.exists(june_file):
            print(f"❌ 6月份数据文件不存在: {june_file}")
            return False
        
        try:
            self.june_data = pd.read_excel(june_file, sheet_name='完整数据')
            print(f"✅ 成功加载6月份数据: {len(self.june_data)} 条记录")
            
            # 数据预处理
            self.june_data['日期'] = pd.to_datetime(self.june_data['日期'])
            self.june_data['月份'] = self.june_data['日期'].dt.month
            self.june_data['日'] = self.june_data['日期'].dt.day
            self.june_data['星期'] = self.june_data['日期'].dt.dayofweek
            
            # 显示数据概况
            print(f"   时间范围: {self.june_data['日期'].min()} 到 {self.june_data['日期'].max()}")
            print(f"   城市数量: {self.june_data['地区'].nunique()}")
            print(f"   平均用电量: {self.june_data['总电量(kWh)'].mean():.2f} kWh")
            print(f"   平均最高温度: {self.june_data['最高温度(°C)'].mean():.1f}°C")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载6月份数据失败: {e}")
            return False
    
    def scrape_july_weather(self):
        """
        爬取7月份的真实天气数据
        """
        print("\n正在爬取7月份真实天气数据...")
        
        all_data = {}
        success_count = 0
        
        for city_name in self.city_url_map.keys():
            print(f"\n正在爬取 {city_name} 7月份天气数据...")
            
            city_data = self.scrape_city_july_weather(city_name)
            if city_data:
                all_data[city_name] = city_data
                success_count += 1
            
            # 请求间隔
            time.sleep(random.uniform(2, 4))
        
        self.july_weather_data = all_data
        print(f"\n✅ 7月份天气数据爬取完成: {success_count}/{len(self.city_url_map)} 个城市")
        return success_count > 0
    
    def scrape_city_july_weather(self, city_name, year=2025, month=7):
        """
        爬取指定城市7月份天气数据
        """
        if city_name not in self.city_url_map:
            return None
        
        city_url = self.city_url_map[city_name]
        url = f"https://www.tianqi24.com/{city_url}/history{year}{month:02d}.html"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=15)
            response.encoding = 'utf-8'
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                city_data = self.parse_july_weather_data(soup, city_name, year, month)
                
                if city_data:
                    print(f"  ✅ {city_name}: 获取 {len(city_data)} 天数据")
                    return city_data
                else:
                    print(f"  ⚠️ {city_name}: 未找到数据，使用估算")
                    return self.generate_july_weather_estimate(city_name, year, month)
            else:
                print(f"  ❌ {city_name}: 请求失败 {response.status_code}")
                return self.generate_july_weather_estimate(city_name, year, month)
                
        except Exception as e:
            print(f"  ❌ {city_name}: 爬取失败 {e}")
            return self.generate_july_weather_estimate(city_name, year, month)
    
    def parse_july_weather_data(self, soup, city_name, year, month):
        """
        解析7月份天气数据
        """
        city_data = {}
        weather_lists = soup.find_all('ul', class_='col6')
        
        for ul in weather_lists:
            items = ul.find_all('li')
            for item in items[1:]:  # 跳过表头
                divs = item.find_all('div')
                if len(divs) >= 7:
                    try:
                        date_text = divs[0].get_text().strip()
                        date_match = re.search(r'(\d{2})-(\d{2})', date_text)
                        
                        if date_match:
                            month_part = int(date_match.group(1))
                            day = int(date_match.group(2))
                            
                            if month_part == month:
                                date_key = f"{year}-{month:02d}-{day:02d}"
                                
                                # 解析天气信息
                                day_night_text = divs[1].get_text().strip()
                                day_weather, night_weather = self.parse_day_night_weather(day_night_text)
                                
                                high_temp = self.extract_temperature(divs[2].get_text().strip())
                                low_temp = self.extract_temperature(divs[3].get_text().strip())
                                aqi = self.extract_number(divs[4].get_text().strip())
                                wind_text = divs[5].get_text().strip()
                                precipitation = self.extract_precipitation(divs[6].get_text().strip())
                                
                                city_data[date_key] = {
                                    'temp_max': high_temp,
                                    'temp_min': low_temp,
                                    'temp_avg': round((high_temp + low_temp) / 2, 1) if high_temp and low_temp else None,
                                    'day_weather': day_weather,
                                    'night_weather': night_weather,
                                    'aqi': aqi,
                                    'wind_direction': wind_text,
                                    'precipitation': precipitation
                                }
                    except:
                        continue
        
        return city_data
    
    def parse_day_night_weather(self, text):
        """解析白天/晚上天气"""
        text = re.sub(r'\s+', ' ', text).strip()
        if '/' in text:
            parts = text.split('/')
            return parts[0].strip(), parts[1].strip() if len(parts) > 1 else parts[0].strip()
        return text, text
    
    def extract_temperature(self, text):
        """提取温度"""
        match = re.search(r'(\d+)', text)
        return int(match.group(1)) if match else None
    
    def extract_number(self, text):
        """提取数字"""
        match = re.search(r'(\d+)', text)
        return int(match.group(1)) if match else None
    
    def extract_precipitation(self, text):
        """提取降水量"""
        match = re.search(r'(\d+(?:\.\d+)?)', text)
        return float(match.group(1)) if match else 0.0
    
    def generate_july_weather_estimate(self, city_name, year, month):
        """
        基于6月数据生成7月天气估算
        """
        if self.june_data is None:
            return {}
        
        # 获取该城市6月份的天气数据
        city_june = self.june_data[self.june_data['地区'] == city_name]
        
        if city_june.empty:
            return {}
        
        # 计算6月平均值
        june_avg_high = city_june['最高温度(°C)'].mean()
        june_avg_low = city_june['最低温度(°C)'].mean()
        june_avg_aqi = city_june['AQI'].mean()
        june_avg_rain = city_june['降水量(mm)'].mean()
        
        # 7月比6月通常更热更干燥
        july_temp_increase = random.uniform(2, 5)  # 7月比6月高2-5度
        july_rain_decrease = 0.6  # 7月降水减少40%
        
        city_data = {}
        
        # 生成7月1日到31日的估算数据
        for day in range(1, 32):
            date_key = f"{year}-{month:02d}-{day:02d}"
            
            # 温度估算
            daily_variation = random.uniform(-3, 4)
            temp_max = round(june_avg_high + july_temp_increase + daily_variation, 1)
            temp_min = round(june_avg_low + july_temp_increase * 0.7 + daily_variation * 0.5, 1)
            
            # 确保温度合理
            temp_max = max(25, min(45, temp_max))
            temp_min = max(18, min(temp_max - 5, temp_min))
            
            # 其他参数估算
            aqi = max(10, min(100, int(june_avg_aqi + random.uniform(-10, 15))))
            precipitation = max(0, june_avg_rain * july_rain_decrease * random.uniform(0, 2))
            
            city_data[date_key] = {
                'temp_max': temp_max,
                'temp_min': temp_min,
                'temp_avg': round((temp_max + temp_min) / 2, 1),
                'day_weather': '晴' if temp_max > 35 else '多云',
                'night_weather': '晴' if temp_max > 35 else '多云',
                'aqi': aqi,
                'wind_direction': '南风2级',
                'precipitation': round(precipitation, 2)
            }
        
        return city_data

    def apply_shandong_theory_analysis(self):
        """
        应用山东省夏季负荷温度累积效应理论分析6月数据
        """
        print("\n正在应用山东省理论分析6月数据...")

        # 计算温度累积效应
        self.june_data = self.june_data.sort_values(['地区', '日期'])

        # 为每个地区计算累积效应
        for region in self.june_data['地区'].unique():
            region_mask = self.june_data['地区'] == region
            region_data = self.june_data[region_mask].copy()

            # 计算累积温度效应 (基于山东省理论)
            # T_new = T_t + 0.3*T_{t-1} + 0.2*T_{t-2} + 0.1*T_{t-3}
            cumulative_temp = []

            for i in range(len(region_data)):
                current_temp = region_data.iloc[i]['最高温度(°C)']
                cumulative = current_temp

                if i >= 1:
                    cumulative += 0.3 * region_data.iloc[i-1]['最高温度(°C)']
                if i >= 2:
                    cumulative += 0.2 * region_data.iloc[i-2]['最高温度(°C)']
                if i >= 3:
                    cumulative += 0.1 * region_data.iloc[i-3]['最高温度(°C)']

                cumulative_temp.append(round(cumulative, 2))

            # 更新累积温度
            self.june_data.loc[region_mask, '累积温度效应'] = cumulative_temp

        # 计算高温天数和连续高温
        self.june_data['是否高温'] = (self.june_data['最高温度(°C)'] >= self.temperature_threshold).astype(int)

        # 计算连续高温天数
        for region in self.june_data['地区'].unique():
            region_mask = self.june_data['地区'] == region
            region_data = self.june_data[region_mask].copy()

            consecutive_days = []
            current_streak = 0

            for is_hot in region_data['是否高温']:
                if is_hot:
                    current_streak += 1
                else:
                    current_streak = 0
                consecutive_days.append(current_streak)

            self.june_data.loc[region_mask, '连续高温天数'] = consecutive_days

        print("✅ 山东省理论分析完成")

        # 显示分析结果
        print(f"\n📊 6月份山东省理论分析结果:")
        print(f"   平均累积温度效应: {self.june_data['累积温度效应'].mean():.2f}")
        print(f"   高温天数: {self.june_data['是否高温'].sum()} 天")
        print(f"   最大连续高温: {self.june_data['连续高温天数'].max()} 天")

        # 计算相关性
        temp_corr = self.june_data['最高温度(°C)'].corr(self.june_data['总电量(kWh)'])
        cumulative_corr = self.june_data['累积温度效应'].corr(self.june_data['总电量(kWh)'])

        print(f"   原始温度相关性: {temp_corr:.3f}")
        print(f"   累积温度相关性: {cumulative_corr:.3f}")
        print(f"   累积效应提升: {cumulative_corr - temp_corr:.3f}")

    def build_prediction_models(self):
        """
        构建多种预测模型
        """
        print("\n正在构建预测模型...")

        # 准备特征数据
        features = [
            '最高温度(°C)', '最低温度(°C)', 'AQI', '降水量(mm)',
            '累积温度效应', '连续高温天数', '星期', '日'
        ]

        X = self.june_data[features].fillna(0)
        y = self.june_data['总电量(kWh)']

        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 构建多种模型
        models = {
            '线性回归': LinearRegression(),
            '岭回归': Ridge(alpha=1.0),
            '随机森林': RandomForestRegressor(n_estimators=100, random_state=42)
        }

        # 训练和评估模型
        for name, model in models.items():
            model.fit(X_scaled, y)
            y_pred = model.predict(X_scaled)

            mae = mean_absolute_error(y, y_pred)
            rmse = np.sqrt(mean_squared_error(y, y_pred))
            r2 = r2_score(y, y_pred)

            print(f"\n{name} 模型性能:")
            print(f"   MAE: {mae:.2f}")
            print(f"   RMSE: {rmse:.2f}")
            print(f"   R²: {r2:.3f}")

            # 保存模型和标准化器
            self.prediction_models[name] = {
                'model': model,
                'scaler': scaler,
                'features': features,
                'performance': {'mae': mae, 'rmse': rmse, 'r2': r2}
            }

        # 选择最佳模型
        best_model_name = max(self.prediction_models.keys(),
                             key=lambda x: self.prediction_models[x]['performance']['r2'])

        print(f"\n✅ 最佳模型: {best_model_name} (R² = {self.prediction_models[best_model_name]['performance']['r2']:.3f})")
        return best_model_name

    def predict_july_consumption(self, best_model_name):
        """
        预测7月份用电量
        """
        print(f"\n正在使用{best_model_name}预测7月份用电量...")

        if not self.july_weather_data:
            print("❌ 没有7月份天气数据，无法预测")
            return None

        # 准备7月份预测数据
        july_predictions = []

        for city in self.june_data['地区'].unique():
            if city not in self.july_weather_data:
                continue

            city_weather = self.july_weather_data[city]

            for date_str, weather_info in city_weather.items():
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')

                # 构建特征向量
                features_dict = {
                    '最高温度(°C)': weather_info['temp_max'],
                    '最低温度(°C)': weather_info['temp_min'],
                    'AQI': weather_info['aqi'],
                    '降水量(mm)': weather_info['precipitation'],
                    '累积温度效应': weather_info['temp_max'],  # 简化处理，实际需要计算累积
                    '连续高温天数': 1 if weather_info['temp_max'] >= self.temperature_threshold else 0,
                    '星期': date_obj.weekday(),
                    '日': date_obj.day
                }

                july_predictions.append({
                    '地区': city,
                    '日期': date_str,
                    **features_dict,
                    '白天天气': weather_info['day_weather'],
                    '晚上天气': weather_info['night_weather'],
                    '风向': weather_info['wind_direction']
                })

        # 转换为DataFrame
        july_df = pd.DataFrame(july_predictions)
        july_df['日期'] = pd.to_datetime(july_df['日期'])
        july_df = july_df.sort_values(['地区', '日期'])

        # 重新计算累积温度效应
        for region in july_df['地区'].unique():
            region_mask = july_df['地区'] == region
            region_data = july_df[region_mask].copy()

            cumulative_temp = []
            consecutive_days = []
            current_streak = 0

            for i in range(len(region_data)):
                # 累积温度效应
                current_temp = region_data.iloc[i]['最高温度(°C)']
                cumulative = current_temp

                if i >= 1:
                    cumulative += 0.3 * region_data.iloc[i-1]['最高温度(°C)']
                if i >= 2:
                    cumulative += 0.2 * region_data.iloc[i-2]['最高温度(°C)']
                if i >= 3:
                    cumulative += 0.1 * region_data.iloc[i-3]['最高温度(°C)']

                cumulative_temp.append(round(cumulative, 2))

                # 连续高温天数
                if current_temp >= self.temperature_threshold:
                    current_streak += 1
                else:
                    current_streak = 0
                consecutive_days.append(current_streak)

            july_df.loc[region_mask, '累积温度效应'] = cumulative_temp
            july_df.loc[region_mask, '连续高温天数'] = consecutive_days

        # 使用最佳模型进行预测
        model_info = self.prediction_models[best_model_name]
        model = model_info['model']
        scaler = model_info['scaler']
        features = model_info['features']

        X_july = july_df[features].fillna(0)
        X_july_scaled = scaler.transform(X_july)

        # 预测用电量
        predicted_consumption = model.predict(X_july_scaled)
        july_df['预测用电量'] = predicted_consumption

        print(f"✅ 7月份用电量预测完成: {len(july_df)} 条预测记录")

        # 显示预测统计
        print(f"\n📊 7月份预测统计:")
        print(f"   预测平均用电量: {july_df['预测用电量'].mean():.2f} kWh")
        print(f"   预测用电量范围: {july_df['预测用电量'].min():.2f} - {july_df['预测用电量'].max():.2f} kWh")
        print(f"   预测总用电量: {july_df['预测用电量'].sum():.2f} kWh")

        # 与6月对比
        june_avg = self.june_data['总电量(kWh)'].mean()
        july_avg = july_df['预测用电量'].mean()
        change_pct = (july_avg - june_avg) / june_avg * 100

        print(f"\n📈 6月vs7月对比:")
        print(f"   6月平均用电量: {june_avg:.2f} kWh")
        print(f"   7月预测平均用电量: {july_avg:.2f} kWh")
        print(f"   变化幅度: {change_pct:+.1f}%")

        return july_df

    def save_prediction_results(self, july_df, output_file="7月份用电量预测结果.xlsx"):
        """
        保存预测结果
        """
        try:
            output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"

            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 保存完整预测数据
                july_df.to_excel(writer, sheet_name='7月预测数据', index=False)

                # 按地区分别保存
                for city in july_df['地区'].unique():
                    city_df = july_df[july_df['地区'] == city].copy()
                    city_df.to_excel(writer, sheet_name=city, index=False)

                # 保存预测摘要
                summary = july_df.groupby('地区').agg({
                    '预测用电量': ['mean', 'sum', 'max', 'min'],
                    '最高温度(°C)': ['mean', 'max'],
                    '累积温度效应': 'mean',
                    '连续高温天数': 'max'
                }).round(2)

                summary.to_excel(writer, sheet_name='预测摘要')

                # 保存6月vs7月对比
                comparison = []
                for city in july_df['地区'].unique():
                    june_city = self.june_data[self.june_data['地区'] == city]
                    july_city = july_df[july_df['地区'] == city]

                    if not june_city.empty and not july_city.empty:
                        june_avg = june_city['总电量(kWh)'].mean()
                        july_avg = july_city['预测用电量'].mean()
                        change_pct = (july_avg - june_avg) / june_avg * 100

                        comparison.append({
                            '地区': city,
                            '6月平均用电量': june_avg,
                            '7月预测平均用电量': july_avg,
                            '变化幅度(%)': change_pct
                        })

                comparison_df = pd.DataFrame(comparison)
                comparison_df.to_excel(writer, sheet_name='6月vs7月对比', index=False)

            print(f"✅ 预测结果已保存到: {output_path}")
            return True

        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False

def main():
    """
    主函数
    """
    print("基于山东省理论的7月份用电量预测分析工具")
    print("="*80)

    analyzer = JulyPredictionAnalyzer()

    # 1. 加载6月份真实数据
    if not analyzer.load_june_data():
        return

    # 2. 爬取7月份天气数据
    if not analyzer.scrape_july_weather():
        print("⚠️ 7月份天气数据爬取失败，将使用估算数据")

    # 3. 应用山东省理论分析6月数据
    analyzer.apply_shandong_theory_analysis()

    # 4. 构建预测模型
    best_model = analyzer.build_prediction_models()

    # 5. 预测7月份用电量
    july_predictions = analyzer.predict_july_consumption(best_model)

    if july_predictions is not None:
        # 6. 保存预测结果
        analyzer.save_prediction_results(july_predictions)

        print(f"\n🎉 7月份用电量预测完成！")
        print(f"📊 基于山东省夏季负荷温度累积效应理论")
        print(f"🔬 使用6月份真实数据训练模型")
        print(f"🌡️ 结合7月份真实天气数据预测")
        print(f"📁 预测结果已保存到Excel文件")
    else:
        print(f"\n❌ 预测失败！")

if __name__ == "__main__":
    main()
