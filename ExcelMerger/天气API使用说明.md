# 天气API使用说明

## 🌤️ 功能介绍

天气信息增强工具可以为您的Excel文件中的地区数据添加实时天气信息，包括：

- 🌡️ **气温** - 当前温度和体感温度
- 💧 **湿度** - 空气湿度百分比
- ☁️ **天气状况** - 晴、多云、雨等天气描述
- 💨 **风速风向** - 风速和风向角度
- 📊 **气压** - 大气压力
- 👁️ **能见度** - 可视距离
- ☀️ **日出日落** - 日出和日落时间

## 🔑 获取免费API密钥

### 步骤1：注册OpenWeatherMap账户
1. 访问 [OpenWeatherMap官网](https://openweathermap.org/api)
2. 点击 "Sign Up" 注册新账户
3. 填写邮箱、用户名和密码
4. 验证邮箱地址

### 步骤2：获取API密钥
1. 登录后进入 [API Keys页面](https://home.openweathermap.org/api_keys)
2. 复制默认的API密钥（或创建新的）
3. API密钥格式类似：`a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6`

### 步骤3：API激活
- 新注册的API密钥需要等待2小时才能激活
- 激活后即可使用

## 📊 免费版本限制

OpenWeatherMap免费版本提供：
- ✅ **每分钟60次请求**
- ✅ **每月100万次请求**
- ✅ **当前天气数据**
- ✅ **5天/3小时预报**
- ✅ **历史天气数据（1天）**

对于我们的用途完全足够！

## 🚀 使用方法

### 方法1：使用真实天气数据
```bash
cd ExcelMerger
source venv/bin/activate
python3 weather_enhancer_with_api.py
# 输入您的API密钥
```

### 方法2：使用模拟天气数据
```bash
cd ExcelMerger
source venv/bin/activate
python3 weather_enhancer.py
# 无需API密钥，使用预设的模拟数据
```

## 📍 支持的地区

程序已预配置以下浙江省地区的精确坐标：

| 地区 | 坐标 | 地区 | 坐标 |
|------|------|------|------|
| 衢州 | 28.97°N, 118.87°E | 杭州 | 30.27°N, 120.16°E |
| 诸暨 | 29.71°N, 120.23°E | 宁波 | 29.87°N, 121.54°E |
| 温州 | 28.00°N, 120.67°E | 嘉兴 | 30.75°N, 120.75°E |
| 湖州 | 30.87°N, 120.09°E | 绍兴 | 30.00°N, 120.58°E |
| 金华 | 29.10°N, 119.65°E | 台州 | 28.61°N, 121.42°E |
| 丽水 | 28.45°N, 119.92°E | | |

## 📋 输出数据格式

程序会在原Excel文件基础上添加以下列：

| 列名 | 说明 | 单位 |
|------|------|------|
| 气温(°C) | 当前温度 | 摄氏度 |
| 体感温度(°C) | 体感温度 | 摄氏度 |
| 湿度(%) | 相对湿度 | 百分比 |
| 天气状况 | 天气描述 | 中文 |
| 天气类型 | 天气分类 | 英文 |
| 风速(m/s) | 风速 | 米/秒 |
| 风向(度) | 风向角度 | 度 |
| 气压(hPa) | 大气压力 | 百帕 |
| 能见度(km) | 可视距离 | 公里 |
| 云量(%) | 云覆盖率 | 百分比 |
| 日出时间 | 日出时间 | HH:MM |
| 日落时间 | 日落时间 | HH:MM |

## ⚠️ 注意事项

1. **API请求频率**：程序会在请求间添加1秒延迟，避免超出API限制
2. **网络连接**：需要稳定的网络连接访问API
3. **数据时效性**：天气数据为获取时的实时数据
4. **地区匹配**：确保Excel文件中的地区名称与支持列表匹配

## 🔧 故障排除

### 问题1：API密钥无效
- 检查API密钥是否正确复制
- 确认API密钥已激活（新密钥需等待2小时）

### 问题2：网络连接失败
- 检查网络连接
- 尝试使用VPN或更换网络

### 问题3：地区未找到
- 检查地区名称是否在支持列表中
- 可以联系我们添加新的地区支持

## 📞 技术支持

如有问题，请检查：
1. Python环境是否正确安装
2. 依赖包是否完整安装
3. 输入文件路径是否正确
4. API密钥是否有效

## 🎯 示例输出

处理后的Excel文件将包含如下数据：

```
户号          地区    气温(°C)  湿度(%)  天气状况  风速(m/s)
3301321246986  衢州    28.5     65      多云      3.2
3301762086265  诸暨    29.1     68      晴        2.8
3301840413016  温州    30.2     72      小雨      4.1
```

这样您就可以分析用电量与天气条件的关系了！
