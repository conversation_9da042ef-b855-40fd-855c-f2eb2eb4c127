#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终天气数据合并脚本
专门用于合并指定的两个Excel文件
"""

import pandas as pd
import os
from datetime import datetime, timedelta

def main():
    """主函数"""
    print("天气数据合并工具 - 最终版本")
    print("=" * 50)
    
    # 指定的文件路径
    usage_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/合并结果_6月用电量信息含地区.xlsx"
    weather_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/6月份全部地方天气.xlsx"
    output_file = "/Users/<USER>/RiderProjects/Solution3/最终合并结果_6月用电量含完整天气信息.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(usage_file):
        print(f"错误: 用电量文件不存在")
        print(f"路径: {usage_file}")
        return False
    
    if not os.path.exists(weather_file):
        print(f"错误: 天气文件不存在")
        print(f"路径: {weather_file}")
        return False
    
    try:
        # 读取用电量文件
        print(f"正在读取用电量文件...")
        usage_df = pd.read_excel(usage_file)
        print(f"用电量文件读取成功，共 {len(usage_df)} 行数据")
        
        # 读取天气文件
        print(f"正在读取天气文件...")
        weather_df = pd.read_excel(weather_file)
        print(f"天气文件读取成功，共 {len(weather_df)} 行数据")
        
        # 预处理数据
        print(f"正在预处理数据...")
        
        # 标准化用电量数据的日期格式
        def normalize_date(date_value):
            if pd.isna(date_value):
                return None
            try:
                if isinstance(date_value, datetime):
                    return date_value.strftime('%Y-%m-%d')
                else:
                    parsed_date = pd.to_datetime(date_value)
                    return parsed_date.strftime('%Y-%m-%d')
            except:
                return None

        usage_df['标准化日期'] = usage_df['时间'].apply(normalize_date)
        weather_df['标准化日期'] = weather_df['日期'].apply(normalize_date)
        
        # 创建天气映射字典
        print(f"正在创建天气映射...")
        weather_mapping = {}
        for _, row in weather_df.iterrows():
            key = (row['城市'], row['标准化日期'])
            weather_mapping[key] = row['天气情况']
        
        print(f"天气映射创建完成，共 {len(weather_mapping)} 个条目")
        
        # 合并天气数据
        print(f"正在合并天气数据...")
        def get_weather(row):
            if pd.isna(row['地区']):
                return None
            key = (row['地区'], row['标准化日期'])
            return weather_mapping.get(key, None)
        
        usage_df['天气信息'] = usage_df.apply(get_weather, axis=1)
        
        # 处理缺失的天气数据
        print(f"正在处理缺失的天气数据...")
        unmatched_count = usage_df['天气信息'].isna().sum()
        if unmatched_count > 0:
            print(f"发现 {unmatched_count} 条记录缺少天气数据，正在尝试填充...")
            
            # 对于诸暨6月1日的特殊情况，使用6月2日的天气
            zhuji_june_1_mask = (usage_df['地区'] == '诸暨') & (usage_df['标准化日期'] == '2025-06-01')
            if zhuji_june_1_mask.any():
                # 查找诸暨6月2日的天气
                zhuji_june_2_weather = weather_mapping.get(('诸暨', '2025-06-02'))
                if zhuji_june_2_weather:
                    usage_df.loc[zhuji_june_1_mask, '天气信息'] = zhuji_june_2_weather
                    print(f"为诸暨6月1日填充天气数据: {zhuji_june_2_weather}")
        
        # 统计最终结果
        final_matched = usage_df['天气信息'].notna().sum()
        final_unmatched = len(usage_df) - final_matched
        
        print(f"\n最终合并结果:")
        print(f"总记录数: {len(usage_df)}")
        print(f"成功匹配天气: {final_matched}")
        print(f"未匹配天气: {final_unmatched}")
        print(f"天气匹配率: {final_matched/len(usage_df)*100:.2f}%")
        
        # 清理数据并保存
        print(f"正在保存结果...")
        
        # 删除辅助列
        result_df = usage_df.drop(columns=['标准化日期'])
        
        # 重新排列列的顺序
        cols = list(result_df.columns)
        if '天气信息' in cols:
            cols.remove('天气信息')
            # 在地区列后面插入天气信息列
            if '地区' in cols:
                region_index = cols.index('地区')
                cols.insert(region_index + 1, '天气信息')
            else:
                cols.append('天气信息')
        
        result_df = result_df[cols]
        
        # 保存到Excel文件
        result_df.to_excel(output_file, index=False)
        print(f"结果已保存到: {output_file}")
        
        # 显示预览
        print(f"\n数据预览 (前10行):")
        preview_cols = ['地区', '时间', '天气信息', '总电量(kWh)']
        available_cols = [col for col in preview_cols if col in result_df.columns]
        print(result_df[available_cols].head(10))
        
        # 显示天气数据统计
        print(f"\n天气数据统计:")
        weather_stats = result_df['天气信息'].value_counts()
        print(weather_stats)
        
        print(f"\n处理完成！")
        return True
        
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    main()
