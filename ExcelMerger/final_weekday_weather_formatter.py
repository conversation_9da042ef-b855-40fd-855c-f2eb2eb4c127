#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版本：周天天气数据格式化工具
将整理好的同一周天相同天气数据按指定格式添加到现有Excel文件的各个分表中

格式示例：
日期	    2025-06-23						
日期类型	星期一	星期二	星期三	星期四	星期五	星期六	星期日
天气	    雨	    雨	    雨	    雨	    雨	    雨	    雨
用电量	    57032.93
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from collections import defaultdict
from openpyxl import load_workbook

class FinalWeekdayWeatherFormatter:
    def __init__(self):
        """
        初始化格式化器
        """
        self.source_data = None
        self.target_workbook = None
        self.grouped_data = {}
        
        # 中文周天映射
        self.weekday_names = {
            0: '星期一',
            1: '星期二', 
            2: '星期三',
            3: '星期四',
            4: '星期五',
            5: '星期六',
            6: '星期日'
        }
    
    def load_and_process_data(self, source_file, target_file):
        """
        加载并处理数据
        """
        try:
            print(f"正在读取源数据文件: {os.path.basename(source_file)}")
            
            # 读取完整数据表
            self.source_data = pd.read_excel(source_file, sheet_name='完整数据')
            print(f"源数据读取成功，共 {len(self.source_data)} 行数据")
            
            # 读取目标文件
            print(f"正在读取目标文件: {os.path.basename(target_file)}")
            self.target_workbook = load_workbook(target_file)
            print(f"发现工作表: {self.target_workbook.sheetnames}")
            
            # 预处理数据
            self.preprocess_data()
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def preprocess_data(self):
        """
        预处理数据，按地区和天气类型分组
        """
        print(f"\n=== 预处理数据 ===")

        # 按地区分组
        for region in self.source_data['地区'].unique():
            region_data = self.source_data[self.source_data['地区'] == region]
            self.grouped_data[region] = {}

            # 按天气类型分组
            for weather in region_data['天气'].unique():
                weather_data = region_data[region_data['天气'] == weather]

                # 按周天统计用电量和日期
                weekday_data = {}
                for _, row in weather_data.iterrows():
                    weekday = row['周天']
                    power = row['总电量(kWh)']
                    date = row['日期']

                    if weekday not in weekday_data:
                        weekday_data[weekday] = {
                            '用电量列表': [],
                            '日期列表': []
                        }
                    weekday_data[weekday]['用电量列表'].append(power)
                    weekday_data[weekday]['日期列表'].append(date)

                # 计算每个周天的总用电量和对应日期
                weekday_totals = {}
                weekday_dates = {}
                for weekday, data in weekday_data.items():
                    weekday_totals[weekday] = sum(data['用电量列表'])
                    # 使用最早的日期作为代表日期
                    weekday_dates[weekday] = min(data['日期列表'])

                self.grouped_data[region][weather] = {
                    '周天用电量': weekday_totals,
                    '周天日期': weekday_dates,
                    '代表日期': weather_data['日期'].iloc[0]
                }

        print(f"数据预处理完成，共处理 {len(self.grouped_data)} 个地区")
        for region, data in self.grouped_data.items():
            print(f"  {region}: {len(data)} 种天气类型")
    
    def add_formatted_data_to_sheet(self, sheet_name, region_data):
        """
        将格式化数据添加到指定工作表
        格式：
        日期	    2025-06-23
        日期类型	星期一	星期二	星期三	星期四	星期五	星期六	星期日
        天气	    雨	    雨	    雨	    雨	    雨	    雨	    雨
        用电量	    57032.93
        具体日期    2025-06-23  2025-06-24  2025-06-25  2025-06-26  2025-06-27  2025-06-28  2025-06-29
        具体用电量  12345.67    23456.78    34567.89    45678.90    56789.01    67890.12    78901.23
        """
        try:
            if sheet_name not in self.target_workbook.sheetnames:
                print(f"警告: 工作表 {sheet_name} 不存在")
                return False

            worksheet = self.target_workbook[sheet_name]
            last_row = worksheet.max_row

            print(f"在工作表 {sheet_name} 的第 {last_row + 2} 行开始添加数据")

            # 添加标题分隔行
            worksheet.cell(row=last_row + 2, column=1, value="=== 同一周天相同天气数据汇总 ===")
            current_row = last_row + 4

            # 为每种天气类型添加数据
            for weather_type, weather_info in region_data.items():
                # 日期行（代表日期）
                worksheet.cell(row=current_row, column=1, value="日期")
                worksheet.cell(row=current_row, column=2, value=weather_info['代表日期'].strftime('%Y-%m-%d'))
                current_row += 1

                # 日期类型行（周天标题）
                worksheet.cell(row=current_row, column=1, value="日期类型")
                weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
                for i, weekday in enumerate(weekdays, 2):
                    worksheet.cell(row=current_row, column=i, value=weekday)
                current_row += 1

                # 天气行
                worksheet.cell(row=current_row, column=1, value="天气")
                for i in range(2, 9):  # B到H列
                    worksheet.cell(row=current_row, column=i, value=weather_type)
                current_row += 1

                # 用电量行（总计）
                worksheet.cell(row=current_row, column=1, value="用电量")
                weekday_power = weather_info['周天用电量']
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weekday_power:
                        power_value = weekday_power[weekday]
                        worksheet.cell(row=current_row, column=i, value=round(power_value, 2))
                current_row += 1

                # 具体日期行
                worksheet.cell(row=current_row, column=1, value="具体日期")
                weekday_dates = weather_info['周天日期']
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weekday_dates:
                        date_value = weekday_dates[weekday]
                        worksheet.cell(row=current_row, column=i, value=date_value.strftime('%Y-%m-%d'))
                current_row += 1

                # 具体用电量行（与具体日期对应）
                worksheet.cell(row=current_row, column=1, value="具体用电量")
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weekday_power:
                        power_value = weekday_power[weekday]
                        worksheet.cell(row=current_row, column=i, value=round(power_value, 2))

                current_row += 2  # 空一行分隔不同天气类型

            print(f"成功添加 {len(region_data)} 种天气类型的数据到工作表 {sheet_name}")
            return True

        except Exception as e:
            print(f"添加数据到工作表 {sheet_name} 失败: {e}")
            return False
    
    def process_all_regions(self):
        """
        处理所有地区的数据
        """
        regions = ['杭州', '金华', '宁波', '台州', '衢州']
        
        for region in regions:
            print(f"\n处理地区: {region}")
            
            if region in self.grouped_data:
                region_data = self.grouped_data[region]
                self.add_formatted_data_to_sheet(region, region_data)
            else:
                print(f"警告: 未找到地区 {region} 的数据")
    
    def save_result(self, output_file):
        """
        保存结果文件
        """
        try:
            self.target_workbook.save(output_file)
            print(f"\n✅ 处理完成！结果已保存到: {output_file}")
            return True
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False
    
    def process(self, source_file, target_file, output_file):
        """
        完整的处理流程
        """
        print(f"最终版本：周天天气数据格式化工具")
        print(f"=" * 80)
        print(f"源数据文件: {source_file}")
        print(f"目标文件: {target_file}")
        print(f"输出文件: {output_file}")
        
        # 执行处理步骤
        if not self.load_and_process_data(source_file, target_file):
            return False
        
        self.process_all_regions()
        
        if not self.save_result(output_file):
            return False
        
        return True

def main():
    """主函数"""
    # 源数据文件（之前生成的周天天气分组文件）
    source_file = "城市售电表（含气温+日期类型+天气）_周天天气分组.xlsx"
    
    # 目标文件
    target_file = "/Users/<USER>/Desktop/副本城市售电表（含气温+日期类型+天气）(1).xlsx"
    
    # 输出文件
    output_file = "最终版本_副本城市售电表_含周天天气汇总.xlsx"
    
    if not os.path.exists(source_file):
        print(f"错误: 源数据文件不存在 - {source_file}")
        print("请先运行 weekday_weather_grouper.py 生成源数据文件")
        return
    
    if not os.path.exists(target_file):
        print(f"错误: 目标文件不存在 - {target_file}")
        return
    
    formatter = FinalWeekdayWeatherFormatter()
    success = formatter.process(source_file, target_file, output_file)
    
    if success:
        print(f"\n🎉 任务完成！")
        print(f"已成功将同一周天相同天气的数据按指定格式添加到各个地区分表中")
        print(f"文件保存位置: {output_file}")
        
        # 显示数据汇总
        print(f"\n📊 数据汇总:")
        for region, data in formatter.grouped_data.items():
            print(f"  {region}: {len(data)} 种天气类型")
            for weather, info in data.items():
                weekday_count = len(info['周天用电量'])
                total_power = sum(info['周天用电量'].values())
                print(f"    {weather}: {weekday_count} 个周天, 总用电量 {total_power:.2f} kWh")

if __name__ == "__main__":
    main()
