#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中栋电厂出力曲线处理工具
从现货结算表提取电能数据，转换为15分钟间隔，计算总功率
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

class PowerCurveProcessor:
    def __init__(self):
        """
        初始化出力曲线处理器
        """
        self.settlement_data = None
        self.curve_template = None
        self.processed_data = None
        
        # 15分钟时间点列表
        self.time_points = []
        for hour in range(24):
            for minute in [15, 30, 45]:
                self.time_points.append(f"{hour:02d}:{minute:02d}")
            if hour < 23:  # 避免25:00
                self.time_points.append(f"{hour+1:02d}:00")
        self.time_points.append("24:00")  # 添加24:00
    
    def load_settlement_data(self, file_path):
        """
        加载现货结算表数据
        """
        print("正在加载中栋电厂现货结算表...")
        
        try:
            # 跳过前3行，从第4行开始读取
            df = pd.read_excel(file_path, skiprows=3)
            
            # 重新命名列
            df.columns = ['项目名称', '结算单元', '空列1', '时间', '计量电量', '结算电量', 
                         '出清电价', '结算电价', '空列2', '结算电费', '空列3']
            
            # 过滤掉表头行和空行
            df = df[df['时间'].notna()]
            df = df[df['时间'] != '时间']
            
            # 转换时间格式
            df['时间'] = pd.to_datetime(df['时间'])
            
            # 转换数值列
            numeric_cols = ['计量电量', '结算电量', '出清电价', '结算电价', '结算电费']
            for col in numeric_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 过滤掉无效数据
            df = df.dropna(subset=['时间', '结算电量'])
            
            self.settlement_data = df
            
            print(f"✅ 成功加载现货结算数据: {len(df)} 条记录")
            print(f"   时间范围: {df['时间'].min()} 到 {df['时间'].max()}")
            print(f"   结算电量范围: {df['结算电量'].min():.2f} - {df['结算电量'].max():.2f} MWh")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载现货结算数据失败: {e}")
            return False
    
    def load_curve_template(self, file_path):
        """
        加载出力曲线表格模板
        """
        print("正在加载出力曲线表格模板...")
        
        try:
            df = pd.read_excel(file_path)
            self.curve_template = df
            
            print(f"✅ 成功加载出力曲线模板: {len(df)} 行")
            print(f"   列名: {list(df.columns)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载出力曲线模板失败: {e}")
            return False
    
    def interpolate_to_15min(self):
        """
        将半小时数据转换为15分钟间隔数据
        注意：原始数据是半小时累积电量，需要计算每15分钟的实际发电量
        """
        print("\n正在将半小时数据转换为15分钟间隔...")

        if self.settlement_data is None:
            print("❌ 请先加载现货结算数据")
            return False

        # 按日期分组处理
        daily_data = []

        for date, group in self.settlement_data.groupby(self.settlement_data['时间'].dt.date):
            print(f"  处理日期: {date}")

            # 创建完整的15分钟时间序列
            date_str = date.strftime('%Y-%m-%d')
            full_times = []

            for time_point in self.time_points:
                if time_point == "24:00":
                    # 24:00 对应次日00:00
                    next_date = date + timedelta(days=1)
                    full_time = datetime.combine(next_date, datetime.strptime("00:00", "%H:%M").time())
                else:
                    full_time = datetime.combine(date, datetime.strptime(time_point, "%H:%M").time())
                full_times.append(full_time)

            # 创建15分钟间隔的DataFrame
            df_15min = pd.DataFrame({
                '时间': full_times,
                '时间点': self.time_points
            })

            # 合并原始数据
            group_sorted = group.sort_values('时间')

            # 计算15分钟实际发电量
            power_15min = []

            for i, row in df_15min.iterrows():
                current_time = row['时间']
                time_point = row['时间点']

                # 找到对应的半小时数据点
                # 15分钟时间点对应的半小时区间
                if time_point.endswith(':15'):
                    # 00:15 对应 00:00-00:30 区间的前半部分
                    hour = int(time_point.split(':')[0])
                    if hour == 0:
                        prev_time = datetime.combine(date - timedelta(days=1), datetime.strptime("23:30", "%H:%M").time())
                    else:
                        prev_time = datetime.combine(date, datetime.strptime(f"{hour-1:02d}:30", "%H:%M").time())
                    next_time = datetime.combine(date, datetime.strptime(f"{hour:02d}:30", "%H:%M").time())
                elif time_point.endswith(':30'):
                    # 00:30 对应 00:00-00:30 区间的后半部分
                    hour = int(time_point.split(':')[0])
                    if hour == 0:
                        prev_time = datetime.combine(date - timedelta(days=1), datetime.strptime("23:30", "%H:%M").time())
                    else:
                        prev_time = datetime.combine(date, datetime.strptime(f"{hour-1:02d}:30", "%H:%M").time())
                    next_time = datetime.combine(date, datetime.strptime(f"{hour:02d}:30", "%H:%M").time())
                elif time_point.endswith(':45'):
                    # 00:45 对应 00:30-01:00 区间的前半部分
                    hour = int(time_point.split(':')[0])
                    prev_time = datetime.combine(date, datetime.strptime(f"{hour:02d}:30", "%H:%M").time())
                    next_time = datetime.combine(date, datetime.strptime(f"{hour+1:02d}:00", "%H:%M").time())
                else:  # :00
                    # 01:00 对应 00:30-01:00 区间的后半部分
                    hour = int(time_point.split(':')[0])
                    if hour == 0 or time_point == "24:00":
                        hour = 24
                    prev_time = datetime.combine(date, datetime.strptime(f"{hour-1:02d}:30", "%H:%M").time())
                    if hour == 24:
                        next_time = datetime.combine(date + timedelta(days=1), datetime.strptime("00:00", "%H:%M").time())
                    else:
                        next_time = datetime.combine(date, datetime.strptime(f"{hour:02d}:00", "%H:%M").time())

                # 查找对应的半小时数据
                prev_data = group_sorted[group_sorted['时间'] == prev_time]
                next_data = group_sorted[group_sorted['时间'] == next_time]

                if len(next_data) > 0:
                    # 半小时电量除以2得到15分钟电量
                    half_hour_energy = next_data.iloc[0]['结算电量']
                    quarter_hour_energy = half_hour_energy / 2
                    power_15min.append(quarter_hour_energy)
                elif len(prev_data) > 0:
                    # 使用前一个数据点
                    half_hour_energy = prev_data.iloc[0]['结算电量']
                    quarter_hour_energy = half_hour_energy / 2
                    power_15min.append(quarter_hour_energy)
                else:
                    # 没有数据，使用0
                    power_15min.append(0)

            df_15min['电量(MWh)'] = power_15min
            df_15min['日期'] = date_str

            daily_data.append(df_15min)

        # 合并所有日期的数据
        if daily_data:
            self.processed_data = pd.concat(daily_data, ignore_index=True)
            print(f"✅ 15分钟间隔数据生成完成: {len(self.processed_data)} 条记录")
            return True
        else:
            print("❌ 没有数据可处理")
            return False
    
    def calculate_power(self):
        """
        计算总功率 (总功率 = 总电量 / 总时长)
        """
        print("\n正在计算总功率...")
        
        if self.processed_data is None:
            print("❌ 请先生成15分钟间隔数据")
            return False
        
        # 为每个时间点计算功率
        # 15分钟 = 0.25小时
        time_interval = 0.25  # 小时
        
        self.processed_data['功率(MW)'] = self.processed_data['电量(MWh)'] / time_interval
        
        # 计算统计信息
        total_energy = self.processed_data['电量(MWh)'].sum()
        total_time = len(self.processed_data) * time_interval
        average_power = total_energy / total_time if total_time > 0 else 0
        
        print(f"✅ 功率计算完成:")
        print(f"   总电量: {total_energy:.2f} MWh")
        print(f"   总时长: {total_time:.2f} 小时")
        print(f"   平均功率: {average_power:.2f} MW")
        print(f"   功率范围: {self.processed_data['功率(MW)'].min():.2f} - {self.processed_data['功率(MW)'].max():.2f} MW")
        
        return True
    
    def create_output_format(self):
        """
        创建仿照出力曲线表格格式的输出
        """
        print("\n正在创建输出格式...")
        
        if self.processed_data is None:
            print("❌ 请先处理数据")
            return None
        
        # 按日期分组创建输出
        output_data = []
        
        for date, group in self.processed_data.groupby('日期'):
            print(f"  处理日期: {date}")
            
            # 创建该日期的数据
            daily_output = pd.DataFrame({
                '时间': group['时间点'].values,
                '电量(MWh)': group['电量(MWh)'].round(3),
                '功率(MW)': group['功率(MW)'].round(3)
            })
            
            daily_output['日期'] = date
            output_data.append(daily_output)
        
        if output_data:
            final_output = pd.concat(output_data, ignore_index=True)
            print(f"✅ 输出格式创建完成: {len(final_output)} 条记录")
            return final_output
        else:
            return None
    
    def save_results(self, output_file="中栋电厂15分钟出力曲线.xlsx"):
        """
        保存处理结果
        """
        print(f"\n正在保存结果...")
        
        output_data = self.create_output_format()
        if output_data is None:
            print("❌ 没有数据可保存")
            return False
        
        try:
            output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 1. 完整数据
                output_data.to_excel(writer, sheet_name='15分钟出力曲线', index=False)
                
                # 2. 按日期分表
                for date in output_data['日期'].unique():
                    daily_data = output_data[output_data['日期'] == date].copy()
                    daily_data = daily_data[['时间', '电量(MWh)', '功率(MW)']]  # 移除日期列
                    sheet_name = f"出力曲线_{date}"
                    daily_data.to_excel(writer, sheet_name=sheet_name, index=False)
                    print(f"    {date}: {len(daily_data)} 个时间点")
                
                # 3. 统计汇总
                summary_data = output_data.groupby('日期').agg({
                    '电量(MWh)': ['sum', 'mean', 'min', 'max'],
                    '功率(MW)': ['mean', 'min', 'max']
                }).round(3)
                
                summary_data.columns = ['总电量', '平均电量', '最小电量', '最大电量', 
                                      '平均功率', '最小功率', '最大功率']
                summary_data = summary_data.reset_index()
                summary_data.to_excel(writer, sheet_name='日统计汇总', index=False)
                
                # 4. 原始数据对比
                if self.settlement_data is not None:
                    comparison_data = self.settlement_data[['时间', '结算电量', '出清电价']].copy()
                    comparison_data.to_excel(writer, sheet_name='原始半小时数据', index=False)
            
            print(f"✅ 结果已保存到: {output_path}")
            
            # 显示统计信息
            print(f"\n📊 处理统计:")
            print(f"   处理日期数: {output_data['日期'].nunique()}")
            print(f"   总时间点数: {len(output_data)}")
            print(f"   总电量: {output_data['电量(MWh)'].sum():.2f} MWh")
            print(f"   平均功率: {output_data['功率(MW)'].mean():.2f} MW")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False

def main():
    """
    主函数
    """
    print("中栋电厂出力曲线处理工具")
    print("="*60)
    
    # 文件路径
    settlement_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/现货结算表/中栋电厂2025-07-01-day_sbs_gen_pub_detail_25.xlsx"
    
    curve_template_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/出力曲线表格 (1).xlsx"
    
    if not os.path.exists(settlement_file):
        print(f"❌ 现货结算表文件不存在: {settlement_file}")
        return
    
    processor = PowerCurveProcessor()
    
    # 1. 加载数据
    if not processor.load_settlement_data(settlement_file):
        return
    
    if os.path.exists(curve_template_file):
        processor.load_curve_template(curve_template_file)
    
    # 2. 转换为15分钟间隔
    if not processor.interpolate_to_15min():
        return
    
    # 3. 计算功率
    if not processor.calculate_power():
        return
    
    # 4. 保存结果
    if processor.save_results():
        print(f"\n🎉 处理完成！")
        print(f"📊 成功将半小时数据转换为15分钟间隔")
        print(f"⚡ 计算了每个时间点的功率")
        print(f"📁 结果已保存到Excel文件")
    else:
        print(f"\n❌ 处理失败！")

if __name__ == "__main__":
    main()
