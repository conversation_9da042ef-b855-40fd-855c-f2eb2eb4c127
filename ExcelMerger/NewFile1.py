import pandas as pd

# 1. 定义文件路径（请替换为您的实际路径）
用电量文件路径 = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/合并结果_6月用电量信息含地区.xlsx"
天气文件路径 = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/6月份全部地方天气.xlsx"
输出文件路径 = "/Users/<USER>/Desktop/合并结果_用电量+天气.xlsx"  # 替换为你想要的输出路径

# 2. 读取Excel文件
try:
    # 读取用电量数据（假设包含'地区'和'日期'列）
    df_用电量 = pd.read_excel(用电量文件路径)
    
    # 读取天气数据（假设包含'地区'、'日期'和天气相关列）
    df_天气 = pd.read_excel(天气文件路径)
    
    # 3. 合并数据（左连接，保留所有用电量数据）
    # 注意：确保列名一致，例如'地区'和'日期'
    df_合并 = pd.merge(
        df_用电量,
        df_天气,
        on=['地区', '日期'],  # 根据这两列匹配
        how='left'           # 保留左表（用电量）所有行
    )
    
    # 4. 保存结果
    df_合并.to_excel(输出文件路径, index=False)
    print(f"合并完成！结果已保存到: {输出文件_path}")

except Exception as e:
    print(f"出错: {e}")
    print("请检查：")
    print("1. 文件路径是否正确（尤其是微信路径可能变动）")
    print("2. Excel中是否包含'地区'和'日期'列（名称需完全一致）")
    print("3. 日期格式是否一致（建议都改为YYYY-MM-DD格式）")