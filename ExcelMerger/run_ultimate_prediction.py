#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行终极预测模型
实现完整的预测流程：总量约束 + 外部因素 + 动态更新
"""

import pandas as pd
import numpy as np
from ultimate_prediction_model import UltimatePredictionModel
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

def run_complete_ultimate_prediction():
    """
    运行完整的终极预测流程
    """
    print("🚀 运行终极预测模型完整流程")
    print("🎯 总量约束 + 外部因素 + 动态更新")
    print("="*70)
    
    model = UltimatePredictionModel()
    
    try:
        # 步骤1: 初始化外部因素
        print("步骤1: 初始化外部因素")
        model.initialize_external_factors()
        
        # 步骤2: 加载综合数据
        print("\n步骤2: 加载综合数据")
        df_train, df_actual_18, df_actual_19 = model.load_and_prepare_comprehensive_data()
        
        # 步骤3: 创建增强用户聚类
        print("\n步骤3: 创建增强用户聚类")
        user_clusters = model.create_enhanced_user_clusters(df_train)
        
        # 步骤4: 训练增强群体模型
        print("\n步骤4: 训练增强群体模型")
        cluster_success = train_enhanced_cluster_models(model, df_train, df_actual_18)
        
        if cluster_success:
            # 步骤5: 训练总量约束模型
            print("\n步骤5: 训练总量约束模型")
            constraint_success = train_total_constraint_model(model, df_train, df_actual_18, df_actual_19)
            
            if constraint_success:
                # 步骤6: 执行终极预测
                print("\n步骤6: 执行终极预测")
                final_predictions, weather_data = execute_ultimate_prediction(model, '2025-07-19')
                
                if final_predictions is not None:
                    # 步骤7: 评估终极效果
                    print("\n步骤7: 评估终极效果")
                    evaluation = evaluate_ultimate_prediction(final_predictions, df_actual_19)
                    
                    # 步骤8: 保存终极结果
                    print("\n步骤8: 保存终极结果")
                    output_file = save_ultimate_results(final_predictions, evaluation, weather_data, '2025-07-19')
                    
                    if output_file:
                        print(f"\n🎉 终极预测完成！")
                        print(f"📁 结果文件: {output_file}")
                        
                        if evaluation:
                            print(f"\n🏆 终极效果:")
                            print(f"   MAE: {evaluation['mae']:.1f} kWh")
                            print(f"   R²: {evaluation['r2']:.4f}")
                            print(f"   总量误差: {evaluation['total_error_pct']:.1f}%")
                            
                            print(f"\n📈 相比超精度模型的改进:")
                            print(f"   MAE: 1665.8 → {evaluation['mae']:.1f} kWh")
                            print(f"   R²: 0.4655 → {evaluation['r2']:.4f}")
                            print(f"   总量误差: 17.2% → {evaluation['total_error_pct']:.1f}%")
                
            else:
                print("❌ 总量约束模型训练失败")
        else:
            print("❌ 增强群体模型训练失败")
            
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

def train_enhanced_cluster_models(model, df_train, df_actual_18):
    """
    训练增强群体模型
    """
    print("🤖 训练增强群体模型...")
    
    # 准备训练数据
    df_train_enhanced = df_train.merge(
        model.user_clusters[['户号', '用户群体', '天气敏感度', '周末效应']], 
        on='户号', how='left'
    )
    
    # 添加18号真实数据作为目标
    if len(df_actual_18) > 0:
        df_actual_18_target = df_actual_18[['户号', '总电量(kWh)']].copy()
        df_actual_18_target.columns = ['户号', '目标电量']
        
        df_train_with_target = df_train_enhanced.merge(
            df_actual_18_target, on='户号', how='inner'
        )
    else:
        print("❌ 没有18号数据作为训练目标")
        return False
    
    print(f"增强训练数据: {len(df_train_with_target)} 条")
    
    # 为每个群体训练增强模型
    for cluster in range(10):
        cluster_data = df_train_with_target[df_train_with_target['用户群体'] == cluster]
        
        if len(cluster_data) < 15:
            print(f"  群体{cluster}: 数据不足({len(cluster_data)}条)，跳过")
            continue
        
        print(f"  训练增强群体{cluster}模型: {len(cluster_data)}条数据")
        
        # 按用户聚合特征
        user_data = cluster_data.groupby('户号').agg({
            '总电量(kWh)': ['mean', 'std', 'count'],
            '最高温度(°C)': 'mean',
            '最低温度(°C)': 'mean',
            '平均温度': 'mean',
            '湿度(%)': 'mean',
            'AQI': 'mean',
            '制冷度日': 'mean',
            '制热度日': 'mean',
            '天气舒适度指数': 'mean',
            '是否周末': 'mean',
            '是否月初': 'mean',
            '是否月末': 'mean',
            '天气敏感度': 'first',
            '周末效应': 'first',
            '目标电量': 'first'
        }).round(2)
        
        # 扁平化列名
        user_data.columns = ['_'.join(col).strip() for col in user_data.columns]
        user_data = user_data.reset_index()
        
        # 增强特征选择
        feature_columns = [
            '总电量(kWh)_mean', '总电量(kWh)_std', '总电量(kWh)_count',
            '平均温度_mean', '制冷度日_mean', '制热度日_mean',
            '天气舒适度指数_mean', '湿度(%)_mean', 'AQI_mean',
            '是否周末_mean', '是否月初_mean', '是否月末_mean',
            '天气敏感度_first', '周末效应_first'
        ]
        
        X = user_data[feature_columns].fillna(0)
        y = user_data['目标电量_first']
        
        if len(X) < 8:
            print(f"    群体{cluster}: 用户数不足({len(X)}个)，跳过")
            continue
        
        # 选择模型
        from sklearn.linear_model import Ridge
        from sklearn.ensemble import GradientBoostingRegressor
        from sklearn.preprocessing import StandardScaler
        from sklearn.metrics import r2_score, mean_absolute_error
        
        if len(X) < 25:
            model_obj = Ridge(alpha=0.5)
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            model_obj.fit(X_scaled, y)
            model.user_models[cluster] = {'model': model_obj, 'scaler': scaler, 'features': feature_columns}
        else:
            model_obj = GradientBoostingRegressor(n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42)
            model_obj.fit(X, y)
            model.user_models[cluster] = {'model': model_obj, 'scaler': None, 'features': feature_columns}
        
        # 评估
        if cluster in model.user_models and model.user_models[cluster]['scaler'] is not None:
            y_pred = model_obj.predict(X_scaled)
        else:
            y_pred = model_obj.predict(X)
        
        r2 = r2_score(y, y_pred)
        mae = mean_absolute_error(y, y_pred)
        
        print(f"    增强群体{cluster}: R²={r2:.4f}, MAE={mae:.1f}kWh")
    
    print(f"✅ 完成 {len(model.user_models)} 个增强群体模型训练")
    
    return True

def train_total_constraint_model(model, df_train, df_actual_18, df_actual_19):
    """
    训练总量约束模型
    """
    print("⚖️ 训练总量约束模型...")
    
    # 计算历史总量数据
    daily_totals = df_train.groupby(df_train['时间'].dt.date).agg({
        '总电量(kWh)': 'sum',
        '平均温度': 'mean',
        '制冷度日': 'mean',
        '天气舒适度指数': 'mean',
        '是否周末': 'mean'
    }).reset_index()
    
    # 添加18号实际总量
    if len(df_actual_18) > 0:
        total_18 = df_actual_18['总电量(kWh)'].sum()
        weather_18 = {
            '平均温度': df_actual_18['平均温度'].mean() if '平均温度' in df_actual_18.columns else 31.0,
            '制冷度日': df_actual_18['制冷度日'].mean() if '制冷度日' in df_actual_18.columns else 5.0,
            '天气舒适度指数': 1.15,
            '是否周末': 0
        }
        
        new_row = {
            '时间': pd.to_datetime('2025-07-18').date(),
            '总电量(kWh)': total_18,
            **weather_18
        }
        
        daily_totals = pd.concat([daily_totals, pd.DataFrame([new_row])], ignore_index=True)
    
    print(f"总量训练数据: {len(daily_totals)} 天")
    
    # 特征和目标
    constraint_features = ['平均温度', '制冷度日', '天气舒适度指数', '是否周末']
    X_constraint = daily_totals[constraint_features].fillna(0)
    y_constraint = daily_totals['总电量(kWh)']
    
    # 训练总量约束模型
    from sklearn.ensemble import GradientBoostingRegressor
    from sklearn.metrics import r2_score, mean_absolute_error
    
    model.total_constraint_model = GradientBoostingRegressor(
        n_estimators=50, max_depth=4, learning_rate=0.1, random_state=42
    )
    model.total_constraint_model.fit(X_constraint, y_constraint)
    
    # 评估
    y_pred_constraint = model.total_constraint_model.predict(X_constraint)
    r2_constraint = r2_score(y_constraint, y_pred_constraint)
    mae_constraint = mean_absolute_error(y_constraint, y_pred_constraint)
    
    print(f"✅ 总量约束模型: R²={r2_constraint:.4f}, MAE={mae_constraint:.0f}kWh")
    
    model.constraint_features = constraint_features
    
    return True

def execute_ultimate_prediction(model, target_date):
    """
    执行终极预测
    """
    print(f"🎯 执行终极预测 {target_date}...")
    
    # 1. 爬取真实天气数据
    weather_data = model.scrape_real_weather_data(target_date)
    
    # 2. 使用群体模型进行初始预测
    initial_predictions = predict_with_cluster_models(model, target_date, weather_data)
    
    # 3. 计算总量约束
    constraint_features_data = {
        '平均温度': weather_data['平均温度'],
        '制冷度日': weather_data['制冷度日'],
        '天气舒适度指数': weather_data['天气舒适度指数'],
        '是否周末': 1 if weather_data['日期类型'] == '周末' else 0
    }
    
    X_constraint = pd.DataFrame([constraint_features_data])[model.constraint_features]
    predicted_total = model.total_constraint_model.predict(X_constraint)[0]
    
    print(f"📊 总量约束: 预测总量 {predicted_total:.0f} kWh")
    
    # 4. 应用总量约束调整
    initial_total = initial_predictions['总电量(kWh)'].sum()
    adjustment_factor = predicted_total / initial_total if initial_total > 0 else 1.0
    
    print(f"📊 调整因子: {adjustment_factor:.4f}")
    
    # 5. 调整个体预测
    final_predictions = initial_predictions.copy()
    final_predictions['总电量(kWh)'] = (final_predictions['总电量(kWh)'] * adjustment_factor).round(1)
    
    # 6. 重新计算分时电量
    final_predictions = recalculate_time_segments(final_predictions)
    
    print(f"✅ 终极预测完成: {len(final_predictions)} 个用户")
    print(f"📊 调整后总电量: {final_predictions['总电量(kWh)'].sum():.1f} kWh")
    print(f"📊 平均用电量: {final_predictions['总电量(kWh)'].mean():.1f} kWh")
    
    return final_predictions, weather_data

def predict_with_cluster_models(model, target_date, weather_data):
    """
    使用群体模型进行预测
    """
    from datetime import datetime
    
    all_users = model.user_clusters.copy()
    predictions = []
    
    for _, user_row in all_users.iterrows():
        user_id = user_row['户号']
        cluster = user_row['用户群体']
        
        if cluster not in model.user_models:
            # 使用历史平均值
            pred_power = user_row['总电量(kWh)_mean']
        else:
            # 使用群体模型预测
            model_info = model.user_models[cluster]
            model_obj = model_info['model']
            scaler = model_info['scaler']
            features = model_info['features']
            
            # 准备预测特征
            pred_features = {
                '总电量(kWh)_mean': user_row['总电量(kWh)_mean'],
                '总电量(kWh)_std': user_row['总电量(kWh)_std'],
                '总电量(kWh)_count': user_row.get('总电量(kWh)_count', 30),
                '平均温度_mean': weather_data['平均温度'],
                '制冷度日_mean': weather_data['制冷度日'],
                '制热度日_mean': weather_data['制热度日'],
                '天气舒适度指数_mean': weather_data['天气舒适度指数'],
                '湿度(%)_mean': weather_data['湿度(%)'],
                'AQI_mean': weather_data['AQI'],
                '是否周末_mean': 1 if weather_data['日期类型'] == '周末' else 0,
                '是否月初_mean': 1 if datetime.strptime(target_date, '%Y-%m-%d').day <= 10 else 0,
                '是否月末_mean': 1 if datetime.strptime(target_date, '%Y-%m-%d').day >= 20 else 0,
                '天气敏感度_first': user_row.get('天气敏感度', 1.0),
                '周末效应_first': user_row.get('周末效应', 0.0)
            }
            
            X_pred = pd.DataFrame([pred_features])[features].fillna(0)
            
            if scaler is not None:
                X_pred_scaled = scaler.transform(X_pred)
                pred_power = model_obj.predict(X_pred_scaled)[0]
            else:
                pred_power = model_obj.predict(X_pred)[0]
            
            pred_power = max(pred_power, 0)
        
        predictions.append({
            '表计号': user_id,
            '户号': user_id,
            '时间': target_date,
            '总电量(kWh)': round(pred_power, 1),
            '用户群体': cluster
        })
    
    return pd.DataFrame(predictions)

def recalculate_time_segments(df_pred):
    """
    重新计算分时电量
    """
    # 使用优化的分时比例
    optimal_ratios = {
        '尖电量': 0.1940,
        '峰电量': 0.2807,
        '平电量': 0.1380,
        '谷电量': 0.3873
    }
    
    df_pred['尖电量(kWh)'] = (df_pred['总电量(kWh)'] * optimal_ratios['尖电量']).round(1)
    df_pred['峰电量(kWh)'] = (df_pred['总电量(kWh)'] * optimal_ratios['峰电量']).round(1)
    df_pred['平电量(kWh)'] = (df_pred['总电量(kWh)'] * optimal_ratios['平电量']).round(1)
    df_pred['谷电量(kWh)'] = (df_pred['总电量(kWh)'] * optimal_ratios['谷电量']).round(1)
    
    return df_pred

def evaluate_ultimate_prediction(df_pred, df_actual_19):
    """
    评估终极预测效果
    """
    print("📈 评估终极预测效果...")
    
    if len(df_actual_19) == 0:
        print("❌ 没有19号真实数据用于评估")
        return None
    
    # 合并预测和真实数据
    df_compare = pd.merge(df_pred, df_actual_19, on='户号', suffixes=('_pred', '_actual'))
    
    print(f"匹配用户数: {len(df_compare)}")
    
    if len(df_compare) == 0:
        print("❌ 没有匹配的用户")
        return None
    
    # 计算误差指标
    mae = mean_absolute_error(df_compare['总电量(kWh)_actual'], df_compare['总电量(kWh)_pred'])
    rmse = np.sqrt(mean_squared_error(df_compare['总电量(kWh)_actual'], df_compare['总电量(kWh)_pred']))
    r2 = r2_score(df_compare['总电量(kWh)_actual'], df_compare['总电量(kWh)_pred'])
    
    # 总量对比
    pred_total = df_compare['总电量(kWh)_pred'].sum()
    actual_total = df_compare['总电量(kWh)_actual'].sum()
    total_error_pct = (pred_total - actual_total) / actual_total * 100 if actual_total > 0 else 0
    
    print(f"\n📊 终极预测评估结果:")
    print(f"   MAE: {mae:.1f} kWh")
    print(f"   RMSE: {rmse:.1f} kWh")
    print(f"   R²: {r2:.4f}")
    print(f"   总量误差: {total_error_pct:.1f}%")
    
    return {
        'mae': mae,
        'rmse': rmse,
        'r2': r2,
        'total_error_pct': total_error_pct,
        'comparison_data': df_compare
    }

def save_ultimate_results(df_pred, evaluation, weather_data, target_date):
    """
    保存终极预测结果
    """
    print("💾 保存终极预测结果...")
    
    date_str = target_date.replace('-', '')
    output_file = f"/Users/<USER>/RiderProjects/Solution3/终极预测_{date_str}.xlsx"
    
    try:
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 1. 预测结果
            result_cols = ['表计号', '户号', '时间', '总电量(kWh)', '尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']
            df_pred[result_cols].to_excel(writer, sheet_name='终极预测结果', index=False)
            
            # 2. 模型评估
            if evaluation:
                eval_data = {
                    '指标': ['MAE (kWh)', 'RMSE (kWh)', 'R²', '总量误差 (%)'],
                    '数值': [
                        evaluation['mae'],
                        evaluation['rmse'],
                        evaluation['r2'],
                        evaluation['total_error_pct']
                    ]
                }
                pd.DataFrame(eval_data).to_excel(writer, sheet_name='模型评估', index=False)
            
            # 3. 天气数据
            weather_df = pd.DataFrame([weather_data])
            weather_df.to_excel(writer, sheet_name='使用的天气数据', index=False)
            
            # 4. 改进对比
            improvement_data = {
                '模型版本': ['超精度模型', '终极模型', '改进幅度'],
                'MAE (kWh)': [1665.8, evaluation['mae'] if evaluation else 0, f"{((1665.8 - evaluation['mae'])/1665.8*100):.1f}%" if evaluation else "N/A"],
                'R²': [0.4655, evaluation['r2'] if evaluation else 0, f"{((evaluation['r2'] - 0.4655)/0.4655*100):.1f}%" if evaluation else "N/A"],
                '总量误差 (%)': [17.2, evaluation['total_error_pct'] if evaluation else 0, f"{(17.2 - evaluation['total_error_pct']):.1f}个百分点" if evaluation else "N/A"]
            }
            pd.DataFrame(improvement_data).to_excel(writer, sheet_name='改进对比', index=False)
        
        print(f"✅ 终极结果已保存到: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return None

if __name__ == "__main__":
    run_complete_ultimate_prediction()
