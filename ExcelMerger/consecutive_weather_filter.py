#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连续天气筛选工具
筛选出连续7天相同天气的信息
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from collections import defaultdict

class ConsecutiveWeatherFilter:
    def __init__(self):
        """
        初始化连续天气筛选器
        """
        self.df = None
        self.consecutive_periods = []
        
    def load_data(self, file_path):
        """
        加载Excel数据
        
        Args:
            file_path: Excel文件路径
        """
        try:
            print(f"正在读取文件: {os.path.basename(file_path)}")
            self.df = pd.read_excel(file_path)
            print(f"读取成功，共 {len(self.df)} 行数据")
            
            # 检查必要的列
            required_columns = ['时间', '地区']
            missing_columns = [col for col in required_columns if col not in self.df.columns]
            if missing_columns:
                raise ValueError(f"缺少必要的列: {missing_columns}")
            
            # 检查天气相关列
            weather_columns = [col for col in self.df.columns if any(keyword in col for keyword in ['天气', '气温', '湿度'])]
            if not weather_columns:
                raise ValueError("未找到天气相关列")
            
            print(f"发现天气相关列: {weather_columns}")
            
            # 确定主要天气列
            if '天气状况' in self.df.columns:
                self.weather_column = '天气状况'
            elif '天气' in self.df.columns:
                self.weather_column = '天气'
            elif '新天气' in self.df.columns:
                self.weather_column = '新天气'
            else:
                self.weather_column = weather_columns[0]
            
            print(f"使用天气列: {self.weather_column}")
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def preprocess_data(self):
        """
        数据预处理
        """
        try:
            print(f"\n=== 数据预处理 ===")
            
            # 转换时间列
            self.df['时间'] = pd.to_datetime(self.df['时间'])
            self.df['日期'] = self.df['时间'].dt.date
            
            # 清理天气数据
            self.df = self.df[self.df[self.weather_column].notna()].copy()
            print(f"清理后数据: {len(self.df)} 行")
            
            # 获取唯一地区和日期
            unique_regions = self.df['地区'].unique()
            unique_dates = sorted(self.df['日期'].unique())
            
            print(f"地区数量: {len(unique_regions)}")
            print(f"日期范围: {unique_dates[0]} 到 {unique_dates[-1]} (共{len(unique_dates)}天)")
            print(f"地区列表: {list(unique_regions)}")
            
            # 创建每日天气汇总
            self.daily_weather = self.df.groupby(['地区', '日期'])[self.weather_column].first().reset_index()
            print(f"每日天气汇总: {len(self.daily_weather)} 条记录")
            
            return True
            
        except Exception as e:
            print(f"数据预处理失败: {e}")
            return False
    
    def find_consecutive_weather_periods(self, min_days=7):
        """
        查找连续相同天气的时间段
        
        Args:
            min_days: 最少连续天数，默认7天
        """
        try:
            print(f"\n=== 查找连续{min_days}天相同天气的时间段 ===")
            
            self.consecutive_periods = []
            
            # 按地区分组处理
            for region in self.daily_weather['地区'].unique():
                region_data = self.daily_weather[self.daily_weather['地区'] == region].copy()
                region_data = region_data.sort_values('日期')
                
                print(f"\n分析地区: {region}")
                
                if len(region_data) < min_days:
                    print(f"  数据不足{min_days}天，跳过")
                    continue
                
                # 查找连续相同天气
                current_weather = None
                current_start_date = None
                consecutive_count = 0
                
                for _, row in region_data.iterrows():
                    date = row['日期']
                    weather = row[self.weather_column]
                    
                    if weather == current_weather:
                        consecutive_count += 1
                    else:
                        # 检查之前的连续段是否满足条件
                        if consecutive_count >= min_days and current_weather is not None:
                            end_date = date - timedelta(days=1)
                            period_info = {
                                '地区': region,
                                '天气': current_weather,
                                '开始日期': current_start_date,
                                '结束日期': end_date,
                                '连续天数': consecutive_count,
                                '日期列表': self._get_date_range(current_start_date, end_date)
                            }
                            self.consecutive_periods.append(period_info)
                            print(f"  找到连续{consecutive_count}天{current_weather}: {current_start_date} 到 {end_date}")
                        
                        # 开始新的连续段
                        current_weather = weather
                        current_start_date = date
                        consecutive_count = 1
                
                # 检查最后一个连续段
                if consecutive_count >= min_days and current_weather is not None:
                    end_date = region_data.iloc[-1]['日期']
                    period_info = {
                        '地区': region,
                        '天气': current_weather,
                        '开始日期': current_start_date,
                        '结束日期': end_date,
                        '连续天数': consecutive_count,
                        '日期列表': self._get_date_range(current_start_date, end_date)
                    }
                    self.consecutive_periods.append(period_info)
                    print(f"  找到连续{consecutive_count}天{current_weather}: {current_start_date} 到 {end_date}")
            
            print(f"\n总共找到 {len(self.consecutive_periods)} 个连续{min_days}天以上的相同天气时间段")
            
            return True
            
        except Exception as e:
            print(f"查找连续天气时间段失败: {e}")
            return False
    
    def _get_date_range(self, start_date, end_date):
        """
        获取日期范围内的所有日期
        """
        dates = []
        current_date = start_date
        while current_date <= end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)
        return dates
    
    def filter_data_by_consecutive_periods(self):
        """
        根据连续天气时间段筛选原始数据
        """
        try:
            print(f"\n=== 筛选连续天气时间段的数据 ===")
            
            if not self.consecutive_periods:
                print("没有找到连续天气时间段")
                return pd.DataFrame()
            
            filtered_data_list = []
            
            for period in self.consecutive_periods:
                region = period['地区']
                date_list = period['日期列表']
                
                # 筛选该地区在该时间段的所有数据
                region_mask = self.df['地区'] == region
                date_mask = self.df['日期'].isin(date_list)
                
                period_data = self.df[region_mask & date_mask].copy()
                
                # 添加连续天气信息
                period_data['连续天气类型'] = period['天气']
                period_data['连续开始日期'] = pd.to_datetime(period['开始日期']).strftime('%Y-%m-%d')
                period_data['连续结束日期'] = pd.to_datetime(period['结束日期']).strftime('%Y-%m-%d')
                period_data['连续天数'] = period['连续天数']
                
                filtered_data_list.append(period_data)
                
                print(f"  {region} {period['天气']} ({period['开始日期']} 到 {period['结束日期']}): {len(period_data)} 条记录")
            
            # 合并所有筛选的数据
            filtered_df = pd.concat(filtered_data_list, ignore_index=True)
            
            print(f"\n筛选结果: 共 {len(filtered_df)} 条记录")
            
            return filtered_df
            
        except Exception as e:
            print(f"数据筛选失败: {e}")
            return pd.DataFrame()
    
    def generate_summary_report(self):
        """
        生成连续天气汇总报告
        """
        print(f"\n" + "="*60)
        print(f"连续7天相同天气汇总报告")
        print(f"="*60)
        
        if not self.consecutive_periods:
            print("未找到连续7天相同天气的时间段")
            return
        
        # 按天气类型统计
        weather_stats = defaultdict(list)
        for period in self.consecutive_periods:
            weather_stats[period['天气']].append(period)
        
        print(f"\n各天气类型的连续时间段:")
        for weather, periods in weather_stats.items():
            print(f"\n{weather}:")
            total_days = sum(p['连续天数'] for p in periods)
            print(f"  时间段数量: {len(periods)}")
            print(f"  总连续天数: {total_days}")
            print(f"  平均连续天数: {total_days/len(periods):.1f}")
            
            for i, period in enumerate(periods, 1):
                print(f"    {i}. {period['地区']}: {period['开始日期']} 到 {period['结束日期']} ({period['连续天数']}天)")
        
        # 按地区统计
        print(f"\n各地区的连续天气情况:")
        region_stats = defaultdict(list)
        for period in self.consecutive_periods:
            region_stats[period['地区']].append(period)
        
        for region, periods in region_stats.items():
            print(f"\n{region}:")
            for period in periods:
                print(f"  {period['天气']}: {period['开始日期']} 到 {period['结束日期']} ({period['连续天数']}天)")
    
    def save_filtered_data(self, output_file):
        """
        保存筛选后的数据
        
        Args:
            output_file: 输出文件路径
        """
        try:
            filtered_df = self.filter_data_by_consecutive_periods()
            
            if filtered_df.empty:
                print("没有数据需要保存")
                return False
            
            # 格式化时间列以便正确显示
            if '时间' in filtered_df.columns:
                filtered_df['时间显示'] = pd.to_datetime(filtered_df['时间']).dt.strftime('%Y-%m-%d %H:%M:%S')

            # 保存到Excel
            filtered_df.to_excel(output_file, index=False)
            print(f"\n筛选结果已保存到: {output_file}")

            # 显示数据预览
            print(f"\n数据预览 (前5行):")
            display_cols = ['时间显示', '地区', '总电量(kWh)', '连续天气类型', '连续开始日期', '连续结束日期', '连续天数']
            available_cols = [col for col in display_cols if col in filtered_df.columns]
            if '时间显示' not in filtered_df.columns and '时间' in filtered_df.columns:
                display_cols[0] = '时间'
                available_cols = [col for col in display_cols if col in filtered_df.columns]

            print(filtered_df[available_cols].head())
            
            return True
            
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False
    
    def process(self, input_file, output_file, min_days=7):
        """
        完整的处理流程
        
        Args:
            input_file: 输入Excel文件路径
            output_file: 输出Excel文件路径
            min_days: 最少连续天数
        """
        print(f"连续天气筛选工具")
        print(f"=" * 60)
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        print(f"最少连续天数: {min_days}")
        
        # 执行处理步骤
        if not self.load_data(input_file):
            return False
        
        if not self.preprocess_data():
            return False
        
        if not self.find_consecutive_weather_periods(min_days):
            return False
        
        self.generate_summary_report()
        
        if not self.save_filtered_data(output_file):
            return False
        
        print(f"\n✅ 处理完成！")
        return True

def main():
    """主函数"""
    # 支持多个可能的输入文件
    possible_files = [
        "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/合并结果_6月用电量含更新天气信息包含星期(1).xlsx",
        "最终合并结果_6月用电量含完整天气信息.xlsx",
        "合并结果_6月用电量含完整天气信息.xlsx",
        "合并结果_6月用电量信息含地区_含天气.xlsx"
    ]

    input_file = None
    for file_path in possible_files:
        if os.path.exists(file_path):
            input_file = file_path
            break

    if input_file is None:
        print("错误: 未找到可用的输入文件")
        print("请检查以下文件是否存在:")
        for file_path in possible_files:
            print(f"  - {file_path}")
        return

    output_file = "连续7天相同天气筛选结果.xlsx"

    print(f"使用输入文件: {os.path.basename(input_file)}")

    filter_tool = ConsecutiveWeatherFilter()
    filter_tool.process(input_file, output_file, min_days=7)

if __name__ == "__main__":
    main()
