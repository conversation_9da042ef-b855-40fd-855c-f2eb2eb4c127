#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除Excel文件中的白天温度和晚上温度列
"""

import pandas as pd
import os

def remove_temperature_columns():
    """
    删除户号用电量含天气数据分表.xlsx中的白天温度和晚上温度列
    """
    input_file = "/Users/<USER>/RiderProjects/Solution3/户号用电量含天气数据分表.xlsx"
    output_file = "/Users/<USER>/RiderProjects/Solution3/户号用电量含天气数据分表_已删除温度列.xlsx"
    
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return False
    
    print("正在处理Excel文件，删除白天温度和晚上温度列...")
    
    try:
        # 读取所有工作表
        xl_file = pd.ExcelFile(input_file)
        sheet_names = xl_file.sheet_names
        
        print(f"发现 {len(sheet_names)} 个工作表")
        
        # 要删除的列名
        columns_to_remove = ['白天温度(°C)', '晚上温度(°C)']
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            processed_sheets = 0
            removed_columns_count = 0
            
            for sheet_name in sheet_names:
                print(f"正在处理工作表: {sheet_name}")
                
                try:
                    # 读取工作表
                    df = pd.read_excel(input_file, sheet_name=sheet_name)
                    
                    # 检查并删除指定列
                    original_columns = df.columns.tolist()
                    columns_removed = []
                    
                    for col in columns_to_remove:
                        if col in df.columns:
                            df = df.drop(columns=[col])
                            columns_removed.append(col)
                            removed_columns_count += 1
                    
                    # 保存处理后的工作表
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                    processed_sheets += 1
                    
                    if columns_removed:
                        print(f"  ✅ 删除了列: {', '.join(columns_removed)}")
                    else:
                        print(f"  ℹ️ 未找到需要删除的列")
                
                except Exception as e:
                    print(f"  ❌ 处理工作表 {sheet_name} 失败: {e}")
                    continue
        
        print(f"\n✅ 处理完成！")
        print(f"   处理工作表数: {processed_sheets}/{len(sheet_names)}")
        print(f"   删除列次数: {removed_columns_count}")
        print(f"   输出文件: {output_file}")
        
        # 验证结果
        print(f"\n🔍 验证结果:")
        xl_new = pd.ExcelFile(output_file)
        df_sample = pd.read_excel(output_file, sheet_name='完整数据')
        
        print(f"   新文件工作表数: {len(xl_new.sheet_names)}")
        print(f"   完整数据行数: {len(df_sample)}")
        print(f"   完整数据列数: {len(df_sample.columns)}")
        
        # 检查是否还有要删除的列
        remaining_temp_cols = [col for col in df_sample.columns if col in columns_to_remove]
        if remaining_temp_cols:
            print(f"   ⚠️ 仍存在的温度列: {remaining_temp_cols}")
        else:
            print(f"   ✅ 温度列已完全删除")
        
        # 显示剩余的天气相关列
        weather_cols = [col for col in df_sample.columns if any(keyword in col for keyword in ['天气', '温度', 'AQI', '风向', '降水', '湿度', '气压'])]
        print(f"   剩余天气列: {weather_cols}")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("删除Excel文件中的白天温度和晚上温度列")
    print("="*60)
    
    success = remove_temperature_columns()
    
    if success:
        print(f"\n🎉 任务完成！")
        print(f"已成功删除所有工作表中的'白天温度(°C)'和'晚上温度(°C)'列")
        print(f"新文件已保存为: 户号用电量含天气数据分表_已删除温度列.xlsx")
    else:
        print(f"\n❌ 任务失败！")

if __name__ == "__main__":
    main()
