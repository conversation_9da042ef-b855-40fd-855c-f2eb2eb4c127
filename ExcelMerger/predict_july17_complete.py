#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的7月17日用电量预测工具
爬取真实天气数据 + 高精度预测模型
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
from bs4 import BeautifulSoup
import time
import random
import re
import json
from sklearn.ensemble import GradientBoostingRegressor, RandomForestRegressor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class July17CompletePrediction:
    def __init__(self):
        """
        初始化7月17日完整预测系统
        """
        self.weather_data = {}
        self.model = None
        self.scaler = None
        self.encoders = {}
        self.feature_names = []
        self.company_profiles = {}
        
        print("🌤️ 7月17日完整预测系统初始化")
        print("📊 集成天气爬取 + 机器学习预测")
    
    def scrape_july17_weather(self):
        """
        爬取7月17日真实天气数据
        """
        print("\n🌐 开始爬取7月17日真实天气数据...")
        
        # 浙江省主要城市
        cities = {
            '杭州': 'hangzhou',
            '宁波': 'ningbo', 
            '温州': 'wenzhou',
            '嘉兴': 'jiaxing',
            '湖州': 'huzhou',
            '绍兴': 'shaoxing'
        }
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive'
        }
        
        weather_results = {}
        
        for city_name, city_code in cities.items():
            try:
                url = f"https://www.tianqi24.com/{city_code}/history202507.html"
                print(f"  爬取 {city_name}: {url}")
                
                response = requests.get(url, headers=headers, timeout=15)
                response.encoding = 'utf-8'
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 查找7月17日数据
                    weather_lists = soup.find_all('ul', class_='col6')
                    
                    for ul in weather_lists:
                        items = ul.find_all('li')
                        
                        for item in items[1:]:  # 跳过表头
                            divs = item.find_all('div')
                            
                            if len(divs) >= 7:
                                date_text = divs[0].get_text().strip()
                                
                                if '07-17' in date_text or ('17' in date_text and '07' in date_text):
                                    print(f"    ✅ 找到{city_name}7月17日数据")
                                    
                                    # 解析天气数据
                                    weather_text = divs[1].get_text().strip()
                                    high_temp = self.extract_number(divs[2].get_text())
                                    low_temp = self.extract_number(divs[3].get_text())
                                    aqi = self.extract_number(divs[4].get_text())
                                    wind = divs[5].get_text().strip()
                                    precipitation = self.extract_float(divs[6].get_text())
                                    
                                    weather_results[city_name] = {
                                        'weather': weather_text.split('/')[0] if '/' in weather_text else weather_text,
                                        'temp_max': high_temp,
                                        'temp_min': low_temp,
                                        'aqi': aqi,
                                        'wind': wind,
                                        'precipitation': precipitation
                                    }
                                    
                                    print(f"      天气: {weather_results[city_name]['weather']}")
                                    print(f"      温度: {high_temp}°C/{low_temp}°C")
                                    print(f"      AQI: {aqi}")
                                    break
                
                # 请求间隔
                time.sleep(random.uniform(1, 3))
                
            except Exception as e:
                print(f"    ❌ {city_name}爬取失败: {e}")
        
        if weather_results:
            # 计算平均天气数据
            temps_max = [w['temp_max'] for w in weather_results.values() if w['temp_max']]
            temps_min = [w['temp_min'] for w in weather_results.values() if w['temp_min']]
            aqis = [w['aqi'] for w in weather_results.values() if w['aqi']]
            precipitations = [w['precipitation'] for w in weather_results.values() if w['precipitation']]
            
            weathers = [w['weather'] for w in weather_results.values()]
            main_weather = max(set(weathers), key=weathers.count) if weathers else '雨'
            
            self.weather_data = {
                '最高温度(°C)': np.mean(temps_max) if temps_max else 36.0,
                '最低温度(°C)': np.mean(temps_min) if temps_min else 27.0,
                '湿度(%)': 75.0,  # 7月典型湿度
                'AQI': np.mean(aqis) if aqis else 40.0,
                '降水量(mm)': np.mean(precipitations) if precipitations else 0.0,
                '气压(hPa)': 1013.0,
                '天气': '雨' if '雨' in main_weather else '阴',
                '风向': '南风',
                '日期类型': '工作日'
            }
            
            print(f"\n✅ 天气数据爬取完成: {len(weather_results)}个城市")
            print(f"📊 平均天气条件:")
            for key, value in self.weather_data.items():
                if isinstance(value, float):
                    print(f"   {key}: {value:.1f}")
                else:
                    print(f"   {key}: {value}")
            
            return True
        else:
            print("❌ 天气数据爬取失败，使用默认值")
            self.weather_data = {
                '最高温度(°C)': 36.0,
                '最低温度(°C)': 27.0,
                '湿度(%)': 75.0,
                'AQI': 40.0,
                '降水量(mm)': 0.0,
                '气压(hPa)': 1013.0,
                '天气': '雨',
                '风向': '南风',
                '日期类型': '工作日'
            }
            return False
    
    def extract_number(self, text):
        """提取数字"""
        match = re.search(r'(\d+)', str(text))
        return int(match.group(1)) if match else None
    
    def extract_float(self, text):
        """提取浮点数"""
        match = re.search(r'(\d+(?:\.\d+)?)', str(text))
        return float(match.group(1)) if match else 0.0
    
    def load_and_prepare_data(self):
        """
        加载和准备训练数据
        """
        print("\n📋 加载训练数据...")
        
        # 加载6月训练数据
        train_file = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        df = pd.read_excel(train_file)
        
        print(f"原始数据: {df.shape}")
        
        # 数据清洗
        df = df[df['总电量(kWh)'] >= 0]
        df = df[df['总电量(kWh)'] < df['总电量(kWh)'].quantile(0.99)]
        df = df.dropna(subset=['公司名称', '户号', '总电量(kWh)'])
        
        print(f"清洗后数据: {df.shape}")
        
        # 时间特征
        df['时间'] = pd.to_datetime(df['时间'])
        df['月份'] = df['时间'].dt.month
        df['日期'] = df['时间'].dt.day
        df['星期'] = df['时间'].dt.dayofweek
        df['是否周末'] = (df['星期'] >= 5).astype(int)
        
        # 天气特征
        df['平均温度'] = (df['最高温度(°C)'] + df['最低温度(°C)']) / 2
        df['温差'] = df['最高温度(°C)'] - df['最低温度(°C)']
        df['制冷度日'] = np.maximum(df['平均温度'] - 26, 0)
        df['制热度日'] = np.maximum(18 - df['平均温度'], 0)
        
        # 公司特征
        company_stats = df.groupby('公司名称')['总电量(kWh)'].agg(['mean', 'std']).reset_index()
        company_stats.columns = ['公司名称', '日均用电', '用电标准差']
        company_stats['用电稳定性'] = company_stats['用电标准差'] / (company_stats['日均用电'] + 1)
        
        df = df.merge(company_stats, on='公司名称', how='left')
        
        # 历史特征
        df = df.sort_values(['户号', '时间'])
        df['历史平均用电'] = df.groupby('户号')['总电量(kWh)'].transform(lambda x: x.expanding().mean().shift(1))
        
        # 地区特征
        region_avg = df.groupby('地区')['总电量(kWh)'].mean()
        df['地区平均用电'] = df['地区'].map(region_avg)
        
        self.company_profiles = company_stats.set_index('公司名称').to_dict('index')
        
        return df
    
    def train_prediction_model(self, df):
        """
        训练预测模型
        """
        print("\n🤖 训练预测模型...")
        
        # 选择特征
        feature_columns = [
            '月份', '日期', '星期', '是否周末',
            '最高温度(°C)', '最低温度(°C)', '平均温度', '温差',
            '湿度(%)', 'AQI', '降水量(mm)', '气压(hPa)',
            '制冷度日', '制热度日',
            '日均用电', '用电稳定性', '历史平均用电', '地区平均用电'
        ]
        
        # 分类特征编码
        categorical_features = ['天气', '风向', '地区', '日期类型']
        
        for cat_feature in categorical_features:
            if cat_feature in df.columns:
                encoder = LabelEncoder()
                df[f'{cat_feature}_encoded'] = encoder.fit_transform(df[cat_feature].astype(str))
                feature_columns.append(f'{cat_feature}_encoded')
                self.encoders[cat_feature] = encoder
        
        # 准备训练数据
        available_features = [col for col in feature_columns if col in df.columns]
        X = df[available_features].fillna(0)
        y = df['总电量(kWh)']
        
        # 处理无穷大值
        X = X.replace([np.inf, -np.inf], 0)
        
        print(f"特征数: {len(available_features)}")
        print(f"样本数: {len(X)}")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 训练模型
        self.model = GradientBoostingRegressor(n_estimators=100, random_state=42)
        self.model.fit(X_train, y_train)
        
        # 评估模型
        y_pred = self.model.predict(X_test)
        r2 = r2_score(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        
        print(f"✅ 模型训练完成")
        print(f"   R²: {r2:.4f}")
        print(f"   MAE: {mae:.2f} kWh")
        
        self.feature_names = available_features

        return True

    def predict_july17_consumption(self):
        """
        预测7月17日用电量
        """
        print("\n🔮 预测7月17日用电量...")

        if self.model is None:
            print("❌ 模型未训练")
            return None

        # 加载目标用户
        target_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250717-20250717 (1).xlsx'

        try:
            df_target = pd.read_excel(target_file)
            target_users = df_target['户号'].unique()
            print(f"目标用户数: {len(target_users)}")
        except Exception as e:
            print(f"❌ 无法加载目标用户: {e}")
            return None

        # 加载训练数据以获取用户信息
        train_file = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        df_train = pd.read_excel(train_file)

        # 创建预测数据
        pred_data = []

        for user in target_users:
            # 查找用户信息
            user_info = df_train[df_train['户号'] == user]

            if len(user_info) > 0:
                # 使用用户的历史信息
                company = user_info['公司名称'].iloc[0]
                region = user_info['地区'].iloc[0]
                avg_power = user_info['总电量(kWh)'].mean()

                pred_data.append({
                    '户号': user,
                    '公司名称': company,
                    '地区': region,
                    '历史平均用电': avg_power,
                    '时间': '2025-07-17'
                })
            else:
                # 新用户，使用默认值
                pred_data.append({
                    '户号': user,
                    '公司名称': '未知公司',
                    '地区': '杭州',
                    '历史平均用电': 3000.0,  # 默认值
                    '时间': '2025-07-17'
                })

        df_pred = pd.DataFrame(pred_data)

        # 添加天气特征
        for key, value in self.weather_data.items():
            df_pred[key] = value

        # 添加时间特征
        df_pred['时间'] = pd.to_datetime(df_pred['时间'])
        df_pred['月份'] = 7
        df_pred['日期'] = 17
        df_pred['星期'] = 2  # 假设是周三
        df_pred['是否周末'] = 0

        # 添加天气计算特征
        df_pred['平均温度'] = (df_pred['最高温度(°C)'] + df_pred['最低温度(°C)']) / 2
        df_pred['温差'] = df_pred['最高温度(°C)'] - df_pred['最低温度(°C)']
        df_pred['制冷度日'] = np.maximum(df_pred['平均温度'] - 26, 0)
        df_pred['制热度日'] = np.maximum(18 - df_pred['平均温度'], 0)

        # 添加公司特征
        for idx, row in df_pred.iterrows():
            company = row['公司名称']
            if company in self.company_profiles:
                df_pred.at[idx, '日均用电'] = self.company_profiles[company]['日均用电']
                df_pred.at[idx, '用电稳定性'] = self.company_profiles[company]['用电稳定性']
            else:
                df_pred.at[idx, '日均用电'] = row['历史平均用电']
                df_pred.at[idx, '用电稳定性'] = 0.3

        # 添加地区特征
        region_defaults = {'杭州': 4500, '宁波': 4200, '温州': 3800, '嘉兴': 4000}
        df_pred['地区平均用电'] = df_pred['地区'].map(region_defaults).fillna(4000)

        # 分类特征编码
        for cat_feature, encoder in self.encoders.items():
            if cat_feature in df_pred.columns:
                try:
                    df_pred[f'{cat_feature}_encoded'] = encoder.transform(df_pred[cat_feature].astype(str))
                except:
                    df_pred[f'{cat_feature}_encoded'] = 0

        # 准备预测特征
        X_pred = df_pred[self.feature_names].fillna(0)
        X_pred = X_pred.replace([np.inf, -np.inf], 0)

        # 进行预测
        predictions = self.model.predict(X_pred)
        predictions = np.maximum(predictions, 0)  # 确保非负

        # 创建结果
        df_result = df_pred[['户号', '公司名称', '时间']].copy()
        df_result['预测总电量(kWh)'] = predictions

        # 基于历史比例预测分时电量
        df_result = self.predict_time_segments(df_result)

        print(f"✅ 预测完成: {len(df_result)} 个用户")
        print(f"📊 总预测电量: {df_result['预测总电量(kWh)'].sum():.2f} kWh")
        print(f"📊 平均用电量: {df_result['预测总电量(kWh)'].mean():.2f} kWh")

        return df_result

    def predict_time_segments(self, df_result):
        """
        预测分时电量
        """
        # 使用历史比例
        ratios = {
            '尖电量': 0.05,
            '峰电量': 0.35,
            '平电量': 0.40,
            '谷电量': 0.20
        }

        df_result['预测尖电量(kWh)'] = df_result['预测总电量(kWh)'] * ratios['尖电量']
        df_result['预测峰电量(kWh)'] = df_result['预测总电量(kWh)'] * ratios['峰电量']
        df_result['预测平电量(kWh)'] = df_result['预测总电量(kWh)'] * ratios['平电量']
        df_result['预测谷电量(kWh)'] = df_result['预测总电量(kWh)'] * ratios['谷电量']

        return df_result

    def save_results(self, predictions):
        """
        保存预测结果
        """
        print("\n💾 保存预测结果...")

        output_file = "/Users/<USER>/RiderProjects/Solution3/7月17日真实天气预测结果.xlsx"

        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 1. 预测结果
                predictions.to_excel(writer, sheet_name='7月17日预测结果', index=False)

                # 2. 天气数据
                weather_df = pd.DataFrame([self.weather_data])
                weather_df.to_excel(writer, sheet_name='使用的天气数据', index=False)

                # 3. 按公司汇总
                company_summary = predictions.groupby('公司名称').agg({
                    '预测总电量(kWh)': ['sum', 'mean', 'count'],
                    '预测尖电量(kWh)': 'sum',
                    '预测峰电量(kWh)': 'sum',
                    '预测平电量(kWh)': 'sum',
                    '预测谷电量(kWh)': 'sum'
                }).round(2)

                company_summary.columns = ['_'.join(col).strip() for col in company_summary.columns]
                company_summary = company_summary.reset_index()
                company_summary.to_excel(writer, sheet_name='按公司汇总', index=False)

                # 4. 统计摘要
                summary_data = {
                    '项目': [
                        '预测日期', '预测用户数', '总预测电量(kWh)', '平均用电量(kWh)',
                        '最大用电量(kWh)', '最小用电量(kWh)', '使用天气', '最高温度(°C)',
                        '最低温度(°C)', 'AQI', '降水量(mm)'
                    ],
                    '数值': [
                        '2025-07-17',
                        len(predictions),
                        f"{predictions['预测总电量(kWh)'].sum():.2f}",
                        f"{predictions['预测总电量(kWh)'].mean():.2f}",
                        f"{predictions['预测总电量(kWh)'].max():.2f}",
                        f"{predictions['预测总电量(kWh)'].min():.2f}",
                        self.weather_data['天气'],
                        f"{self.weather_data['最高温度(°C)']:.1f}",
                        f"{self.weather_data['最低温度(°C)']:.1f}",
                        f"{self.weather_data['AQI']:.0f}",
                        f"{self.weather_data['降水量(mm)']:.1f}"
                    ]
                }

                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='预测摘要', index=False)

            print(f"✅ 结果已保存到: {output_file}")
            return output_file

        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return None

def main():
    """
    主函数
    """
    print("🌤️ 7月17日完整预测系统")
    print("爬取真实天气数据 + 高精度预测模型")
    print("="*60)

    predictor = July17CompletePrediction()

    try:
        # 1. 爬取天气数据
        print("步骤1: 爬取7月17日真实天气数据")
        weather_success = predictor.scrape_july17_weather()

        # 2. 加载和准备数据
        print("\n步骤2: 加载和准备训练数据")
        df = predictor.load_and_prepare_data()

        # 3. 训练模型
        print("\n步骤3: 训练预测模型")
        model_success = predictor.train_prediction_model(df)

        if model_success:
            # 4. 进行预测
            print("\n步骤4: 预测7月17日用电量")
            predictions = predictor.predict_july17_consumption()

            if predictions is not None:
                # 5. 保存结果
                print("\n步骤5: 保存预测结果")
                output_file = predictor.save_results(predictions)

                if output_file:
                    print(f"\n🎉 7月17日预测完成！")
                    print(f"📊 预测摘要:")
                    print(f"   预测用户数: {len(predictions)}")
                    print(f"   总预测电量: {predictions['预测总电量(kWh)'].sum():.2f} kWh")
                    print(f"   平均用电量: {predictions['预测总电量(kWh)'].mean():.2f} kWh")

                    print(f"\n🌤️ 使用的天气条件:")
                    for key, value in predictor.weather_data.items():
                        if isinstance(value, float):
                            print(f"   {key}: {value:.1f}")
                        else:
                            print(f"   {key}: {value}")

                    print(f"\n📁 结果文件: {output_file}")

                    # 显示前10名用电大户
                    top10 = predictions.nlargest(10, '预测总电量(kWh)')
                    print(f"\n🏢 预测用电量前10名:")
                    for i, (_, row) in enumerate(top10.iterrows(), 1):
                        print(f"   {i:2d}. 户号{row['户号']}: {row['预测总电量(kWh)']:.2f} kWh")

            else:
                print("❌ 预测失败")
        else:
            print("❌ 模型训练失败")

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
