#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版保存功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from zhongdong_complete_48_data_processor import ZhongdongComplete48DataProcessor

def save_continuous_format(processor):
    """
    保存连续时间格式的数据
    """
    print(f"\n正在保存连续时间格式...")
    
    if not processor.processed_15min_data:
        print("❌ 没有数据可保存")
        return False
    
    try:
        output_path = f"/Users/<USER>/RiderProjects/Solution3/中栋电厂7月1-14日连续时间出力曲线.xlsx"
        
        # 创建连续的格式化数据
        final_format = []
        
        # 按时间顺序排序
        sorted_data = sorted(processor.processed_15min_data, 
                           key=lambda x: (x['日期'], x['时间']))
        
        for data_point in sorted_data:
            final_format.append({
                '日期时间': f"{data_point['日期']} {data_point['时间']}",
                '实时出力_MW': f"{data_point['功率(MW)']:.4f}",
                '总功率=总电量/总时长': ""  # 电量列留空
            })
        
        final_df = pd.DataFrame(final_format)
        
        # 保存到Excel
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            final_df.to_excel(writer, sheet_name='连续时间出力曲线', index=False)
        
        print(f"✅ 连续时间出力曲线已保存到: {output_path}")
        print(f"   总数据条数: {len(final_format)}")
        print(f"   时间范围: {final_format[0]['日期时间']} 到 {final_format[-1]['日期时间']}")
        
        # 显示前10条数据验证
        print(f"\n前10条数据验证:")
        print(f"日期时间\\t\\t功率(MW)\\t电量列(空)")
        for i, data in enumerate(final_format[:10]):
            print(f"{data['日期时间']}\\t{data['实时出力_MW']}\\t{data['总功率=总电量/总时长']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("中栋电厂连续时间出力曲线生成工具")
    print("="*50)
    
    processor = ZhongdongComplete48DataProcessor()
    
    # 1. 处理所有文件，提取完整48条数据
    if not processor.process_all_files():
        return
    
    # 2. 生成15分钟出力曲线
    if not processor.generate_15min_curves():
        return
    
    # 3. 保存连续时间格式
    if save_continuous_format(processor):
        print(f"\n🎉 连续时间出力曲线生成完成！")
        print(f"📊 成功提取 {len(processor.all_complete_data)} 条完整半小时数据")
        print(f"⚡ 生成了 {len(processor.processed_15min_data)} 个15分钟时间点")
        print(f"📁 结果已保存为连续时间格式")
    else:
        print(f"\n❌ 处理失败！")

if __name__ == "__main__":
    main()
