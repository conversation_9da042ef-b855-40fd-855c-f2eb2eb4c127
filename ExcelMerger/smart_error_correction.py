#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能误差修正工具
基于误差模式分析，直接修正预测结果
"""

import pandas as pd
import numpy as np
from sklearn.metrics import mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class SmartErrorCorrector:
    def __init__(self):
        """
        初始化智能误差修正器
        """
        self.correction_factors = {}
        self.user_patterns = {}
        
    def analyze_error_patterns(self):
        """
        分析误差模式
        """
        print("📊 深度分析误差模式...")
        
        # 读取预测结果和实际数据
        pred_file = '/Users/<USER>/RiderProjects/Solution3/110kV以下用户用电量预测结果_2025-07-17.xlsx'
        actual_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250717-20250717 (1).xlsx'
        
        df_pred = pd.read_excel(pred_file)
        df_actual = pd.read_excel(actual_file)
        
        # 合并数据
        df_compare = pd.merge(df_pred, df_actual, on=['表计号', '户号'], suffixes=('_pred', '_actual'))
        
        print(f"匹配用户数: {len(df_compare)}")
        
        # 移除异常数据（实际用电量为0的用户）
        df_compare = df_compare[df_compare['总电量(kWh)_actual'] > 0]
        print(f"有效用户数: {len(df_compare)}")
        
        # 分析各个电量类型的误差模式
        target_columns = ['总电量(kWh)', '尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']
        
        error_patterns = {}
        
        for col in target_columns:
            pred_col = f'{col}_pred'
            actual_col = f'{col}_actual'
            
            if pred_col in df_compare.columns and actual_col in df_compare.columns:
                # 计算误差比率
                df_compare[f'{col}_ratio'] = df_compare[pred_col] / (df_compare[actual_col] + 1)
                df_compare[f'{col}_error'] = df_compare[pred_col] - df_compare[actual_col]
                
                # 分析误差模式
                median_ratio = df_compare[f'{col}_ratio'].median()
                mean_ratio = df_compare[f'{col}_ratio'].mean()
                std_ratio = df_compare[f'{col}_ratio'].std()
                
                error_patterns[col] = {
                    'median_ratio': median_ratio,
                    'mean_ratio': mean_ratio,
                    'std_ratio': std_ratio,
                    'correction_factor': 1.0 / median_ratio if median_ratio > 0 else 1.0
                }
                
                print(f"{col}:")
                print(f"  预测/实际比率 - 中位数: {median_ratio:.3f}, 平均: {mean_ratio:.3f}")
                print(f"  建议修正因子: {error_patterns[col]['correction_factor']:.3f}")
        
        self.correction_factors = error_patterns
        return df_compare, error_patterns
    
    def analyze_user_patterns(self, df_compare):
        """
        分析用户用电模式
        """
        print("\n🔍 分析用户用电模式...")
        
        # 读取历史训练数据
        train_file = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        df_train = pd.read_excel(train_file)
        
        # 计算每个用户的历史平均用电量
        user_stats = df_train.groupby('户号').agg({
            '总电量(kWh)': ['mean', 'std', 'count'],
            '尖电量(kWh)': 'mean',
            '峰电量(kWh)': 'mean',
            '平电量(kWh)': 'mean',
            '谷电量(kWh)': 'mean'
        }).round(2)
        
        # 扁平化列名
        user_stats.columns = ['_'.join(col).strip() for col in user_stats.columns]
        user_stats = user_stats.reset_index()
        
        # 计算用电模式特征
        user_stats['尖峰比'] = user_stats['尖电量(kWh)_mean'] / (user_stats['峰电量(kWh)_mean'] + 1)
        user_stats['峰谷比'] = user_stats['峰电量(kWh)_mean'] / (user_stats['谷电量(kWh)_mean'] + 1)
        
        # 用户分类
        user_stats['用电规模'] = pd.cut(user_stats['总电量(kWh)_mean'], 
                                   bins=[0, 1000, 5000, 20000, np.inf], 
                                   labels=['小用户', '中小用户', '中大用户', '大用户'])
        
        print(f"用户分类统计:")
        print(user_stats['用电规模'].value_counts())
        
        self.user_patterns = user_stats
        return user_stats
    
    def create_smart_corrections(self, df_compare):
        """
        创建智能修正预测
        """
        print("\n🧠 创建智能修正预测...")
        
        df_corrected = df_compare.copy()
        
        # 应用基础修正因子
        target_columns = ['总电量(kWh)', '尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']
        
        for col in target_columns:
            pred_col = f'{col}_pred'
            corrected_col = f'{col}_corrected'
            
            if col in self.correction_factors and pred_col in df_corrected.columns:
                correction_factor = self.correction_factors[col]['correction_factor']
                
                # 应用修正因子
                df_corrected[corrected_col] = df_corrected[pred_col] * correction_factor
                
                # 特殊处理
                if col == '尖电量(kWh)':
                    # 尖电量通常很小，使用更保守的修正
                    df_corrected[corrected_col] = df_corrected[pred_col] * min(correction_factor, 10.0)
                elif col == '平电量(kWh)':
                    # 平电量预测偏高，使用更强的修正
                    df_corrected[corrected_col] = df_corrected[pred_col] * correction_factor * 0.8
                
                # 确保非负
                df_corrected[corrected_col] = np.maximum(df_corrected[corrected_col], 0)
        
        # 重新计算总电量
        df_corrected['总电量(kWh)_corrected'] = (
            df_corrected['尖电量(kWh)_corrected'] + 
            df_corrected['峰电量(kWh)_corrected'] + 
            df_corrected['平电量(kWh)_corrected'] + 
            df_corrected['谷电量(kWh)_corrected']
        )
        
        return df_corrected
    
    def evaluate_corrections(self, df_corrected):
        """
        评估修正效果
        """
        print("\n📈 评估修正效果...")
        
        target_columns = ['总电量(kWh)', '尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']
        
        evaluation_results = []
        
        for col in target_columns:
            pred_col = f'{col}_pred'
            corrected_col = f'{col}_corrected'
            actual_col = f'{col}_actual'
            
            if all(c in df_corrected.columns for c in [pred_col, corrected_col, actual_col]):
                # 原始预测误差
                original_mae = mean_absolute_error(df_corrected[actual_col], df_corrected[pred_col])
                original_r2 = r2_score(df_corrected[actual_col], df_corrected[pred_col])
                
                # 修正后误差
                corrected_mae = mean_absolute_error(df_corrected[actual_col], df_corrected[corrected_col])
                corrected_r2 = r2_score(df_corrected[actual_col], df_corrected[corrected_col])
                
                # 改进率
                mae_improvement = (original_mae - corrected_mae) / original_mae * 100
                r2_improvement = corrected_r2 - original_r2
                
                result = {
                    '指标': col,
                    '原始MAE': original_mae,
                    '修正MAE': corrected_mae,
                    'MAE改进率(%)': mae_improvement,
                    '原始R²': original_r2,
                    '修正R²': corrected_r2,
                    'R²改进': r2_improvement
                }
                
                evaluation_results.append(result)
                
                print(f"{col}:")
                print(f"  MAE: {original_mae:.2f} → {corrected_mae:.2f} (改进{mae_improvement:.1f}%)")
                print(f"  R²: {original_r2:.4f} → {corrected_r2:.4f} (改进{r2_improvement:.4f})")
        
        return evaluation_results
    
    def save_corrected_results(self, df_corrected, evaluation_results):
        """
        保存修正结果
        """
        print("\n💾 保存智能修正结果...")
        
        output_file = "/Users/<USER>/RiderProjects/Solution3/智能修正的用电量预测结果_2025-07-17.xlsx"
        
        # 准备最终预测结果
        base_columns = ['表计号', '户号']
        if '时间' in df_corrected.columns:
            base_columns.append('时间')
        final_results = df_corrected[base_columns].copy()
        
        target_columns = ['总电量(kWh)', '尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']
        for col in target_columns:
            corrected_col = f'{col}_corrected'
            if corrected_col in df_corrected.columns:
                final_results[col] = df_corrected[corrected_col]
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 1. 智能修正预测结果
            final_results.to_excel(writer, sheet_name='智能修正预测结果', index=False)
            
            # 2. 详细对比分析
            comparison_cols = ['表计号', '户号']
            for col in target_columns:
                comparison_cols.extend([f'{col}_pred', f'{col}_corrected', f'{col}_actual'])
            
            if all(c in df_corrected.columns for c in comparison_cols):
                df_corrected[comparison_cols].to_excel(writer, sheet_name='详细对比分析', index=False)
            
            # 3. 修正效果评估
            pd.DataFrame(evaluation_results).to_excel(writer, sheet_name='修正效果评估', index=False)
            
            # 4. 修正因子
            correction_df = pd.DataFrame([
                {'电量类型': k, '修正因子': v['correction_factor'], '原始比率': v['median_ratio']}
                for k, v in self.correction_factors.items()
            ])
            correction_df.to_excel(writer, sheet_name='修正因子', index=False)
        
        print(f"✅ 智能修正结果已保存到: {output_file}")
        
        return final_results

def main():
    """
    主函数
    """
    print("智能误差修正工具")
    print("基于误差模式分析，直接修正预测结果")
    print("="*50)
    
    corrector = SmartErrorCorrector()
    
    try:
        # 1. 分析误差模式
        df_compare, error_patterns = corrector.analyze_error_patterns()
        
        # 2. 分析用户模式
        user_patterns = corrector.analyze_user_patterns(df_compare)
        
        # 3. 创建智能修正
        df_corrected = corrector.create_smart_corrections(df_compare)
        
        # 4. 评估修正效果
        evaluation_results = corrector.evaluate_corrections(df_corrected)
        
        # 5. 保存修正结果
        final_results = corrector.save_corrected_results(df_corrected, evaluation_results)
        
        print(f"\n🎉 智能误差修正完成！")
        print(f"📊 处理用户数: {len(df_corrected)}")
        print(f"🔧 应用修正因子: {len(corrector.correction_factors)}")
        print(f"📈 评估指标: {len(evaluation_results)}")
        print(f"📁 修正结果已保存")
        
        # 显示总体改进效果
        total_improvement = sum(r['MAE改进率(%)'] for r in evaluation_results if r['MAE改进率(%)'] > 0)
        print(f"🏆 总体MAE改进率: {total_improvement/len(evaluation_results):.1f}%")
        
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
