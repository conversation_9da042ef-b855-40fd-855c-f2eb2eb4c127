#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量Excel文件合并工具
可以处理多个用电量文件，将地区信息合并到每个文件中
"""

import pandas as pd
import os
import sys
from pathlib import Path

def read_excel_files(customer_file, usage_file):
    """读取两个Excel文件"""
    try:
        print(f"正在读取客户文件: {os.path.basename(customer_file)}")
        customer_df = pd.read_excel(customer_file)
        print(f"客户文件读取成功，共 {len(customer_df)} 行数据")
        
        print(f"正在读取用电量文件: {os.path.basename(usage_file)}")
        usage_df = pd.read_excel(usage_file)
        print(f"用电量文件读取成功，共 {len(usage_df)} 行数据")
        
        return customer_df, usage_df
        
    except FileNotFoundError as e:
        print(f"错误: 文件未找到 - {e}")
        return None, None
    except Exception as e:
        print(f"错误: 读取文件时发生异常 - {e}")
        return None, None

def find_matching_columns(customer_df, usage_df):
    """查找匹配列"""
    account_keywords = ['户号', '用户号', '客户号', '账号']
    region_keywords = ['地区', '区域', '地址', '位置', '供电所', '所属']
    
    customer_account_col = None
    usage_account_col = None
    customer_region_col = None
    
    for col in customer_df.columns:
        for keyword in account_keywords:
            if keyword in str(col):
                customer_account_col = col
                break
        if customer_account_col:
            break
    
    for col in usage_df.columns:
        for keyword in account_keywords:
            if keyword in str(col):
                usage_account_col = col
                break
        if usage_account_col:
            break
    
    for col in customer_df.columns:
        for keyword in region_keywords:
            if keyword in str(col):
                customer_region_col = col
                break
        if customer_region_col:
            break
    
    return customer_account_col, usage_account_col, customer_region_col

def merge_data(customer_df, usage_df, customer_account_col, usage_account_col, customer_region_col):
    """合并数据"""
    try:
        customer_mapping = customer_df.set_index(customer_account_col)[customer_region_col].to_dict()
        usage_df['地区'] = usage_df[usage_account_col].map(customer_mapping)
        
        matched_count = usage_df['地区'].notna().sum()
        total_count = len(usage_df)
        
        print(f"匹配结果: {matched_count}/{total_count} ({matched_count/total_count*100:.2f}%)")
        
        unmatched_df = usage_df[usage_df['地区'].isna()]
        if len(unmatched_df) > 0:
            unique_unmatched = unmatched_df[usage_account_col].unique()
            print(f"未匹配户号: {len(unique_unmatched)} 个")
            for account in unique_unmatched:
                count = len(unmatched_df[unmatched_df[usage_account_col] == account])
                print(f"  {account} ({count}次)")
        
        return usage_df
        
    except Exception as e:
        print(f"错误: 合并数据时发生异常 - {e}")
        return None

def process_file(customer_file, usage_file, output_file):
    """处理单个文件"""
    print(f"\n{'='*60}")
    print(f"处理文件: {os.path.basename(usage_file)}")
    print(f"{'='*60}")
    
    if not os.path.exists(usage_file):
        print(f"错误: 用电量文件不存在 - {usage_file}")
        return False
    
    customer_df, usage_df = read_excel_files(customer_file, usage_file)
    if customer_df is None or usage_df is None:
        return False
    
    customer_account_col, usage_account_col, customer_region_col = find_matching_columns(customer_df, usage_df)
    
    if not all([customer_account_col, usage_account_col, customer_region_col]):
        print("错误: 无法识别必要的列名")
        return False
    
    merged_df = merge_data(customer_df, usage_df, customer_account_col, usage_account_col, customer_region_col)
    if merged_df is None:
        return False
    
    try:
        merged_df.to_excel(output_file, index=False)
        print(f"结果已保存到: {output_file}")
        return True
    except Exception as e:
        print(f"错误: 保存文件失败 - {e}")
        return False

def main():
    """主函数"""
    print("批量Excel文件合并工具")
    print("=" * 60)
    
    # 客户文件路径
    customer_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/售电签约客户(2).xlsx"
    
    # 检查客户文件是否存在
    if not os.path.exists(customer_file):
        print(f"错误: 客户文件不存在 - {customer_file}")
        return
    
    # 定义要处理的用电量文件
    base_path = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/"
    output_base = "/Users/<USER>/RiderProjects/Solution3/"
    
    files_to_process = [
        {
            "usage_file": base_path + "110kV以下用户历史用电量信息查询20250601-20250630(1).xlsx",
            "output_file": output_base + "合并结果_6月用电量信息含地区.xlsx",
            "description": "6月份用电量数据"
        },
        {
            "usage_file": base_path + "110kV以下用户历史用电量信息查询20250701-20250713(1).xlsx", 
            "output_file": output_base + "合并结果_7月用电量信息含地区.xlsx",
            "description": "7月份用电量数据"
        }
    ]
    
    success_count = 0
    total_count = len(files_to_process)
    
    for file_info in files_to_process:
        success = process_file(
            customer_file, 
            file_info["usage_file"], 
            file_info["output_file"]
        )
        if success:
            success_count += 1
    
    print(f"\n{'='*60}")
    print(f"批量处理完成!")
    print(f"成功处理: {success_count}/{total_count} 个文件")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
