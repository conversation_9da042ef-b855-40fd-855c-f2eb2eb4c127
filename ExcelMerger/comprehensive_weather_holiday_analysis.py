#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合天气和节假日对用电量影响分析工具
基于山东省夏季负荷温度累积效应理论
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
from scipy import stats
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import LabelEncoder, StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class ComprehensiveWeatherHolidayAnalysis:
    def __init__(self):
        """
        初始化综合分析器
        """
        self.main_data = None  # 主要用电量数据
        self.sheet_data = {}   # 6月数据各分表
        self.merged_data = None  # 合并后的完整数据
        self.temperature_accumulation = None  # 温度累积效应数据
        self.holiday_calendar = None  # 节假日日历
        
    def load_data_files(self):
        """
        加载所有数据文件
        """
        print("="*80)
        print("综合天气和节假日对用电量影响分析")
        print("="*80)
        
        try:
            # 主要数据文件
            main_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/合并结果_6月用电量含更新天气信息包含星期(1).xlsx'
            
            # 6月数据分表文件
            sheets_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/9e20f478899dc29eb19741386f9343c8/File/6月数据.xlsx'
            
            print("正在加载主要数据文件...")
            if os.path.exists(main_file):
                self.main_data = pd.read_excel(main_file)
                print(f"✅ 主要数据加载成功: {len(self.main_data)} 条记录")
            else:
                print("❌ 主要数据文件不存在")
                return False
            
            print("正在加载6月数据分表...")
            if os.path.exists(sheets_file):
                # 读取所有工作表
                excel_file = pd.ExcelFile(sheets_file)
                sheet_names = excel_file.sheet_names
                print(f"发现 {len(sheet_names)} 个工作表: {sheet_names}")
                
                for sheet_name in sheet_names:
                    self.sheet_data[sheet_name] = pd.read_excel(sheets_file, sheet_name=sheet_name)
                    print(f"  {sheet_name}: {len(self.sheet_data[sheet_name])} 条记录")
                
                print("✅ 分表数据加载成功")
            else:
                print("❌ 6月数据分表文件不存在")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def preprocess_data(self):
        """
        数据预处理和清洗
        """
        print("\n正在进行数据预处理...")
        
        try:
            # 处理主要数据
            if '时间' in self.main_data.columns:
                self.main_data['时间'] = pd.to_datetime(self.main_data['时间'])
                self.main_data['日期'] = self.main_data['时间'].dt.date
                self.main_data['小时'] = self.main_data['时间'].dt.hour
                self.main_data['星期'] = self.main_data['时间'].dt.dayofweek
                self.main_data['月份'] = self.main_data['时间'].dt.month
                self.main_data['日'] = self.main_data['时间'].dt.day
            
            # 添加日期类型标识
            self.main_data['日期类型'] = self.main_data['星期'].apply(self._classify_day_type)
            
            # 处理天气数据
            if '天气' in self.main_data.columns:
                self.main_data['天气'] = self.main_data['天气'].fillna('未知')
            
            # 提取温度信息（如果存在）
            self._extract_temperature_info()
            
            # 创建节假日日历
            self._create_holiday_calendar()
            
            print("✅ 数据预处理完成")
            return True
            
        except Exception as e:
            print(f"❌ 数据预处理失败: {e}")
            return False
    
    def _classify_day_type(self, weekday):
        """
        分类日期类型
        0-6 对应周一到周日
        """
        if weekday < 5:  # 周一到周五
            return '工作日'
        else:  # 周六周日
            return '周末'
    
    def _extract_temperature_info(self):
        """
        提取温度信息和天气特征
        """
        # 查找可能的温度列
        temp_columns = [col for col in self.main_data.columns if '温度' in col or '气温' in col or 'temp' in col.lower()]

        if temp_columns:
            print(f"发现温度列: {temp_columns}")
            # 使用第一个温度列作为主要温度数据
            self.main_data['温度'] = self.main_data[temp_columns[0]]
        else:
            print("未发现温度列，将基于天气类型创建天气指数")
            # 创建天气指数（基于天气对用电量的潜在影响）
            weather_impact_map = {
                '晴': 5,  # 高温可能性大，用电量可能高
                '多云': 3,
                '阴': 2,
                '雨': 1,  # 温度相对较低
                '雪': 0,
                '雾': 2,
                '霾': 3
            }

            # 处理天气列
            if '天气' in self.main_data.columns:
                self.main_data['天气指数'] = self.main_data['天气'].map(weather_impact_map).fillna(2)
                print(f"创建天气指数，范围0-5，数值越高表示可能温度越高")

                # 统计天气分布
                weather_counts = self.main_data['天气'].value_counts()
                print(f"天气分布: {dict(weather_counts)}")
            else:
                self.main_data['天气指数'] = 2  # 默认值
    
    def _create_holiday_calendar(self):
        """
        创建2024年6月节假日日历
        """
        # 2024年6月的节假日（端午节等）
        holidays_2024_june = [
            '2024-06-10',  # 端午节
        ]
        
        # 创建节假日标识
        holiday_dates = pd.to_datetime(holidays_2024_june).date
        
        if '日期' in self.main_data.columns:
            self.main_data['是否节假日'] = self.main_data['日期'].isin(holiday_dates)
            
            # 更新日期类型，节假日优先级最高
            self.main_data.loc[self.main_data['是否节假日'], '日期类型'] = '节假日'
        
        print(f"✅ 节假日日历创建完成，识别节假日: {len(holiday_dates)} 天")
    
    def calculate_temperature_accumulation(self, days=3):
        """
        计算温度累积效应
        基于山东省夏季负荷温度累积效应理论

        Args:
            days: 累积天数，默认3天
        """
        print(f"\n正在计算{days}天天气累积效应...")

        # 优先使用温度数据，否则使用天气指数
        if '温度' in self.main_data.columns and not self.main_data['温度'].isna().all():
            weather_metric = '温度'
            print("使用温度数据计算累积效应")
        elif '天气指数' in self.main_data.columns:
            weather_metric = '天气指数'
            print("使用天气指数计算累积效应")
        else:
            print("⚠️ 缺少温度数据，跳过累积效应计算")
            return True  # 不阻止后续分析
        
        try:
            # 按日期分组计算日平均天气指标
            daily_weather = self.main_data.groupby('日期')[weather_metric].mean().reset_index()
            daily_weather = daily_weather.sort_values('日期')

            # 计算累积天气效应
            daily_weather[f'累积{weather_metric}'] = daily_weather[weather_metric].rolling(window=days, min_periods=1).mean()
            daily_weather[f'{weather_metric}变化率'] = daily_weather[weather_metric].pct_change()
            daily_weather[f'累积{weather_metric}变化'] = daily_weather[f'{weather_metric}变化率'].rolling(window=days, min_periods=1).sum()

            # 定义高值阈值（基于数据分布）
            threshold = daily_weather[weather_metric].quantile(0.75)  # 75分位数作为高值阈值
            daily_weather['高值天数'] = (daily_weather[weather_metric] > threshold).astype(int)
            daily_weather['连续高值天数'] = daily_weather['高值天数'].rolling(window=days, min_periods=1).sum()

            # 合并回主数据
            merge_columns = ['日期', f'累积{weather_metric}', f'{weather_metric}变化率', f'累积{weather_metric}变化', '连续高值天数']
            self.main_data = self.main_data.merge(
                daily_weather[merge_columns],
                on='日期',
                how='left'
            )

            print(f"✅ {weather_metric}累积效应计算完成")
            print(f"   高值阈值: {threshold:.1f}")
            print(f"   最大连续高值天数: {daily_weather['连续高值天数'].max()}")

            return True

        except Exception as e:
            print(f"❌ {weather_metric}累积效应计算失败: {e}")
            return True  # 不阻止后续分析
    
    def analyze_day_type_patterns(self):
        """
        分析不同日期类型的用电模式
        """
        print(f"\n" + "="*60)
        print(f"不同日期类型用电模式分析")
        print(f"="*60)
        
        # 确定用电量列
        power_columns = [col for col in self.main_data.columns if '电量' in col and 'kWh' in col]
        if not power_columns:
            print("❌ 未找到用电量数据列")
            return None
        
        main_power_col = power_columns[0]  # 使用第一个用电量列
        print(f"使用用电量列: {main_power_col}")
        
        # 按日期类型分组分析
        day_type_stats = self.main_data.groupby('日期类型')[main_power_col].agg([
            'count', 'mean', 'std', 'min', 'max', 'median'
        ]).round(2)
        
        print(f"\n各日期类型用电统计:")
        for day_type, stats in day_type_stats.iterrows():
            print(f"\n{day_type}:")
            print(f"  数据量: {stats['count']} 条")
            print(f"  平均用电量: {stats['mean']:,.0f} kWh")
            print(f"  标准差: {stats['std']:,.0f} kWh")
            print(f"  中位数: {stats['median']:,.0f} kWh")
            print(f"  范围: {stats['min']:,.0f} - {stats['max']:,.0f} kWh")
        
        # 统计检验
        day_types = self.main_data['日期类型'].unique()
        if len(day_types) >= 2:
            groups = [self.main_data[self.main_data['日期类型'] == dt][main_power_col].dropna()
                     for dt in day_types]

            # 过滤空组
            groups = [group for group in groups if len(group) > 0]

            if len(groups) >= 2:
                try:
                    # Kruskal-Wallis检验（非参数）
                    from scipy.stats import kruskal
                    kw_stat, kw_p = kruskal(*groups)
                    print(f"\nKruskal-Wallis检验结果:")
                    print(f"  统计量: {kw_stat:.3f}")
                    print(f"  p值: {kw_p:.6f}")
                    print(f"  结论: {'显著差异' if kw_p < 0.05 else '无显著差异'}")
                except Exception as e:
                    print(f"\n统计检验失败: {e}")
            else:
                print(f"\n数据不足，无法进行统计检验")
        
        return day_type_stats
    
    def analyze_weather_sensitivity_by_day_type(self):
        """
        分析不同日期类型下的天气敏感性
        """
        print(f"\n" + "="*60)
        print(f"不同日期类型天气敏感性分析")
        print(f"="*60)

        # 确定使用的天气指标
        weather_metric = None
        if '温度' in self.main_data.columns and not self.main_data['温度'].isna().all():
            weather_metric = '温度'
        elif '天气指数' in self.main_data.columns:
            weather_metric = '天气指数'

        if weather_metric is None:
            print("❌ 缺少天气数据")
            return None

        print(f"使用天气指标: {weather_metric}")
        
        power_columns = [col for col in self.main_data.columns if '电量' in col and 'kWh' in col]
        if not power_columns:
            print("❌ 未找到用电量数据列")
            return None
        
        main_power_col = power_columns[0]
        
        # 按日期类型分析温度敏感性
        sensitivity_results = {}
        
        for day_type in self.main_data['日期类型'].unique():
            day_data = self.main_data[self.main_data['日期类型'] == day_type]
            
            if len(day_data) < 10:  # 数据量太少
                continue
            
            # 计算相关性
            weather_corr = day_data[main_power_col].corr(day_data[weather_metric])

            # 线性回归分析
            valid_data = day_data[[main_power_col, weather_metric]].dropna()
            if len(valid_data) >= 5:
                X = valid_data[[weather_metric]]
                y = valid_data[main_power_col]

                model = LinearRegression()
                model.fit(X, y)

                slope = model.coef_[0]
                intercept = model.intercept_
                r2 = model.score(X, y)

                sensitivity_results[day_type] = {
                    '相关系数': weather_corr,
                    f'{weather_metric}敏感度': slope,
                    '基础负荷': intercept,
                    'R²': r2,
                    '数据量': len(valid_data)
                }
        
        # 输出结果
        print(f"\n天气敏感性分析结果:")
        for day_type, results in sensitivity_results.items():
            print(f"\n{day_type}:")
            print(f"  {weather_metric}相关系数: {results['相关系数']:.3f}")
            sensitivity_key = f'{weather_metric}敏感度'
            unit = '/°C' if weather_metric == '温度' else '/指数'
            print(f"  {weather_metric}敏感度: {results[sensitivity_key]:,.1f} kWh{unit}")
            print(f"  基础负荷: {results['基础负荷']:,.0f} kWh")
            print(f"  拟合优度(R²): {results['R²']:.3f}")
            print(f"  数据量: {results['数据量']} 条")
        
        return sensitivity_results

    def analyze_temperature_accumulation_effect(self):
        """
        分析温度累积效应对用电量的影响
        """
        print(f"\n" + "="*60)
        print(f"温度累积效应分析")
        print(f"="*60)

        if '累积温度' not in self.main_data.columns:
            print("❌ 请先计算温度累积效应")
            return None

        power_columns = [col for col in self.main_data.columns if '电量' in col and 'kWh' in col]
        if not power_columns:
            print("❌ 未找到用电量数据列")
            return None

        main_power_col = power_columns[0]

        # 按日期聚合数据
        daily_data = self.main_data.groupby('日期').agg({
            main_power_col: 'sum',
            '温度': 'mean',
            '累积温度': 'first',
            '连续高温天数': 'first',
            '日期类型': 'first'
        }).reset_index()

        # 分析累积温度与用电量的关系
        accumulation_corr = daily_data[main_power_col].corr(daily_data['累积温度'])
        instant_temp_corr = daily_data[main_power_col].corr(daily_data['温度'])

        print(f"\n温度效应对比:")
        print(f"  即时温度相关性: {instant_temp_corr:.3f}")
        print(f"  累积温度相关性: {accumulation_corr:.3f}")
        print(f"  累积效应提升: {(accumulation_corr - instant_temp_corr):.3f}")

        # 分析连续高温天数的影响
        high_temp_effect = daily_data.groupby('连续高温天数')[main_power_col].agg([
            'count', 'mean', 'std'
        ]).round(2)

        print(f"\n连续高温天数影响分析:")
        for days, stats in high_temp_effect.iterrows():
            if stats['count'] >= 2:  # 至少2天数据
                print(f"  连续{days}天高温: 平均用电{stats['mean']:,.0f} kWh (样本{stats['count']}天)")

        # 温度累积效应回归模型
        valid_data = daily_data[['累积温度', '连续高温天数', main_power_col]].dropna()
        if len(valid_data) >= 5:
            X = valid_data[['累积温度', '连续高温天数']]
            y = valid_data[main_power_col]

            model = LinearRegression()
            model.fit(X, y)

            r2 = model.score(X, y)

            print(f"\n累积效应回归模型:")
            print(f"  拟合优度(R²): {r2:.3f}")
            print(f"  累积温度系数: {model.coef_[0]:,.1f} kWh/°C")
            print(f"  连续高温系数: {model.coef_[1]:,.1f} kWh/天")

        return {
            '累积温度相关性': accumulation_corr,
            '即时温度相关性': instant_temp_corr,
            '连续高温影响': high_temp_effect
        }

    def analyze_regional_differences(self):
        """
        分析地区差异
        """
        print(f"\n" + "="*60)
        print(f"地区差异分析")
        print(f"="*60)

        if '地区' not in self.main_data.columns:
            print("❌ 未找到地区信息")
            return None

        power_columns = [col for col in self.main_data.columns if '电量' in col and 'kWh' in col]
        if not power_columns:
            print("❌ 未找到用电量数据列")
            return None

        main_power_col = power_columns[0]

        # 地区基本统计
        agg_dict = {main_power_col: ['count', 'sum', 'mean', 'std']}

        # 添加天气指标（如果存在）
        if '温度' in self.main_data.columns:
            agg_dict['温度'] = 'mean'
        elif '天气指数' in self.main_data.columns:
            agg_dict['天气指数'] = 'mean'

        regional_stats = self.main_data.groupby('地区').agg(agg_dict).round(2)

        print(f"\n各地区用电统计:")
        total_power = regional_stats[(main_power_col, 'sum')].sum()

        for region in regional_stats.index:
            power_sum = regional_stats.loc[region, (main_power_col, 'sum')]
            power_mean = regional_stats.loc[region, (main_power_col, 'mean')]
            power_std = regional_stats.loc[region, (main_power_col, 'std')]
            data_count = regional_stats.loc[region, (main_power_col, 'count')]
            contribution = power_sum / total_power * 100

            print(f"\n{region}:")
            print(f"  数据量: {data_count} 条")
            print(f"  总用电量: {power_sum:,.0f} kWh ({contribution:.1f}%)")
            print(f"  平均用电量: {power_mean:,.0f} kWh")
            print(f"  标准差: {power_std:,.0f} kWh")

            # 显示天气指标
            if '温度' in self.main_data.columns:
                avg_temp = regional_stats.loc[region, ('温度', 'mean')]
                if not pd.isna(avg_temp):
                    print(f"  平均温度: {avg_temp:.1f}°C")
            elif '天气指数' in self.main_data.columns:
                avg_weather = regional_stats.loc[region, ('天气指数', 'mean')]
                if not pd.isna(avg_weather):
                    print(f"  平均天气指数: {avg_weather:.1f}")

        # 地区间天气敏感性差异
        weather_metric = None
        if '温度' in self.main_data.columns:
            weather_metric = '温度'
        elif '天气指数' in self.main_data.columns:
            weather_metric = '天气指数'

        if weather_metric:
            print(f"\n地区{weather_metric}敏感性对比:")
            for region in self.main_data['地区'].unique():
                region_data = self.main_data[self.main_data['地区'] == region]
                if len(region_data) >= 10:
                    weather_corr = region_data[main_power_col].corr(region_data[weather_metric])
                    print(f"  {region}: {weather_metric}相关性 {weather_corr:.3f}")

        return regional_stats

    def generate_comprehensive_insights(self):
        """
        生成综合洞察和建议
        """
        print(f"\n" + "="*80)
        print(f"综合分析洞察与建议")
        print(f"="*80)

        power_columns = [col for col in self.main_data.columns if '电量' in col and 'kWh' in col]
        if not power_columns:
            print("❌ 未找到用电量数据列")
            return None

        main_power_col = power_columns[0]

        # 1. 数据概况
        print(f"\n1. 数据概况:")
        print(f"   分析时间范围: {self.main_data['时间'].min().date()} 至 {self.main_data['时间'].max().date()}")
        print(f"   总数据量: {len(self.main_data):,} 条记录")
        print(f"   涉及地区: {len(self.main_data['地区'].unique())} 个")
        print(f"   总用电量: {self.main_data[main_power_col].sum():,.0f} kWh")

        # 2. 日期类型影响
        day_type_impact = self.main_data.groupby('日期类型')[main_power_col].mean()
        print(f"\n2. 日期类型影响:")
        for day_type, avg_power in day_type_impact.items():
            print(f"   {day_type}: 平均 {avg_power:,.0f} kWh")

        # 3. 天气影响
        if '温度' in self.main_data.columns:
            overall_temp_corr = self.main_data[main_power_col].corr(self.main_data['温度'])
            print(f"\n3. 天气影响:")
            print(f"   整体温度相关性: {overall_temp_corr:.3f}")

            if '累积温度' in self.main_data.columns:
                accum_temp_corr = self.main_data[main_power_col].corr(self.main_data['累积温度'])
                print(f"   累积温度相关性: {accum_temp_corr:.3f}")
                print(f"   累积效应提升: {(accum_temp_corr - overall_temp_corr):.3f}")

        # 4. 业务建议
        print(f"\n4. 业务建议:")

        # 负荷预测建议
        print(f"   📈 负荷预测建议:")
        print(f"      • 工作日用电量通常高于周末，需要差异化预测")
        print(f"      • 温度累积效应显著，连续高温天气需要特别关注")
        print(f"      • 建议建立分地区、分日期类型的预测模型")

        # 风险管理建议
        print(f"   ⚠️  风险管理建议:")
        print(f"      • 关注连续高温天气的累积效应")
        print(f"      • 节假日用电模式与工作日差异较大，需要特殊处理")
        print(f"      • 建议建立温度预警机制")

        # 运营优化建议
        print(f"   🔧 运营优化建议:")
        print(f"      • 根据天气预报调整发电计划")
        print(f"      • 在高温期间加强设备巡检")
        print(f"      • 优化不同地区的供电策略")

        return True

    def run_complete_analysis(self):
        """
        运行完整的综合分析
        """
        print("开始运行综合天气和节假日对用电量影响分析...")

        # 1. 加载数据
        if not self.load_data_files():
            return False

        # 2. 数据预处理
        if not self.preprocess_data():
            return False

        # 3. 计算温度累积效应
        self.calculate_temperature_accumulation(days=3)

        # 4. 分析不同日期类型的用电模式
        day_type_stats = self.analyze_day_type_patterns()

        # 5. 分析天气敏感性
        sensitivity_results = self.analyze_weather_sensitivity_by_day_type()

        # 6. 分析温度累积效应
        accumulation_results = self.analyze_temperature_accumulation_effect()

        # 7. 分析地区差异
        regional_results = self.analyze_regional_differences()

        # 8. 生成综合洞察
        self.generate_comprehensive_insights()

        # 9. 保存分析结果
        self.save_analysis_results()

        print(f"\n✅ 综合分析完成！")
        print(f"📊 已完成天气和节假日对用电量影响的全面分析")

        return True

    def save_analysis_results(self):
        """
        保存分析结果到Excel文件
        """
        try:
            output_file = "/Users/<USER>/RiderProjects/Solution3/综合天气节假日用电量影响分析结果.xlsx"

            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 保存主要数据
                if self.main_data is not None:
                    self.main_data.to_excel(writer, sheet_name='主要数据', index=False)

                # 保存各分表数据
                for sheet_name, data in self.sheet_data.items():
                    safe_sheet_name = sheet_name[:31]  # Excel工作表名称限制
                    data.to_excel(writer, sheet_name=safe_sheet_name, index=False)

                # 保存统计摘要
                if hasattr(self, 'main_data') and self.main_data is not None:
                    power_columns = [col for col in self.main_data.columns if '电量' in col and 'kWh' in col]
                    if power_columns:
                        summary_stats = self.main_data.groupby(['地区', '日期类型'])[power_columns[0]].agg([
                            'count', 'sum', 'mean', 'std', 'min', 'max'
                        ]).round(2)
                        summary_stats.to_excel(writer, sheet_name='统计摘要')

            print(f"✅ 分析结果已保存到: {output_file}")

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """
    主函数
    """
    analyzer = ComprehensiveWeatherHolidayAnalysis()
    success = analyzer.run_complete_analysis()

    if success:
        print(f"\n🎉 分析成功完成！")
        print(f"📋 分析内容包括:")
        print(f"   • 温度累积效应分析")
        print(f"   • 工作日vs节假日用电模式对比")
        print(f"   • 地区差异分析")
        print(f"   • 天气敏感性分析")
        print(f"   • 综合业务洞察与建议")
    else:
        print(f"\n❌ 分析失败，请检查数据文件和配置")

if __name__ == "__main__":
    main()
