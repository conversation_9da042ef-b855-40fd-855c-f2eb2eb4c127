#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将售电签约客户表中的地区信息合并到110kV以下用电量表中
专门处理2025-07-14的数据
"""

import pandas as pd
import os
from datetime import datetime

def main():
    """主函数"""
    print("地区信息合并工具 - 2025-07-14数据")
    print("=" * 50)
    
    # 文件路径
    customer_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/售电签约客户(2).xlsx"
    usage_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/110kV以下********-********.xlsx"
    output_file = "/Users/<USER>/RiderProjects/Solution3/合并结果_********用电量信息含地区.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(customer_file):
        print(f"错误: 售电签约客户文件不存在")
        print(f"路径: {customer_file}")
        return False
    
    if not os.path.exists(usage_file):
        print(f"错误: 用电量文件不存在")
        print(f"路径: {usage_file}")
        return False
    
    try:
        # 读取售电签约客户文件
        print(f"正在读取售电签约客户文件...")
        customer_df = pd.read_excel(customer_file)
        print(f"售电签约客户文件读取成功，共 {len(customer_df)} 行数据")
        print(f"客户文件列名: {list(customer_df.columns)}")
        
        # 读取用电量文件
        print(f"正在读取用电量文件...")
        usage_df = pd.read_excel(usage_file)
        print(f"用电量文件读取成功，共 {len(usage_df)} 行数据")
        print(f"用电量文件列名: {list(usage_df.columns)}")
        
        # 显示数据概况
        print(f"\n=== 数据概况 ===")
        print(f"客户文件中的地区分布:")
        customer_regions = customer_df['地区'].value_counts()
        print(customer_regions)
        
        print(f"\n客户文件中的唯一户号数量: {customer_df['户号'].nunique()}")
        print(f"用电量文件中的唯一户号数量: {usage_df['户号'].nunique()}")
        
        # 检查户号格式
        print(f"\n=== 户号格式检查 ===")
        print("客户文件户号样例:")
        print(customer_df['户号'].head().tolist())
        print("用电量文件户号样例:")
        print(usage_df['户号'].head().tolist())
        
        # 数据预处理 - 确保户号格式一致
        print(f"\n正在预处理数据...")
        
        # 将户号转换为字符串格式，去除科学计数法
        customer_df['户号_str'] = customer_df['户号'].apply(lambda x: str(int(float(x))) if pd.notna(x) else None)
        usage_df['户号_str'] = usage_df['户号'].apply(lambda x: str(int(float(x))) if pd.notna(x) else None)
        
        print("预处理后的户号样例:")
        print("客户文件:", customer_df['户号_str'].head().tolist())
        print("用电量文件:", usage_df['户号_str'].head().tolist())
        
        # 创建户号到地区的映射
        print(f"\n正在创建户号-地区映射...")
        
        # 处理重复户号，保留第一个地区信息
        customer_mapping = customer_df.drop_duplicates(subset=['户号_str']).set_index('户号_str')['地区'].to_dict()
        
        print(f"户号-地区映射创建完成，共 {len(customer_mapping)} 个条目")
        
        # 合并地区信息
        print(f"正在合并地区信息...")
        usage_df['地区'] = usage_df['户号_str'].map(customer_mapping)
        
        # 统计匹配情况
        matched_count = usage_df['地区'].notna().sum()
        total_count = len(usage_df)
        
        print(f"\n=== 匹配结果 ===")
        print(f"总记录数: {total_count}")
        print(f"成功匹配地区: {matched_count}")
        print(f"未匹配地区: {total_count - matched_count}")
        print(f"地区匹配率: {matched_count/total_count*100:.2f}%")
        
        # 显示匹配后的地区分布
        if matched_count > 0:
            print(f"\n匹配后的地区分布:")
            matched_regions = usage_df['地区'].value_counts()
            print(matched_regions)
        
        # 分析未匹配的记录
        unmatched_df = usage_df[usage_df['地区'].isna()]
        if len(unmatched_df) > 0:
            print(f"\n=== 未匹配记录分析 ===")
            unmatched_accounts = unmatched_df['户号_str'].unique()
            print(f"未匹配的唯一户号数量: {len(unmatched_accounts)}")
            print("未匹配的户号样例:")
            for account in unmatched_accounts[:10]:  # 显示前10个
                count = len(unmatched_df[unmatched_df['户号_str'] == account])
                print(f"  户号: {account} (出现 {count} 次)")
            
            if len(unmatched_accounts) > 10:
                print(f"  ... 还有 {len(unmatched_accounts) - 10} 个未匹配的户号")
        
        # 清理数据并保存
        print(f"\n正在保存结果...")
        
        # 删除辅助列
        result_df = usage_df.drop(columns=['户号_str'])
        
        # 重新排列列的顺序，将地区列放在合适的位置
        cols = list(result_df.columns)
        if '地区' in cols:
            cols.remove('地区')
            # 在户号列后面插入地区列
            if '户号' in cols:
                account_index = cols.index('户号')
                cols.insert(account_index + 1, '地区')
            else:
                cols.append('地区')
        
        result_df = result_df[cols]
        
        # 保存到Excel文件
        result_df.to_excel(output_file, index=False)
        print(f"结果已保存到: {output_file}")
        
        # 显示预览
        print(f"\n=== 数据预览 (前10行) ===")
        preview_cols = ['户号', '地区', '时间', '总电量(kWh)']
        available_cols = [col for col in preview_cols if col in result_df.columns]
        print(result_df[available_cols].head(10))
        
        # 显示统计信息
        print(f"\n=== 最终统计 ===")
        print(f"总记录数: {len(result_df)}")
        print(f"有地区信息的记录数: {result_df['地区'].notna().sum()}")
        print(f"无地区信息的记录数: {result_df['地区'].isna().sum()}")
        
        if result_df['地区'].notna().sum() > 0:
            print(f"\n最终地区分布:")
            final_regions = result_df['地区'].value_counts()
            print(final_regions)
        
        print(f"\n处理完成！")
        return True
        
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_account_overlap():
    """分析两个文件中户号的重叠情况"""
    print("\n=== 户号重叠分析 ===")
    
    customer_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/售电签约客户(2).xlsx"
    usage_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/110kV以下********-********.xlsx"
    
    try:
        customer_df = pd.read_excel(customer_file)
        usage_df = pd.read_excel(usage_file)
        
        # 标准化户号格式
        customer_accounts = set(customer_df['户号'].apply(lambda x: str(int(float(x))) if pd.notna(x) else None).dropna())
        usage_accounts = set(usage_df['户号'].apply(lambda x: str(int(float(x))) if pd.notna(x) else None).dropna())
        
        print(f"客户文件中的唯一户号数量: {len(customer_accounts)}")
        print(f"用电量文件中的唯一户号数量: {len(usage_accounts)}")
        
        # 计算重叠
        overlap = customer_accounts.intersection(usage_accounts)
        customer_only = customer_accounts - usage_accounts
        usage_only = usage_accounts - customer_accounts
        
        print(f"重叠的户号数量: {len(overlap)}")
        print(f"仅在客户文件中的户号数量: {len(customer_only)}")
        print(f"仅在用电量文件中的户号数量: {len(usage_only)}")
        print(f"重叠率: {len(overlap)/len(usage_accounts)*100:.2f}%")
        
        if len(usage_only) > 0:
            print(f"\n仅在用电量文件中的户号样例:")
            for account in list(usage_only)[:10]:
                print(f"  {account}")
        
    except Exception as e:
        print(f"分析过程中发生错误: {e}")

if __name__ == "__main__":
    # 先进行户号重叠分析
    analyze_account_overlap()
    
    # 然后执行主要的合并操作
    main()
