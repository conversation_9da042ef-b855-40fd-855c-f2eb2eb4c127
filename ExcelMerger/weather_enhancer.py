#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气信息增强工具
为Excel文件中的地区数据添加详细的天气信息，专注于影响用电的关键天气参数
"""

import pandas as pd
import requests
import json
import time
import os
from datetime import datetime, timedelta

class WeatherEnhancer:
    def __init__(self, api_key="3f8b89c1952b3df138580d523d69b2f9"):
        """
        初始化天气增强器

        Args:
            api_key: OpenWeatherMap API密钥
        """
        self.api_key = api_key
        self.base_url = "http://api.openweathermap.org/data/2.5/weather"
        self.forecast_url = "http://api.openweathermap.org/data/2.5/forecast"

        # 中国主要城市的地区映射（更新了海宁的映射）
        self.city_mapping = {
            '衢州': 'Quzhou,CN',
            '诸暨': 'Zhuji,CN',
            '温州': 'Wenzhou,CN',
            '杭州': 'Hangzhou,CN',
            '宁波': 'Ningbo,CN',
            '嘉兴': 'Jiaxing,CN',
            '湖州': 'Huzhou,CN',
            '绍兴': 'Shaoxing,CN',
            '金华': 'Jinhua,CN',
            '台州': 'Taizhou,CN',
            '丽水': 'Lishui,CN',
            '海宁': 'Haining,CN'  # 添加海宁映射
        }
        
        # 模拟天气数据（当没有API密钥时使用）
        self.mock_weather_data = {
            '衢州': {'temperature': 28.5, 'humidity': 65, 'weather': '多云', 'wind_speed': 3.2},
            '诸暨': {'temperature': 29.1, 'humidity': 68, 'weather': '晴', 'wind_speed': 2.8},
            '温州': {'temperature': 30.2, 'humidity': 72, 'weather': '小雨', 'wind_speed': 4.1},
            '杭州': {'temperature': 31.0, 'humidity': 70, 'weather': '晴', 'wind_speed': 2.5},
            '宁波': {'temperature': 29.8, 'humidity': 69, 'weather': '多云', 'wind_speed': 3.5}
        }
    
    def get_weather_data(self, city_name, date_str=None):
        """
        获取指定城市的详细天气数据，专注于影响用电的关键参数

        Args:
            city_name: 城市名称
            date_str: 日期字符串（格式：YYYY-MM-DD）

        Returns:
            dict: 详细天气数据字典
        """
        if not self.api_key:
            return self.get_mock_weather_data(city_name)

        try:
            # 获取城市的英文名称
            city_en = self.city_mapping.get(city_name, city_name)

            # 构建API请求URL
            params = {
                'q': city_en,
                'appid': self.api_key,
                'units': 'metric',  # 使用摄氏度
                'lang': 'zh_cn'     # 中文描述
            }

            response = requests.get(self.base_url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()

                # 提取详细的天气信息，专注于影响用电的参数
                weather_info = {
                    # 基础温度信息
                    'temperature': round(data['main']['temp'], 1),
                    'feels_like': round(data['main']['feels_like'], 1),
                    'temp_min': round(data['main']['temp_min'], 1),
                    'temp_max': round(data['main']['temp_max'], 1),

                    # 湿度和气压
                    'humidity': data['main']['humidity'],
                    'pressure': data['main']['pressure'],

                    # 风力信息
                    'wind_speed': round(data.get('wind', {}).get('speed', 0), 1),
                    'wind_deg': data.get('wind', {}).get('deg', 0),
                    'wind_gust': round(data.get('wind', {}).get('gust', 0), 1),

                    # 天气状况
                    'weather_main': data['weather'][0]['main'],
                    'weather_description': data['weather'][0]['description'],
                    'weather_id': data['weather'][0]['id'],

                    # 云量和能见度
                    'cloudiness': data.get('clouds', {}).get('all', 0),
                    'visibility': data.get('visibility', 10000) / 1000,  # 转换为公里

                    # 降水信息
                    'rain_1h': data.get('rain', {}).get('1h', 0),
                    'snow_1h': data.get('snow', {}).get('1h', 0),

                    # 日照信息
                    'sunrise': datetime.fromtimestamp(data['sys']['sunrise']).strftime('%H:%M'),
                    'sunset': datetime.fromtimestamp(data['sys']['sunset']).strftime('%H:%M'),

                    # 数据时间
                    'data_time': datetime.fromtimestamp(data['dt']).strftime('%Y-%m-%d %H:%M:%S')
                }

                # 计算日照时长（小时）
                sunrise_ts = data['sys']['sunrise']
                sunset_ts = data['sys']['sunset']
                daylight_hours = round((sunset_ts - sunrise_ts) / 3600, 1)
                weather_info['daylight_hours'] = daylight_hours

                return weather_info

            else:
                print(f"API请求失败 {city_name}: {response.status_code}")
                return self.get_mock_weather_data(city_name)

        except Exception as e:
            print(f"获取天气数据失败 {city_name}: {e}")
            return self.get_mock_weather_data(city_name)
    
    def get_mock_weather_data(self, city_name):
        """
        获取模拟天气数据（当API调用失败时使用）

        Args:
            city_name: 城市名称

        Returns:
            dict: 模拟天气数据
        """
        # 如果城市在预定义数据中，返回对应数据
        if city_name in self.mock_weather_data:
            base_data = self.mock_weather_data[city_name].copy()
        else:
            base_data = {
                'temperature': 25.0,
                'humidity': 60,
                'weather': '晴',
                'wind_speed': 3.0,
                'pressure': 1013
            }

        # 扩展为详细的天气数据格式
        return {
            'temperature': base_data['temperature'],
            'feels_like': base_data['temperature'] + 1,
            'temp_min': base_data['temperature'] - 2,
            'temp_max': base_data['temperature'] + 3,
            'humidity': base_data['humidity'],
            'pressure': base_data['pressure'],
            'wind_speed': base_data['wind_speed'],
            'wind_deg': 180,
            'wind_gust': base_data['wind_speed'] * 1.5,
            'weather_main': 'Clear',
            'weather_description': base_data['weather'],
            'weather_id': 800,
            'cloudiness': 20,
            'visibility': 10.0,
            'rain_1h': 0,
            'snow_1h': 0,
            'sunrise': '06:00',
            'sunset': '18:00',
            'daylight_hours': 12.0,
            'data_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def enhance_excel_with_weather(self, input_file, output_file):
        """
        为Excel文件添加详细的天气信息，覆盖原有天气数据

        Args:
            input_file: 输入Excel文件路径
            output_file: 输出Excel文件路径
        """
        try:
            # 读取Excel文件
            print(f"正在读取文件: {input_file}")
            df = pd.read_excel(input_file)
            print(f"读取成功，共 {len(df)} 行数据")

            # 检查是否有地区列
            if '地区' not in df.columns:
                print("错误: 文件中没有找到'地区'列")
                return False

            # 删除现有的天气相关列（如果存在）
            weather_columns_to_remove = [
                '天气', '气温(°C)', '湿度(%)', '天气状况', '风速(m/s)', '气压(hPa)',
                '体感温度(°C)', '最低温度(°C)', '最高温度(°C)', '风向(度)',
                '阵风(m/s)', '云量(%)', '能见度(km)', '降雨量(mm/h)',
                '降雪量(mm/h)', '日出时间', '日落时间', '日照时长(h)', '数据时间'
            ]

            for col in weather_columns_to_remove:
                if col in df.columns:
                    df = df.drop(columns=[col])
                    print(f"已删除原有天气列: {col}")

            # 获取唯一的地区列表
            unique_regions = df['地区'].dropna().unique()
            print(f"发现 {len(unique_regions)} 个不同地区: {list(unique_regions)}")

            # 为每个地区获取天气数据
            weather_cache = {}
            for i, region in enumerate(unique_regions, 1):
                print(f"正在获取 {region} 的天气数据... ({i}/{len(unique_regions)})")
                weather_data = self.get_weather_data(region)
                weather_cache[region] = weather_data
                time.sleep(0.5)  # 避免API请求过于频繁

            print("\n正在添加详细天气信息到Excel...")

            # 添加详细的天气列
            df['气温(°C)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('temperature', None))
            df['体感温度(°C)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('feels_like', None))
            df['最低温度(°C)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('temp_min', None))
            df['最高温度(°C)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('temp_max', None))
            df['湿度(%)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('humidity', None))
            df['气压(hPa)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('pressure', None))
            df['风速(m/s)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('wind_speed', None))
            df['风向(度)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('wind_deg', None))
            df['阵风(m/s)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('wind_gust', None))
            df['天气状况'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('weather_description', None))
            df['云量(%)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('cloudiness', None))
            df['能见度(km)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('visibility', None))
            df['降雨量(mm/h)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('rain_1h', None))
            df['降雪量(mm/h)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('snow_1h', None))
            df['日出时间'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('sunrise', None))
            df['日落时间'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('sunset', None))
            df['日照时长(h)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('daylight_hours', None))
            df['数据时间'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('data_time', None))

            # 保存结果
            df.to_excel(output_file, index=False)
            print(f"结果已保存到: {output_file}")

            # 显示预览
            print(f"\n数据预览 (前3行):")
            weather_cols = ['地区', '气温(°C)', '体感温度(°C)', '湿度(%)', '天气状况', '风速(m/s)', '气压(hPa)', '云量(%)', '日照时长(h)']
            available_cols = [col for col in weather_cols if col in df.columns]
            print(df[available_cols].head(3))

            # 显示天气数据统计
            print(f"\n天气数据统计:")
            for region, data in weather_cache.items():
                print(f"{region}: {data['temperature']}°C, {data['weather_description']}, 湿度{data['humidity']}%, 风速{data['wind_speed']}m/s")

            return True

        except Exception as e:
            print(f"处理文件时发生错误: {e}")
            return False

def process_file(input_file, output_file):
    """处理指定的文件"""
    print("天气信息增强工具 - 详细版")
    print("=" * 60)
    print("使用OpenWeatherMap API获取详细的天气信息")
    print("专注于影响用电的关键天气参数")
    print("=" * 60)

    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在 - {input_file}")
        return False

    # 创建天气增强器（使用提供的API密钥）
    enhancer = WeatherEnhancer(api_key="3f8b89c1952b3df138580d523d69b2f9")

    print("使用OpenWeatherMap API获取实时天气数据")
    print("将覆盖原有的天气信息，添加详细的天气参数")
    print(f"\n输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print("\n开始处理...")

    # 处理文件
    success = enhancer.enhance_excel_with_weather(input_file, output_file)

    if success:
        print(f"\n✅ 处理完成！")
        print(f"📁 输出文件: {output_file}")
        print(f"\n📊 添加的天气信息包括:")
        print("   • 基础温度: 当前温度、体感温度、最高/最低温度")
        print("   • 大气条件: 湿度、气压、云量、能见度")
        print("   • 风力信息: 风速、风向、阵风")
        print("   • 降水信息: 降雨量、降雪量")
        print("   • 日照信息: 日出/日落时间、日照时长")
        print("   • 天气状况: 详细天气描述")
        print("\n这些参数对用电量分析具有重要参考价值！")
        return True
    else:
        print(f"\n❌ 处理失败！")
        return False

def main():
    """主函数"""
    # 默认处理第一个文件
    input_file = "/Users/<USER>/Desktop/副本合并结果_用电量信息含地区.xlsx"
    output_file = "/Users/<USER>/Desktop/副本合并结果_用电量信息含详细天气.xlsx"

    process_file(input_file, output_file)

if __name__ == "__main__":
    main()
