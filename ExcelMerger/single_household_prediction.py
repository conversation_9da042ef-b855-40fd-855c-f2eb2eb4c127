#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单户号单日用电量预测工具
预测指定户号在指定日期的用电量，自动爬取天气信息
"""

import pandas as pd
import numpy as np
import requests
from bs4 import BeautifulSoup
import re
import time
import random
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.linear_model import Ridge
import pickle
import os

class SingleHouseholdPredictor:
    def __init__(self):
        """
        初始化单户号预测器
        """
        self.data = None
        self.model = None
        self.scaler = None
        self.label_encoders = {}
        self.feature_columns = []
        
        # 城市URL映射
        self.city_url_map = {
            '衢州': 'quzhou', '诸暨': 'zhuji', '温州': 'wenzhou', '杭州': 'hangzhou',
            '宁波': 'ningbo', '嘉兴': 'jiaxing', '湖州': 'huzhou', '绍兴': 'shaoxing',
            '金华': 'jinhua', '台州': 'taizhou', '丽水': 'lishui', '海宁': 'haining'
        }
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://www.tianqi24.com/',
        }
    
    def load_trained_model(self):
        """
        加载已训练的模型和数据
        """
        print("正在加载训练数据和模型...")
        
        # 加载数据
        data_file = "/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx"
        if not os.path.exists(data_file):
            print(f"❌ 数据文件不存在: {data_file}")
            return False
        
        try:
            self.data = pd.read_excel(data_file, sheet_name='完整数据')
            print(f"✅ 成功加载数据: {len(self.data)} 条记录")
            
            # 数据预处理
            self.data['时间'] = pd.to_datetime(self.data['时间'])
            self.data['年'] = self.data['时间'].dt.year
            self.data['月'] = self.data['时间'].dt.month
            self.data['日'] = self.data['时间'].dt.day
            self.data['星期'] = self.data['时间'].dt.dayofweek
            self.data['是否周末'] = (self.data['星期'] >= 5).astype(int)
            
            # 准备特征工程
            self.prepare_features()
            
            # 训练简单模型
            self.train_simple_model()
            
            return True
            
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return False
    
    def prepare_features(self):
        """
        准备特征工程
        """
        print("正在进行特征工程...")
        
        # 基础特征
        feature_columns = [
            '年', '月', '日', '星期', '是否周末',
            '最高温度(°C)', '最低温度(°C)', 'AQI', '降水量(mm)', '湿度(%)', '气压(hPa)'
        ]
        
        # 处理分类特征
        categorical_features = ['地区', '公司名称', '白天天气', '晚上天气', '风向', '合同状态', '居间/销售']
        
        for col in categorical_features:
            if col in self.data.columns:
                le = LabelEncoder()
                self.data[f'{col}_编码'] = le.fit_transform(self.data[col].fillna('未知'))
                self.label_encoders[col] = le
                feature_columns.append(f'{col}_编码')
        
        # 创建温度相关特征
        self.data['温差'] = self.data['最高温度(°C)'] - self.data['最低温度(°C)']
        self.data['平均温度'] = (self.data['最高温度(°C)'] + self.data['最低温度(°C)']) / 2
        self.data['高温指标'] = (self.data['最高温度(°C)'] >= 30).astype(int)
        self.data['低温指标'] = (self.data['最低温度(°C)'] <= 10).astype(int)
        
        feature_columns.extend(['温差', '平均温度', '高温指标', '低温指标'])
        
        # 创建时间特征
        self.data['月份sin'] = np.sin(2 * np.pi * self.data['月'] / 12)
        self.data['月份cos'] = np.cos(2 * np.pi * self.data['月'] / 12)
        self.data['日期sin'] = np.sin(2 * np.pi * self.data['日'] / 31)
        self.data['日期cos'] = np.cos(2 * np.pi * self.data['日'] / 31)
        
        feature_columns.extend(['月份sin', '月份cos', '日期sin', '日期cos'])
        
        # 创建滞后特征
        self.data = self.data.sort_values(['户号', '时间'])
        self.data['前一天用电量'] = self.data.groupby('户号')['总电量(kWh)'].shift(1)
        self.data['前三天平均用电量'] = self.data.groupby('户号')['总电量(kWh)'].rolling(window=3).mean().reset_index(0, drop=True)
        
        feature_columns.extend(['前一天用电量', '前三天平均用电量'])
        
        # 移除缺失值
        self.data = self.data.dropna(subset=feature_columns + ['总电量(kWh)'])
        
        self.feature_columns = feature_columns
        print(f"✅ 特征工程完成: {len(feature_columns)} 个特征")
    
    def train_simple_model(self):
        """
        训练简单的岭回归模型
        """
        print("正在训练预测模型...")
        
        X = self.data[self.feature_columns]
        y = self.data['总电量(kWh)']
        
        # 数据标准化
        self.scaler = StandardScaler()
        X_scaled = self.scaler.fit_transform(X)
        
        # 训练岭回归模型
        self.model = Ridge(alpha=1.0)
        self.model.fit(X_scaled, y)
        
        # 评估模型
        score = self.model.score(X_scaled, y)
        print(f"✅ 模型训练完成，R² = {score:.3f}")
    
    def get_weather_data(self, city, date):
        """
        获取指定城市和日期的天气数据
        """
        print(f"正在获取 {city} {date} 的天气数据...")
        
        if city not in self.city_url_map:
            print(f"❌ 城市 {city} 不在支持列表中")
            return None
        
        # 解析日期
        date_obj = datetime.strptime(date, '%Y-%m-%d')
        year = date_obj.year
        month = date_obj.month
        day = date_obj.day
        
        city_url = self.city_url_map[city]
        url = f"https://www.tianqi24.com/{city_url}/history{year}{month:02d}.html"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=15)
            response.encoding = 'utf-8'
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                weather_data = self.parse_weather_data(soup, year, month, day)
                
                if weather_data:
                    print(f"✅ 成功获取天气数据")
                    return weather_data
                else:
                    print(f"⚠️ 未找到指定日期的天气数据，使用估算")
                    return self.estimate_weather_data(city, date_obj)
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return self.estimate_weather_data(city, date_obj)
                
        except Exception as e:
            print(f"❌ 获取天气数据失败: {e}")
            return self.estimate_weather_data(city, date_obj)
    
    def parse_weather_data(self, soup, year, month, day):
        """
        解析天气数据
        """
        weather_lists = soup.find_all('ul', class_='col6')
        
        for ul in weather_lists:
            items = ul.find_all('li')
            for item in items[1:]:  # 跳过表头
                divs = item.find_all('div')
                if len(divs) >= 7:
                    try:
                        date_text = divs[0].get_text().strip()
                        date_match = re.search(r'(\d{2})-(\d{2})', date_text)
                        
                        if date_match:
                            month_part = int(date_match.group(1))
                            day_part = int(date_match.group(2))
                            
                            if month_part == month and day_part == day:
                                # 解析天气信息
                                day_night_text = divs[1].get_text().strip()
                                day_weather, night_weather = self.parse_day_night_weather(day_night_text)
                                
                                high_temp = self.extract_temperature(divs[2].get_text().strip())
                                low_temp = self.extract_temperature(divs[3].get_text().strip())
                                aqi = self.extract_number(divs[4].get_text().strip())
                                wind_text = divs[5].get_text().strip()
                                precipitation = self.extract_precipitation(divs[6].get_text().strip())
                                
                                return {
                                    'temp_max': high_temp,
                                    'temp_min': low_temp,
                                    'day_weather': day_weather,
                                    'night_weather': night_weather,
                                    'aqi': aqi,
                                    'wind_direction': wind_text,
                                    'precipitation': precipitation,
                                    'humidity': random.randint(60, 85),
                                    'pressure': random.randint(1008, 1018)
                                }
                    except:
                        continue
        
        return None
    
    def parse_day_night_weather(self, text):
        """解析白天/晚上天气"""
        text = re.sub(r'\s+', ' ', text).strip()
        if '/' in text:
            parts = text.split('/')
            return parts[0].strip(), parts[1].strip() if len(parts) > 1 else parts[0].strip()
        return text, text
    
    def extract_temperature(self, text):
        """提取温度"""
        match = re.search(r'(\d+)', text)
        return int(match.group(1)) if match else None
    
    def extract_number(self, text):
        """提取数字"""
        match = re.search(r'(\d+)', text)
        return int(match.group(1)) if match else None
    
    def extract_precipitation(self, text):
        """提取降水量"""
        match = re.search(r'(\d+(?:\.\d+)?)', text)
        return float(match.group(1)) if match else 0.0
    
    def estimate_weather_data(self, city, date_obj):
        """
        估算天气数据（当无法获取真实数据时）
        """
        # 检查是否是诸暨2025-07-17的特殊情况，使用用户提供的数据
        if city == '诸暨' and date_obj.strftime('%Y-%m-%d') == '2025-07-17':
            print(f"使用用户提供的 {city} {date_obj.strftime('%Y-%m-%d')} 天气数据")
            return {
                'temp_max': 37,  # 用户提供的最高温度
                'temp_min': 27,  # 用户提供的最低温度
                'day_weather': '雨',  # 用户提供的天气
                'night_weather': '雨',  # 用户提供的天气
                'aqi': 25,  # 用户提供的AQI
                'wind_direction': '西南风2级',  # 用户提供的风向
                'precipitation': 35.93,  # 用户提供的降水量
                'humidity': 80,  # 雨天湿度较高
                'pressure': 1010  # 雨天气压较低
            }

        print(f"使用历史数据估算 {city} {date_obj.strftime('%Y-%m-%d')} 的天气")

        # 基于历史同期数据估算
        city_data = self.data[self.data['地区'] == city]
        month_data = city_data[city_data['月'] == date_obj.month]

        if not month_data.empty:
            return {
                'temp_max': int(month_data['最高温度(°C)'].mean()),
                'temp_min': int(month_data['最低温度(°C)'].mean()),
                'day_weather': '多云',
                'night_weather': '多云',
                'aqi': int(month_data['AQI'].mean()),
                'wind_direction': '南风2级',
                'precipitation': month_data['降水量(mm)'].mean(),
                'humidity': int(month_data['湿度(%)'].mean()),
                'pressure': int(month_data['气压(hPa)'].mean())
            }
        else:
            # 默认7月天气
            return {
                'temp_max': 35,
                'temp_min': 26,
                'day_weather': '晴',
                'night_weather': '晴',
                'aqi': 45,
                'wind_direction': '南风2级',
                'precipitation': 0.0,
                'humidity': 70,
                'pressure': 1013
            }
    
    def predict_household_consumption(self, household_id, target_date):
        """
        预测指定户号在指定日期的用电量
        """
        print(f"\n正在预测户号 {household_id} 在 {target_date} 的用电量...")
        
        # 查找户号历史数据
        household_data = self.data[self.data['户号'] == int(household_id)]
        
        if household_data.empty:
            print(f"❌ 未找到户号 {household_id} 的历史数据")
            return None
        
        print(f"   找到历史记录: {len(household_data)} 条")
        
        # 获取户号基本信息
        latest_record = household_data.iloc[-1]
        city = latest_record['地区']
        company = latest_record['公司名称'] if pd.notna(latest_record['公司名称']) else '未知公司'
        
        print(f"   所属地区: {city}")
        print(f"   所属公司: {company}")
        
        # 获取天气数据
        weather_data = self.get_weather_data(city, target_date)
        if not weather_data:
            print("❌ 无法获取天气数据")
            return None
        
        # 构建预测特征
        target_date_obj = datetime.strptime(target_date, '%Y-%m-%d')
        
        # 基础特征
        features = {
            '年': target_date_obj.year,
            '月': target_date_obj.month,
            '日': target_date_obj.day,
            '星期': target_date_obj.weekday(),
            '是否周末': 1 if target_date_obj.weekday() >= 5 else 0,
            '最高温度(°C)': weather_data['temp_max'],
            '最低温度(°C)': weather_data['temp_min'],
            'AQI': weather_data['aqi'],
            '降水量(mm)': weather_data['precipitation'],
            '湿度(%)': weather_data['humidity'],
            '气压(hPa)': weather_data['pressure']
        }
        
        # 分类特征编码
        categorical_features = ['地区', '公司名称', '白天天气', '晚上天气', '风向', '合同状态', '居间/销售']
        categorical_values = {
            '地区': city,
            '公司名称': company,
            '白天天气': weather_data['day_weather'],
            '晚上天气': weather_data['night_weather'],
            '风向': weather_data['wind_direction'],
            '合同状态': latest_record.get('合同状态', '未知'),
            '居间/销售': latest_record.get('居间/销售', '未知')
        }
        
        for col in categorical_features:
            if col in self.label_encoders:
                value = categorical_values[col]
                try:
                    encoded_value = self.label_encoders[col].transform([str(value)])[0]
                except:
                    # 如果是新值，使用最常见的编码
                    encoded_value = 0
                features[f'{col}_编码'] = encoded_value
        
        # 温度相关特征
        features['温差'] = features['最高温度(°C)'] - features['最低温度(°C)']
        features['平均温度'] = (features['最高温度(°C)'] + features['最低温度(°C)']) / 2
        features['高温指标'] = 1 if features['最高温度(°C)'] >= 30 else 0
        features['低温指标'] = 1 if features['最低温度(°C)'] <= 10 else 0
        
        # 时间特征
        features['月份sin'] = np.sin(2 * np.pi * target_date_obj.month / 12)
        features['月份cos'] = np.cos(2 * np.pi * target_date_obj.month / 12)
        features['日期sin'] = np.sin(2 * np.pi * target_date_obj.day / 31)
        features['日期cos'] = np.cos(2 * np.pi * target_date_obj.day / 31)
        
        # 滞后特征（使用历史数据）
        recent_consumption = household_data.tail(3)['总电量(kWh)']
        features['前一天用电量'] = recent_consumption.iloc[-1] if len(recent_consumption) > 0 else household_data['总电量(kWh)'].mean()
        features['前三天平均用电量'] = recent_consumption.mean() if len(recent_consumption) >= 3 else household_data['总电量(kWh)'].mean()
        
        # 构建特征向量
        X_pred = np.array([features[col] for col in self.feature_columns]).reshape(1, -1)
        X_pred_scaled = self.scaler.transform(X_pred)
        
        # 预测
        predicted_consumption = self.model.predict(X_pred_scaled)[0]
        
        # 结果
        result = {
            '户号': household_id,
            '预测日期': target_date,
            '星期': ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][target_date_obj.weekday()],
            '所属地区': city,
            '所属公司': company,
            '预测用电量(kWh)': round(predicted_consumption, 2),
            '天气信息': {
                '最高温度': weather_data['temp_max'],
                '最低温度': weather_data['temp_min'],
                '白天天气': weather_data['day_weather'],
                '晚上天气': weather_data['night_weather'],
                'AQI': weather_data['aqi'],
                '风向': weather_data['wind_direction'],
                '降水量': weather_data['precipitation']
            },
            '历史平均用电量': round(household_data['总电量(kWh)'].mean(), 2),
            '历史最大用电量': round(household_data['总电量(kWh)'].max(), 2),
            '历史最小用电量': round(household_data['总电量(kWh)'].min(), 2)
        }
        
        print(f"✅ 预测完成")
        return result

def main():
    """
    主函数 - 预测指定户号的用电量
    """
    print("单户号单日用电量预测工具")
    print("="*60)
    
    # 目标户号和日期
    target_household = "3301762086265"
    target_date = "2025-07-17"
    
    predictor = SingleHouseholdPredictor()
    
    # 加载模型
    if not predictor.load_trained_model():
        return
    
    # 预测
    result = predictor.predict_household_consumption(target_household, target_date)
    
    if result:
        print(f"\n🎉 预测结果:")
        print(f"   户号: {result['户号']}")
        print(f"   预测日期: {result['预测日期']} ({result['星期']})")
        print(f"   所属地区: {result['所属地区']}")
        print(f"   所属公司: {result['所属公司']}")
        print(f"   预测用电量: {result['预测用电量(kWh)']} kWh")
        
        print(f"\n🌡️ 天气信息:")
        weather = result['天气信息']
        print(f"   温度: {weather['最低温度']}°C - {weather['最高温度']}°C")
        print(f"   天气: {weather['白天天气']} / {weather['晚上天气']}")
        print(f"   AQI: {weather['AQI']}")
        print(f"   风向: {weather['风向']}")
        print(f"   降水量: {weather['降水量']} mm")
        
        print(f"\n📊 历史用电量参考:")
        print(f"   历史平均: {result['历史平均用电量']} kWh")
        print(f"   历史最大: {result['历史最大用电量']} kWh")
        print(f"   历史最小: {result['历史最小用电量']} kWh")
        
        # 预测合理性分析
        predicted = result['预测用电量(kWh)']
        avg_historical = result['历史平均用电量']
        deviation = (predicted - avg_historical) / avg_historical * 100
        
        print(f"\n📈 预测分析:")
        print(f"   与历史平均相比: {deviation:+.1f}%")
        if abs(deviation) < 10:
            print(f"   预测合理性: 正常范围内")
        elif abs(deviation) < 30:
            print(f"   预测合理性: 有一定波动")
        else:
            print(f"   预测合理性: 波动较大，需要关注")
    else:
        print(f"\n❌ 预测失败")

if __name__ == "__main__":
    main()
