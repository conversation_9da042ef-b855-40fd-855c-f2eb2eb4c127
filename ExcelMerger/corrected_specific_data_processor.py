#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版：确保具体日期和具体用电量一一对应
问题：之前的版本在有多条记录时，具体用电量显示的是总和，而不是对应具体日期的数值
解决：确保具体日期和具体用电量严格对应
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from collections import defaultdict
from openpyxl import load_workbook

class CorrectedSpecificDataProcessor:
    def __init__(self):
        self.source_data = None
        self.target_workbook = None
        self.processed_data = {}
        
        # 中文周天映射
        self.weekday_names = {
            0: '星期一',
            1: '星期二', 
            2: '星期三',
            3: '星期四',
            4: '星期五',
            5: '星期六',
            6: '星期日'
        }
    
    def load_and_analyze_data(self, source_file):
        """
        加载并分析原始数据
        """
        try:
            print(f"正在读取原始数据文件: {os.path.basename(source_file)}")
            self.source_data = pd.read_excel(source_file)
            print(f"原始数据读取成功，共 {len(self.source_data)} 行数据")
            
            # 转换时间列并添加周天信息
            self.source_data['时间_处理'] = pd.to_datetime(self.source_data['日期'])
            self.source_data['日期_处理'] = self.source_data['时间_处理'].dt.date
            self.source_data['周天数字'] = self.source_data['时间_处理'].dt.weekday
            self.source_data['周天'] = self.source_data['周天数字'].map(self.weekday_names)
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def process_region_with_correct_mapping(self, region):
        """
        处理单个地区的数据，确保具体日期和具体用电量一一对应
        """
        region_data = self.source_data[self.source_data['地区'] == region]
        if region_data.empty:
            return {}
        
        result = {}
        
        print(f"\n=== 处理 {region} 地区数据（修正版）===")
        
        # 按天气类型分组
        for weather in region_data['天气'].unique():
            weather_data = region_data[region_data['天气'] == weather]
            print(f"\n处理天气类型: {weather}")
            
            # 按周天收集数据
            weekday_info = {}
            for weekday in ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']:
                weekday_data = weather_data[weather_data['周天'] == weekday]
                
                if not weekday_data.empty:
                    # 按日期排序
                    weekday_data = weekday_data.sort_values('日期_处理')
                    
                    dates = weekday_data['日期_处理'].tolist()
                    powers = weekday_data['总电量(kWh)'].tolist()
                    
                    total_power = sum(powers)
                    earliest_date = min(dates)
                    
                    # 关键修正：具体用电量应该是对应具体日期的数值，而不是总和
                    earliest_power = None
                    for date, power in zip(dates, powers):
                        if date == earliest_date:
                            earliest_power = power
                            break
                    
                    weekday_info[weekday] = {
                        '总用电量': total_power,
                        '具体日期': earliest_date,
                        '具体用电量': earliest_power,  # 修正：使用对应日期的具体数值
                        '记录数': len(powers),
                        '所有记录': list(zip(dates, powers))
                    }
                    
                    print(f"  {weekday}: {len(powers)} 条记录")
                    print(f"    总用电量: {total_power:.2f}")
                    print(f"    具体日期: {earliest_date}")
                    print(f"    具体用电量: {earliest_power:.2f} (对应{earliest_date})")
                    
                    # 显示所有记录以便验证
                    for i, (date, power) in enumerate(zip(dates, powers)):
                        print(f"    记录{i+1}: {date} - {power:.2f} kWh")
            
            if weekday_info:
                result[weather] = {
                    '周天数据': weekday_info,
                    '代表日期': weather_data['日期_处理'].iloc[0]
                }
        
        return result
    
    def load_target_file(self, target_file):
        """
        加载目标文件
        """
        try:
            print(f"\n正在读取目标文件: {os.path.basename(target_file)}")
            self.target_workbook = load_workbook(target_file)
            print(f"发现工作表: {self.target_workbook.sheetnames}")
            return True
        except Exception as e:
            print(f"目标文件加载失败: {e}")
            return False
    
    def add_corrected_data_to_sheet(self, sheet_name, region_data):
        """
        将修正后的数据添加到工作表
        """
        try:
            if sheet_name not in self.target_workbook.sheetnames:
                print(f"警告: 工作表 {sheet_name} 不存在")
                return False
            
            worksheet = self.target_workbook[sheet_name]
            last_row = worksheet.max_row
            current_row = last_row + 2
            
            print(f"\n在工作表 {sheet_name} 的第 {current_row} 行开始添加修正数据")
            
            # 添加标题
            worksheet.cell(row=current_row, column=1, value="=== 同一周天相同天气数据汇总（日期用电量一一对应）===")
            current_row += 2
            
            weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
            
            # 为每种天气类型添加数据
            for weather_type, weather_info in region_data.items():
                weekday_data = weather_info['周天数据']
                
                print(f"  添加天气类型: {weather_type}")
                
                # 1. 日期行
                worksheet.cell(row=current_row, column=1, value="日期")
                worksheet.cell(row=current_row, column=2, value=weather_info['代表日期'].strftime('%Y-%m-%d'))
                current_row += 1
                
                # 2. 日期类型行（周天标题）
                worksheet.cell(row=current_row, column=1, value="日期类型")
                for i, weekday in enumerate(weekdays, 2):
                    worksheet.cell(row=current_row, column=i, value=weekday)
                current_row += 1
                
                # 3. 天气行
                worksheet.cell(row=current_row, column=1, value="天气")
                for i, weekday in enumerate(weekdays, 2):
                    worksheet.cell(row=current_row, column=i, value=weather_type)
                current_row += 1
                
                # 4. 具体日期行
                worksheet.cell(row=current_row, column=1, value="具体日期")
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weekday_data:
                        date = weekday_data[weekday]['具体日期']
                        worksheet.cell(row=current_row, column=i, value=date.strftime('%Y-%m-%d'))
                        print(f"    {weekday}: 具体日期 {date.strftime('%Y-%m-%d')}")
                current_row += 1

                # 5. 具体用电量行（修正：对应具体日期的数值）
                worksheet.cell(row=current_row, column=1, value="具体用电量")
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weekday_data:
                        power = weekday_data[weekday]['具体用电量']
                        worksheet.cell(row=current_row, column=i, value=round(power, 2))
                        print(f"    {weekday}: 具体用电量 {power:.2f} (对应具体日期)")
                current_row += 1
                
                current_row += 1  # 空行分隔
            
            return True
            
        except Exception as e:
            print(f"添加数据到工作表 {sheet_name} 失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def process(self, source_file, target_file, output_file):
        """
        完整的修正处理流程
        """
        print(f"修正版：确保日期和用电量一一对应的数据处理工具")
        print(f"=" * 80)
        
        # 加载并分析原始数据
        if not self.load_and_analyze_data(source_file):
            return False
        
        # 加载目标文件
        if not self.load_target_file(target_file):
            return False
        
        # 处理各个地区
        regions = ['杭州', '金华', '宁波', '台州']
        
        for region in regions:
            print(f"\n{'='*60}")
            print(f"处理地区: {region}")
            print(f"{'='*60}")
            
            region_data = self.process_region_with_correct_mapping(region)
            if region_data:
                self.add_corrected_data_to_sheet(region, region_data)
            else:
                print(f"警告: 未找到地区 {region} 的数据")
        
        # 保存结果
        try:
            self.target_workbook.save(output_file)
            print(f"\n✅ 修正处理完成！结果已保存到: {output_file}")
            print(f"\n🔧 最终格式说明:")
            print(f"- 日期：该天气类型的代表日期")
            print(f"- 日期类型：星期一到星期日的标题行")
            print(f"- 天气：该行所有列都显示相同的天气类型")
            print(f"- 具体日期：每个周天该天气类型的最早日期")
            print(f"- 具体用电量：对应具体日期的实际用电量（已删除总用电量行）")
            return True
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False

def main():
    """主函数"""
    source_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/城市售电表（含气温+日期类型+天气）.xlsx"
    target_file = "/Users/<USER>/Desktop/副本城市售电表（含气温+日期类型+天气）(1).xlsx"
    output_file = "最终版_只保留具体用电量_副本城市售电表.xlsx"
    
    if not os.path.exists(source_file):
        print(f"错误: 原始数据文件不存在 - {source_file}")
        return
    
    if not os.path.exists(target_file):
        print(f"错误: 目标文件不存在 - {target_file}")
        return
    
    processor = CorrectedSpecificDataProcessor()
    processor.process(source_file, target_file, output_file)

if __name__ == "__main__":
    main()
