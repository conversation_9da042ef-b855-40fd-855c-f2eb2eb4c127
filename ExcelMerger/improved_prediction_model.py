#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的用电量预测模型
分析预测偏差原因并优化模型精度
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class ImprovedPredictionModel:
    def __init__(self):
        """
        初始化改进的预测模型
        """
        self.models = {}
        self.scalers = {}
        self.feature_importance = {}
        self.prediction_results = {}
        
        print("改进的用电量预测模型初始化完成")
    
    def load_and_analyze_data(self):
        """
        加载并分析数据，找出偏差原因
        """
        print("\n📊 加载并分析数据...")
        
        # 1. 读取预测结果
        pred_file = '/Users/<USER>/RiderProjects/Solution3/110kV以下用户用电量预测结果_2025-07-17.xlsx'
        self.df_pred = pd.read_excel(pred_file)
        
        # 2. 读取实际数据
        actual_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250717-20250717 (1).xlsx'
        self.df_actual = pd.read_excel(actual_file)
        
        # 3. 读取训练数据
        train_file = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        self.df_train = pd.read_excel(train_file)
        
        print(f"✅ 数据加载完成:")
        print(f"   预测数据: {self.df_pred.shape}")
        print(f"   实际数据: {self.df_actual.shape}")
        print(f"   训练数据: {self.df_train.shape}")
        
        # 4. 数据质量分析
        self.analyze_data_quality()
        
        return True
    
    def analyze_data_quality(self):
        """
        分析数据质量问题
        """
        print(f"\n🔍 数据质量分析:")
        
        # 分析实际数据中的零值
        zero_users = self.df_actual[self.df_actual['总电量(kWh)'] == 0]
        print(f"   实际用电量为0的户号: {len(zero_users)} 个 ({len(zero_users)/len(self.df_actual)*100:.1f}%)")
        
        # 分析预测数据分布
        print(f"   预测总电量统计:")
        print(f"     平均值: {self.df_pred['总电量(kWh)'].mean():.2f} kWh")
        print(f"     中位数: {self.df_pred['总电量(kWh)'].median():.2f} kWh")
        print(f"     标准差: {self.df_pred['总电量(kWh)'].std():.2f} kWh")
        
        # 分析实际数据分布（排除零值）
        actual_nonzero = self.df_actual[self.df_actual['总电量(kWh)'] > 0]
        print(f"   实际总电量统计（排除零值）:")
        print(f"     平均值: {actual_nonzero['总电量(kWh)'].mean():.2f} kWh")
        print(f"     中位数: {actual_nonzero['总电量(kWh)'].median():.2f} kWh")
        print(f"     标准差: {actual_nonzero['总电量(kWh)'].std():.2f} kWh")
        
        # 分析训练数据的时间范围
        if '时间' in self.df_train.columns:
            self.df_train['时间'] = pd.to_datetime(self.df_train['时间'])
            print(f"   训练数据时间范围: {self.df_train['时间'].min()} 到 {self.df_train['时间'].max()}")
            
            # 分析训练数据中的零值比例
            train_zero_ratio = (self.df_train['总电量(kWh)'] == 0).mean()
            print(f"   训练数据中零值比例: {train_zero_ratio*100:.1f}%")
    
    def prepare_improved_features(self):
        """
        准备改进的特征工程
        """
        print(f"\n🔧 准备改进的特征工程...")
        
        # 确保时间列是datetime类型
        self.df_train['时间'] = pd.to_datetime(self.df_train['时间'])
        
        # 1. 时间特征
        self.df_train['年'] = self.df_train['时间'].dt.year
        self.df_train['月'] = self.df_train['时间'].dt.month
        self.df_train['日'] = self.df_train['时间'].dt.day
        self.df_train['星期'] = self.df_train['时间'].dt.dayofweek
        self.df_train['是否周末'] = (self.df_train['星期'] >= 5).astype(int)
        self.df_train['季度'] = self.df_train['时间'].dt.quarter
        
        # 2. 用户历史特征
        user_stats = self.df_train.groupby('户号').agg({
            '总电量(kWh)': ['mean', 'std', 'max', 'min', 'count'],
            '尖电量(kWh)': 'mean',
            '峰电量(kWh)': 'mean',
            '平电量(kWh)': 'mean',
            '谷电量(kWh)': 'mean'
        }).round(2)
        
        # 扁平化列名
        user_stats.columns = ['_'.join(col).strip() for col in user_stats.columns]
        user_stats = user_stats.reset_index()
        
        # 3. 用户用电模式特征
        user_stats['尖峰比'] = user_stats['尖电量(kWh)_mean'] / (user_stats['峰电量(kWh)_mean'] + 0.001)
        user_stats['峰平比'] = user_stats['峰电量(kWh)_mean'] / (user_stats['平电量(kWh)_mean'] + 0.001)
        user_stats['平谷比'] = user_stats['平电量(kWh)_mean'] / (user_stats['谷电量(kWh)_mean'] + 0.001)
        user_stats['用电稳定性'] = user_stats['总电量(kWh)_std'] / (user_stats['总电量(kWh)_mean'] + 0.001)
        
        # 4. 合并特征
        self.df_train_features = pd.merge(self.df_train, user_stats, on='户号', how='left')
        
        # 5. 处理缺失值
        numeric_columns = self.df_train_features.select_dtypes(include=[np.number]).columns
        self.df_train_features[numeric_columns] = self.df_train_features[numeric_columns].fillna(0)
        
        print(f"✅ 特征工程完成，特征数量: {len(self.df_train_features.columns)}")
        
        return True
    
    def filter_valid_data(self):
        """
        过滤有效数据，排除异常值
        """
        print(f"\n🔍 过滤有效数据...")
        
        original_count = len(self.df_train_features)
        
        # 1. 排除总电量为0的记录（可能是数据缺失）
        self.df_train_features = self.df_train_features[self.df_train_features['总电量(kWh)'] > 0]
        
        # 2. 排除异常大的用电量（可能是数据错误）
        q99 = self.df_train_features['总电量(kWh)'].quantile(0.99)
        self.df_train_features = self.df_train_features[self.df_train_features['总电量(kWh)'] <= q99]
        
        # 3. 排除用电量过小的记录（可能是抄表错误）
        q01 = self.df_train_features['总电量(kWh)'].quantile(0.01)
        self.df_train_features = self.df_train_features[self.df_train_features['总电量(kWh)'] >= q01]
        
        filtered_count = len(self.df_train_features)
        
        print(f"✅ 数据过滤完成:")
        print(f"   原始数据: {original_count} 条")
        print(f"   过滤后数据: {filtered_count} 条")
        print(f"   过滤比例: {(original_count-filtered_count)/original_count*100:.1f}%")
        
        return True
    
    def train_improved_models(self):
        """
        训练改进的预测模型
        """
        print(f"\n🤖 训练改进的预测模型...")
        
        # 准备特征和目标变量
        feature_columns = [col for col in self.df_train_features.columns 
                          if col not in ['户号', '表计号', '时间', '总电量(kWh)', '尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']]
        
        X = self.df_train_features[feature_columns]
        y_total = self.df_train_features['总电量(kWh)']
        
        print(f"   特征数量: {len(feature_columns)}")
        print(f"   训练样本数: {len(X)}")
        
        # 数据标准化
        self.scalers['total'] = StandardScaler()
        X_scaled = self.scalers['total'].fit_transform(X)
        
        # 分割训练和测试数据
        X_train, X_test, y_train, y_test = train_test_split(X_scaled, y_total, test_size=0.2, random_state=42)
        
        # 训练多个模型
        models_to_train = {
            'RandomForest': RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1),
            'GradientBoosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'LinearRegression': LinearRegression()
        }
        
        best_model = None
        best_score = float('inf')
        
        for name, model in models_to_train.items():
            print(f"   训练 {name} 模型...")
            
            # 训练模型
            model.fit(X_train, y_train)
            
            # 预测和评估
            y_pred = model.predict(X_test)
            mae = mean_absolute_error(y_test, y_pred)
            mse = mean_squared_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            
            print(f"     MAE: {mae:.2f}")
            print(f"     RMSE: {np.sqrt(mse):.2f}")
            print(f"     R²: {r2:.4f}")
            
            # 保存模型
            self.models[name] = model
            
            # 选择最佳模型
            if mae < best_score:
                best_score = mae
                best_model = name
        
        print(f"✅ 最佳模型: {best_model} (MAE: {best_score:.2f})")
        self.best_model_name = best_model
        
        # 保存特征列名
        self.feature_columns = feature_columns
        
        return True
    
    def predict_with_improved_model(self):
        """
        使用改进的模型进行预测
        """
        print(f"\n🔮 使用改进模型进行预测...")
        
        # 为预测数据准备特征
        pred_users = self.df_pred['户号'].unique()
        
        # 获取用户历史统计特征
        user_stats = self.df_train.groupby('户号').agg({
            '总电量(kWh)': ['mean', 'std', 'max', 'min', 'count'],
            '尖电量(kWh)': 'mean',
            '峰电量(kWh)': 'mean',
            '平电量(kWh)': 'mean',
            '谷电量(kWh)': 'mean'
        }).round(2)
        
        user_stats.columns = ['_'.join(col).strip() for col in user_stats.columns]
        user_stats = user_stats.reset_index()
        
        # 计算用电模式特征
        user_stats['尖峰比'] = user_stats['尖电量(kWh)_mean'] / (user_stats['峰电量(kWh)_mean'] + 0.001)
        user_stats['峰平比'] = user_stats['峰电量(kWh)_mean'] / (user_stats['平电量(kWh)_mean'] + 0.001)
        user_stats['平谷比'] = user_stats['平电量(kWh)_mean'] / (user_stats['谷电量(kWh)_mean'] + 0.001)
        user_stats['用电稳定性'] = user_stats['总电量(kWh)_std'] / (user_stats['总电量(kWh)_mean'] + 0.001)
        
        # 为2025-07-17添加时间特征
        pred_features = []
        for user_id in pred_users:
            user_feature = {
                '户号': user_id,
                '年': 2025,
                '月': 7,
                '日': 17,
                '星期': 3,  # 2025-07-17是星期四
                '是否周末': 0,
                '季度': 3
            }
            pred_features.append(user_feature)
        
        df_pred_features = pd.DataFrame(pred_features)
        
        # 合并用户统计特征
        df_pred_features = pd.merge(df_pred_features, user_stats, on='户号', how='left')
        
        # 处理缺失值（新用户）
        numeric_columns = df_pred_features.select_dtypes(include=[np.number]).columns
        df_pred_features[numeric_columns] = df_pred_features[numeric_columns].fillna(0)
        
        # 准备预测特征
        X_pred = df_pred_features[self.feature_columns]
        X_pred_scaled = self.scalers['total'].transform(X_pred)
        
        # 使用最佳模型预测
        best_model = self.models[self.best_model_name]
        y_pred_improved = best_model.predict(X_pred_scaled)
        
        # 保存改进的预测结果
        self.df_pred_improved = df_pred_features.copy()
        self.df_pred_improved['预测总电量_改进'] = y_pred_improved
        
        print(f"✅ 改进预测完成，预测了 {len(y_pred_improved)} 个用户")
        
        return True
