#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中栋电厂完整实时电能数据处理工具
基于完整的48条半小时数据生成15分钟出力曲线
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

class ZhongdongCompleteDataProcessor:
    def __init__(self):
        """
        初始化中栋电厂完整数据处理器
        """
        self.complete_data = None
        self.processed_data = None
        
        # 您提供的完整48条半小时数据
        self.raw_data = [
            ('00:30:00', 2.4118), ('01:00:00', 2.4453), ('01:30:00', 2.8187), ('02:00:00', 2.6173),
            ('02:30:00', 2.5712), ('03:00:00', 2.2650), ('03:30:00', 2.2021), ('04:00:00', 2.4286),
            ('04:30:00', 2.3573), ('05:00:00', 2.3615), ('05:30:00', 2.3489), ('06:00:00', 2.2398),
            ('06:30:00', 1.9001), ('07:00:00', 1.7910), ('07:30:00', 2.1014), ('08:00:00', 2.4579),
            ('08:30:00', 2.1182), ('09:00:00', 2.0762), ('09:30:00', 2.7893), ('10:00:00', 2.5460),
            ('10:30:00', 2.3782), ('11:00:00', 2.3657), ('11:30:00', 2.1685), ('12:00:00', 2.0720),
            ('12:30:00', 2.1014), ('13:00:00', 2.5502), ('13:30:00', 3.0829), ('14:00:00', 3.7205),
            ('14:30:00', 3.9931), ('15:00:00', 3.3975), ('15:30:00', 3.9889), ('16:00:00', 4.5258),
            ('16:30:00', 4.1818), ('17:00:00', 3.4772), ('17:30:00', 3.4981), ('18:00:00', 3.4688),
            ('18:30:00', 3.4059), ('19:00:00', 3.0577), ('19:30:00', 2.3950), ('20:00:00', 2.3866),
            ('20:30:00', 3.4520), ('21:00:00', 3.6114), ('21:30:00', 3.8295), ('22:00:00', 3.5736),
            ('22:30:00', 3.5191), ('23:00:00', 3.5191), ('23:30:00', 3.8001), ('00:00:00', 3.6911)
        ]
    
    def load_complete_data(self):
        """
        加载完整的实时电能数据
        """
        print("正在加载中栋电厂完整实时电能数据...")
        
        # 转换为DataFrame
        data_list = []
        for time_str, energy in self.raw_data:
            # 处理时间
            if time_str == '00:00:00':
                # 00:00:00 是次日的，对应7月2日
                datetime_obj = datetime(2025, 7, 2, 0, 0, 0)
            else:
                hour, minute, second = map(int, time_str.split(':'))
                datetime_obj = datetime(2025, 7, 1, hour, minute, second)
            
            # 计算功率 (半小时功率 = 半小时电量 / 0.5小时)
            power = energy / 0.5
            
            data_list.append({
                '时间': datetime_obj,
                '时间字符串': time_str,
                '计量电量(MWh)': energy,
                '功率(MW)': power,
                '日期': datetime_obj.date().strftime('%Y-%m-%d')
            })
        
        self.complete_data = pd.DataFrame(data_list)
        
        print(f"✅ 成功加载完整实时电能数据: {len(self.complete_data)} 条记录")
        print(f"   时间范围: {self.complete_data['时间'].min()} 到 {self.complete_data['时间'].max()}")
        print(f"   电量范围: {self.complete_data['计量电量(MWh)'].min():.4f} - {self.complete_data['计量电量(MWh)'].max():.4f} MWh")
        print(f"   功率范围: {self.complete_data['功率(MW)'].min():.4f} - {self.complete_data['功率(MW)'].max():.4f} MW")
        
        return True
    
    def generate_15min_curve(self):
        """
        生成15分钟出力曲线
        """
        print("\n正在生成15分钟出力曲线...")
        
        if self.complete_data is None:
            print("❌ 请先加载完整数据")
            return False
        
        results = []
        
        # 为每个半小时数据生成两个15分钟时间点
        for _, row in self.complete_data.iterrows():
            time_obj = row['时间']
            half_hour_energy = row['计量电量(MWh)']
            half_hour_power = row['功率(MW)']
            
            # 每15分钟的电量 = 半小时电量 / 2
            quarter_hour_energy = half_hour_energy / 2
            # 每15分钟的功率 = 15分钟电量 / 0.25小时
            quarter_hour_power = quarter_hour_energy / 0.25
            
            # 生成两个15分钟时间点
            hour = time_obj.hour
            minute = time_obj.minute
            
            if minute == 30:
                # 00:30 对应 00:15 和 00:30
                time1 = f"{hour:02d}:15"
                time2 = f"{hour:02d}:30"
            elif minute == 0:
                # 01:00 对应 00:45 和 01:00
                if hour == 0:
                    # 特殊处理 00:00 (次日)
                    time1 = "23:45"
                    time2 = "24:00"  # 用24:00表示次日00:00
                else:
                    time1 = f"{hour-1:02d}:45"
                    time2 = f"{hour:02d}:00"
            
            # 添加两个15分钟数据点
            results.append({
                '时间': time1,
                '电量(MWh)': quarter_hour_energy,
                '功率(MW)': quarter_hour_power,
                '日期': row['日期'],
                '原始时间': row['时间字符串'],
                '原始电量': half_hour_energy,
                '原始功率': half_hour_power
            })
            
            results.append({
                '时间': time2,
                '电量(MWh)': quarter_hour_energy,
                '功率(MW)': quarter_hour_power,
                '日期': row['日期'],
                '原始时间': row['时间字符串'],
                '原始电量': half_hour_energy,
                '原始功率': half_hour_power
            })
        
        # 转换为DataFrame并排序
        self.processed_data = pd.DataFrame(results)
        
        # 按时间排序
        def time_sort_key(time_str):
            if time_str == "24:00":
                return 24 * 60
            hour, minute = map(int, time_str.split(':'))
            return hour * 60 + minute
        
        self.processed_data['sort_key'] = self.processed_data['时间'].apply(time_sort_key)
        self.processed_data = self.processed_data.sort_values(['日期', 'sort_key']).drop('sort_key', axis=1).reset_index(drop=True)
        
        print(f"✅ 15分钟出力曲线生成完成: {len(self.processed_data)} 个时间点")
        
        # 显示统计信息
        total_energy = self.processed_data['电量(MWh)'].sum()
        avg_power = self.processed_data['功率(MW)'].mean()
        print(f"   总电量: {total_energy:.2f} MWh")
        print(f"   平均功率: {avg_power:.2f} MW")
        print(f"   功率范围: {self.processed_data['功率(MW)'].min():.2f} - {self.processed_data['功率(MW)'].max():.2f} MW")
        
        return True
    
    def save_results(self, output_file="中栋电厂完整实时电能15分钟出力曲线.xlsx"):
        """
        保存处理结果
        """
        print(f"\n正在保存结果...")
        
        if self.processed_data is None or self.complete_data is None:
            print("❌ 没有数据可保存")
            return False
        
        try:
            output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 1. 按您要求的格式输出
                format_data = []
                for _, row in self.processed_data.iterrows():
                    format_data.append({
                        '7月1号实时出力_时间': row['时间'],
                        '7月1号实时出力_MWH': f"{row['电量(MWh)']:.4f}",
                        '7月1号实时出力_MW_时间': row['时间'],
                        '7月1号实时出力_MW': f"{row['功率(MW)']:.4f}",
                        '总功率=总电量/总时长': f"{row['功率(MW)']:.4f}"
                    })
                
                format_df = pd.DataFrame(format_data)
                format_df.to_excel(writer, sheet_name='按要求格式', index=False)
                
                # 2. 完整15分钟数据
                output_data = self.processed_data[['时间', '电量(MWh)', '功率(MW)', '日期']].copy()
                output_data.to_excel(writer, sheet_name='15分钟出力曲线', index=False)
                
                # 3. 原始半小时数据
                original_data = self.complete_data[['时间字符串', '计量电量(MWh)', '功率(MW)']].copy()
                original_data.columns = ['时间', '计量电量(MWh)', '功率(MW)']
                original_data.to_excel(writer, sheet_name='原始半小时数据', index=False)
                
                # 4. 详细转换数据
                detailed_data = self.processed_data.copy()
                detailed_data.to_excel(writer, sheet_name='详细转换数据', index=False)
                
                # 5. 统计汇总
                summary_data = {
                    '项目': ['总电量', '平均功率', '最大功率', '最小功率', '半小时数据点', '15分钟数据点'],
                    '数值': [
                        f"{self.processed_data['电量(MWh)'].sum():.4f} MWh",
                        f"{self.processed_data['功率(MW)'].mean():.4f} MW",
                        f"{self.processed_data['功率(MW)'].max():.4f} MW",
                        f"{self.processed_data['功率(MW)'].min():.4f} MW",
                        f"{len(self.complete_data)} 个",
                        f"{len(self.processed_data)} 个"
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='统计汇总', index=False)
            
            print(f"✅ 结果已保存到: {output_path}")
            
            # 显示按您要求格式的前10个数据点
            print(f"\n🔍 按您要求格式的前10个数据点:")
            print(f"7月1号实时出力\\tMWH\\t\\t7月1号实时出力\\tMW\\t总功率=总电量/总时长")
            for _, row in self.processed_data.head(10).iterrows():
                print(f"{row['时间']}\\t{row['电量(MWh)']:.4f}\\t\\t{row['时间']}\\t{row['功率(MW)']:.4f}\\t{row['功率(MW)']:.4f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False

def main():
    """
    主函数
    """
    print("中栋电厂完整实时电能15分钟出力曲线处理工具")
    print("="*60)
    
    processor = ZhongdongCompleteDataProcessor()
    
    # 1. 加载完整数据
    if not processor.load_complete_data():
        return
    
    # 2. 生成15分钟出力曲线
    if not processor.generate_15min_curve():
        return
    
    # 3. 保存结果
    if processor.save_results():
        print(f"\n🎉 处理完成！")
        print(f"📊 成功基于48条半小时数据生成96个15分钟时间点")
        print(f"⚡ 功率计算: 总功率 = 总电量 / 总时长")
        print(f"📁 结果已保存到Excel文件，完全按照您的格式要求")
    else:
        print(f"\n❌ 处理失败！")

if __name__ == "__main__":
    main()
