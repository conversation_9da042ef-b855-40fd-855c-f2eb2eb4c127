#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将7月份天气数据合并到用电量文件中
根据地区和日期匹配天气信息
"""

import pandas as pd
import os
from datetime import datetime, timedelta

def main():
    """主函数"""
    print("7月份天气数据合并工具")
    print("=" * 50)
    
    # 文件路径
    weather_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/7月地方天气(1).xlsx"
    usage_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/副本合并结果_用电量信息含地区.xlsx"
    output_file = "/Users/<USER>/RiderProjects/Solution3/合并结果_7月用电量信息含天气.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(weather_file):
        print(f"错误: 天气文件不存在")
        print(f"路径: {weather_file}")
        return False
    
    if not os.path.exists(usage_file):
        print(f"错误: 用电量文件不存在")
        print(f"路径: {usage_file}")
        return False
    
    try:
        # 读取天气文件
        print(f"正在读取天气文件...")
        weather_df = pd.read_excel(weather_file)
        print(f"天气文件读取成功，共 {len(weather_df)} 行数据")
        print(f"天气文件列名: {list(weather_df.columns)}")
        
        # 读取用电量文件
        print(f"正在读取用电量文件...")
        usage_df = pd.read_excel(usage_file)
        print(f"用电量文件读取成功，共 {len(usage_df)} 行数据")
        print(f"用电量文件列名: {list(usage_df.columns)}")
        
        # 预处理数据
        print(f"\n正在预处理数据...")
        
        # 标准化日期格式的函数
        def normalize_date(date_value):
            if pd.isna(date_value):
                return None
            try:
                if isinstance(date_value, datetime):
                    return date_value.strftime('%Y-%m-%d')
                else:
                    parsed_date = pd.to_datetime(date_value)
                    return parsed_date.strftime('%Y-%m-%d')
            except:
                return None
        
        # 标准化用电量文件的日期格式
        usage_df['标准化日期'] = usage_df['时间'].apply(normalize_date)
        
        # 标准化天气文件的日期格式
        weather_df['标准化日期'] = weather_df['日期'].apply(normalize_date)
        
        # 显示数据概况
        print(f"\n=== 数据概况 ===")
        print(f"用电量文件中的地区分布:")
        usage_regions = usage_df['地区'].value_counts()
        print(usage_regions)
        
        print(f"\n天气文件中的城市:")
        weather_cities = sorted(weather_df['城市'].dropna().unique())
        print(weather_cities)
        
        print(f"\n用电量文件日期范围:")
        print(f"最早: {usage_df['标准化日期'].min()}")
        print(f"最晚: {usage_df['标准化日期'].max()}")
        
        print(f"\n天气文件日期范围:")
        print(f"最早: {weather_df['标准化日期'].min()}")
        print(f"最晚: {weather_df['标准化日期'].max()}")
        
        # 创建天气映射字典
        print(f"\n正在创建天气映射...")
        weather_mapping = {}
        for _, row in weather_df.iterrows():
            key = (row['城市'], row['标准化日期'])
            weather_mapping[key] = row['天气']
        
        print(f"天气映射创建完成，共 {len(weather_mapping)} 个条目")
        
        # 合并天气数据
        print(f"正在合并天气数据...")
        def get_weather(row):
            if pd.isna(row['地区']):
                return None
            key = (row['地区'], row['标准化日期'])
            return weather_mapping.get(key, None)
        
        usage_df['天气'] = usage_df.apply(get_weather, axis=1)
        
        # 统计初始匹配情况
        initial_matched = usage_df['天气'].notna().sum()
        initial_unmatched = len(usage_df) - initial_matched
        
        print(f"\n=== 初始匹配结果 ===")
        print(f"总记录数: {len(usage_df)}")
        print(f"成功匹配天气: {initial_matched}")
        print(f"未匹配天气: {initial_unmatched}")
        print(f"天气匹配率: {initial_matched/len(usage_df)*100:.2f}%")
        
        # 分析未匹配的记录
        if initial_unmatched > 0:
            print(f"\n=== 未匹配记录分析 ===")
            unmatched_df = usage_df[usage_df['天气'].isna()]
            unmatched_combinations = unmatched_df[['地区', '标准化日期']].drop_duplicates()
            
            print(f"未匹配的地区-日期组合:")
            for _, row in unmatched_combinations.head(10).iterrows():
                region = row['地区']
                date = row['标准化日期']
                if pd.notna(region):
                    count = len(unmatched_df[(unmatched_df['地区'] == region) & (unmatched_df['标准化日期'] == date)])
                    print(f"  地区: {region}, 日期: {date} ({count} 条记录)")
            
            if len(unmatched_combinations) > 10:
                print(f"  ... 还有 {len(unmatched_combinations) - 10} 个未匹配的组合")
            
            # 尝试填充缺失的天气数据
            print(f"\n正在尝试填充缺失的天气数据...")
            usage_df = fill_missing_weather(usage_df, weather_mapping)
        
        # 统计最终结果
        final_matched = usage_df['天气'].notna().sum()
        final_unmatched = len(usage_df) - final_matched
        
        print(f"\n=== 最终匹配结果 ===")
        print(f"总记录数: {len(usage_df)}")
        print(f"成功匹配天气: {final_matched}")
        print(f"未匹配天气: {final_unmatched}")
        print(f"天气匹配率: {final_matched/len(usage_df)*100:.2f}%")
        
        # 清理数据并保存
        print(f"\n正在保存结果...")
        
        # 删除辅助列
        result_df = usage_df.drop(columns=['标准化日期'])
        
        # 重新排列列的顺序，将天气列放在地区列后面
        cols = list(result_df.columns)
        if '天气' in cols:
            cols.remove('天气')
            if '地区' in cols:
                region_index = cols.index('地区')
                cols.insert(region_index + 1, '天气')
            else:
                cols.append('天气')
        
        result_df = result_df[cols]
        
        # 保存到Excel文件
        result_df.to_excel(output_file, index=False)
        print(f"结果已保存到: {output_file}")
        
        # 显示预览
        print(f"\n=== 数据预览 (前10行) ===")
        preview_cols = ['地区', '天气', '时间', '总电量(kWh)']
        available_cols = [col for col in preview_cols if col in result_df.columns]
        print(result_df[available_cols].head(10))
        
        # 显示天气数据统计
        print(f"\n=== 天气数据统计 ===")
        if result_df['天气'].notna().sum() > 0:
            weather_stats = result_df['天气'].value_counts()
            print(weather_stats)
        
        # 显示最终的未匹配记录
        final_unmatched_df = result_df[result_df['天气'].isna()]
        if len(final_unmatched_df) > 0:
            print(f"\n=== 最终未匹配记录 ===")
            unmatched_summary = final_unmatched_df.groupby(['地区', '时间']).size().reset_index(name='记录数')
            print("未匹配记录按地区和日期汇总:")
            print(unmatched_summary.to_string(index=False))
        
        print(f"\n处理完成！")
        return True
        
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def fill_missing_weather(usage_df, weather_mapping):
    """
    填充缺失的天气数据
    
    Args:
        usage_df: 用电量DataFrame
        weather_mapping: 天气映射字典
        
    Returns:
        DataFrame: 填充后的DataFrame
    """
    try:
        # 获取所有未匹配的记录
        unmatched_df = usage_df[usage_df['天气'].isna()].copy()
        
        # 按地区和日期分组处理
        for (region, date), group in unmatched_df.groupby(['地区', '标准化日期']):
            if pd.isna(region) or region is None:
                continue  # 跳过没有地区信息的记录
            
            print(f"  尝试填充 {region} 在 {date} 的天气数据...")
            
            # 策略1: 使用相邻日期的天气数据
            nearby_weather = get_nearby_date_weather(weather_mapping, region, date)
            if nearby_weather:
                print(f"    找到相邻日期的天气: {nearby_weather}")
                usage_df.loc[(usage_df['地区'] == region) & 
                            (usage_df['标准化日期'] == date), '天气'] = nearby_weather
                continue
            
            # 策略2: 使用相同地区的常见天气
            common_weather = get_common_weather_for_region(weather_mapping, region)
            if common_weather:
                print(f"    使用地区常见天气: {common_weather}")
                usage_df.loc[(usage_df['地区'] == region) & 
                            (usage_df['标准化日期'] == date), '天气'] = common_weather
                continue
            
            # 策略3: 使用相同日期其他地区的天气
            similar_weather = get_similar_region_weather(weather_mapping, date)
            if similar_weather:
                print(f"    使用相似地区天气: {similar_weather}")
                usage_df.loc[(usage_df['地区'] == region) & 
                            (usage_df['标准化日期'] == date), '天气'] = similar_weather
        
        return usage_df
        
    except Exception as e:
        print(f"填充缺失天气数据时发生错误: {e}")
        return usage_df

def get_nearby_date_weather(weather_mapping, region, date):
    """获取相邻日期的天气数据"""
    try:
        target_date = datetime.strptime(date, '%Y-%m-%d')
        
        # 查找前后3天的天气数据
        for days_diff in [1, -1, 2, -2, 3, -3]:
            nearby_date = target_date + timedelta(days=days_diff)
            nearby_date_str = nearby_date.strftime('%Y-%m-%d')
            
            weather = weather_mapping.get((region, nearby_date_str))
            if weather:
                return weather
        
        return None
        
    except Exception as e:
        print(f"获取相邻日期天气时发生错误: {e}")
        return None

def get_common_weather_for_region(weather_mapping, region):
    """获取地区的常见天气"""
    try:
        # 获取该地区的所有天气记录
        region_weather = [weather for (r, d), weather in weather_mapping.items() if r == region]
        
        if region_weather:
            # 获取最常见的天气
            from collections import Counter
            weather_counts = Counter(region_weather)
            return weather_counts.most_common(1)[0][0]
        
        return None
        
    except Exception as e:
        print(f"获取地区常见天气时发生错误: {e}")
        return None

def get_similar_region_weather(weather_mapping, date):
    """获取相同日期其他地区的天气"""
    try:
        # 获取该日期的所有天气记录
        date_weather = [weather for (r, d), weather in weather_mapping.items() if d == date]
        
        if date_weather:
            # 获取最常见的天气
            from collections import Counter
            weather_counts = Counter(date_weather)
            return weather_counts.most_common(1)[0][0]
        
        return None
        
    except Exception as e:
        print(f"获取相似地区天气时发生错误: {e}")
        return None

if __name__ == "__main__":
    main()
