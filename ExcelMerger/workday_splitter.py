#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作日数据分割工具
将用电量数据按工作日和非工作日分成两个表
"""

import pandas as pd
import numpy as np
from datetime import datetime, date
import os

class WorkdaySplitter:
    def __init__(self):
        """
        初始化工作日分割器
        """
        # 2025年7月的节假日（根据中国法定节假日）
        # 7月份没有法定节假日，只需要考虑周末
        self.holidays_2025_july = set([
            # 7月份没有特殊节假日，只有周末
        ])
    
    def is_workday(self, date_obj):
        """
        判断是否为工作日
        
        Args:
            date_obj: datetime.date对象
            
        Returns:
            bool: True为工作日，False为非工作日
        """
        # 检查是否为周末（周六=5, 周日=6）
        if date_obj.weekday() >= 5:  # 周六或周日
            return False
        
        # 检查是否为节假日
        if date_obj in self.holidays_2025_july:
            return False
        
        return True
    
    def split_by_workday(self, input_file, output_dir=None):
        """
        按工作日和非工作日分割Excel文件
        
        Args:
            input_file: 输入Excel文件路径
            output_dir: 输出目录，如果为None则使用输入文件所在目录
        """
        try:
            # 读取Excel文件
            print(f"正在读取文件: {input_file}")
            df = pd.read_excel(input_file)
            print(f"读取成功，共 {len(df)} 行数据")
            
            # 检查时间列
            if '时间' not in df.columns:
                print("错误: 文件中没有找到'时间'列")
                return False
            
            # 转换时间列
            df['时间'] = pd.to_datetime(df['时间'])
            
            # 添加日期和工作日标识列
            df['日期'] = df['时间'].dt.date
            df['星期'] = df['时间'].dt.day_name()
            df['是否工作日'] = df['日期'].apply(self.is_workday)
            
            # 统计信息
            total_days = len(df['日期'].unique())
            workdays = df[df['是否工作日'] == True]['日期'].unique()
            non_workdays = df[df['是否工作日'] == False]['日期'].unique()
            
            print(f"\n数据统计:")
            print(f"  总天数: {total_days} 天")
            print(f"  工作日: {len(workdays)} 天")
            print(f"  非工作日: {len(non_workdays)} 天")
            
            print(f"\n工作日日期: {sorted(workdays)}")
            print(f"非工作日日期: {sorted(non_workdays)}")
            
            # 分割数据
            workday_data = df[df['是否工作日'] == True].copy()
            non_workday_data = df[df['是否工作日'] == False].copy()
            
            # 删除临时列，保持原始格式
            workday_data = workday_data.drop(columns=['日期', '星期', '是否工作日'])
            non_workday_data = non_workday_data.drop(columns=['日期', '星期', '是否工作日'])
            
            print(f"\n分割结果:")
            print(f"  工作日数据: {len(workday_data)} 行")
            print(f"  非工作日数据: {len(non_workday_data)} 行")
            
            # 确定输出目录
            if output_dir is None:
                output_dir = os.path.dirname(input_file)
                if not output_dir:
                    output_dir = "/Users/<USER>/Desktop"
            
            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(input_file))[0]
            workday_file = os.path.join(output_dir, f"{base_name}_工作日.xlsx")
            non_workday_file = os.path.join(output_dir, f"{base_name}_非工作日.xlsx")
            
            # 保存文件
            print(f"\n正在保存文件...")
            workday_data.to_excel(workday_file, index=False)
            print(f"工作日数据已保存到: {workday_file}")
            
            non_workday_data.to_excel(non_workday_file, index=False)
            print(f"非工作日数据已保存到: {non_workday_file}")
            
            # 显示数据预览
            print(f"\n工作日数据预览 (前3行):")
            print(workday_data.head(3))
            
            print(f"\n非工作日数据预览 (前3行):")
            print(non_workday_data.head(3))
            
            # 按日期统计每天的数据量
            print(f"\n各日期数据量统计:")
            df['日期'] = pd.to_datetime(df['时间']).dt.date
            df['是否工作日'] = df['日期'].apply(self.is_workday)
            
            date_stats = df.groupby(['日期', '是否工作日']).size().reset_index(name='记录数')
            for _, row in date_stats.iterrows():
                day_type = "工作日" if row['是否工作日'] else "非工作日"
                weekday = pd.to_datetime(row['日期']).strftime('%A')
                print(f"  {row['日期']} ({weekday}) - {day_type}: {row['记录数']} 条")
            
            return True
            
        except Exception as e:
            print(f"处理文件时发生错误: {e}")
            return False

def main():
    """主函数"""
    print("工作日数据分割工具")
    print("=" * 50)
    print("将用电量数据按工作日和非工作日分成两个表")
    print("=" * 50)
    
    # 输入文件路径
    input_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/合并结果_7月用电量信息含天气(2).xlsx"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在 - {input_file}")
        return
    
    # 创建分割器
    splitter = WorkdaySplitter()
    
    print(f"输入文件: {input_file}")
    print(f"输出目录: /Users/<USER>/Desktop")
    print("\n开始处理...")
    
    # 分割文件
    success = splitter.split_by_workday(input_file, "/Users/<USER>/Desktop")
    
    if success:
        print(f"\n✅ 分割完成！")
        print(f"📁 生成文件:")
        print(f"   • 工作日数据: 合并结果_7月用电量信息含天气(2)_工作日.xlsx")
        print(f"   • 非工作日数据: 合并结果_7月用电量信息含天气(2)_非工作日.xlsx")
        print(f"\n📊 说明:")
        print(f"   • 工作日: 周一至周五（不含法定节假日）")
        print(f"   • 非工作日: 周六、周日及法定节假日")
        print(f"   • 数据格式保持不变")
        print(f"   • 7月份前15天数据已按工作日性质分类")
    else:
        print(f"\n❌ 分割失败！")

if __name__ == "__main__":
    main()
