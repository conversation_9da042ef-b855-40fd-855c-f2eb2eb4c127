#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进预测模型分析
基于真实18-19号数据的误差分析和模型优化
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class ImprovedPredictionModel:
    def __init__(self):
        """
        初始化改进预测模型
        """
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.user_profiles = {}
        
        print("🔧 改进预测模型初始化")
        print("📊 基于真实数据的误差分析和优化")
    
    def analyze_prediction_errors(self):
        """
        分析当前预测的主要问题
        """
        print("\n🔍 分析预测误差的主要问题...")
        
        # 读取预测结果和真实数据
        pred_file = '/Users/<USER>/RiderProjects/Solution3/110kV以下用户用电量预测_20250718.xlsx'
        actual_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250718-20250719.xlsx'
        
        df_pred = pd.read_excel(pred_file)
        df_actual = pd.read_excel(actual_file)
        
        # 提取7月18日数据
        df_actual['时间'] = pd.to_datetime(df_actual['时间'])
        df_actual_18 = df_actual[df_actual['时间'].dt.date == pd.to_datetime('2025-07-18').date()].copy()
        
        # 合并数据
        df_compare = pd.merge(df_pred, df_actual_18, on='户号', suffixes=('_pred', '_actual'))
        
        print(f"✅ 匹配用户数: {len(df_compare)}")
        
        # 问题分析
        problems = []
        
        # 1. 总体预测偏低
        total_pred = df_compare['总电量(kWh)_pred'].sum()
        total_actual = df_compare['总电量(kWh)_actual'].sum()
        total_error = (total_pred - total_actual) / total_actual * 100
        
        problems.append(f"总体预测偏低 {abs(total_error):.1f}%")
        
        # 2. 分时电量比例问题
        time_segments = ['尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']
        
        print(f"\n📊 分时电量比例分析:")
        for segment in time_segments:
            pred_col = f'{segment}_pred'
            actual_col = f'{segment}_actual'
            
            if pred_col in df_compare.columns and actual_col in df_compare.columns:
                pred_ratio = df_compare[pred_col].sum() / df_compare['总电量(kWh)_pred'].sum()
                actual_ratio = df_compare[actual_col].sum() / df_compare['总电量(kWh)_actual'].sum()
                
                print(f"  {segment}: 预测比例{pred_ratio:.3f} vs 实际比例{actual_ratio:.3f}")
                
                if abs(pred_ratio - actual_ratio) > 0.05:
                    problems.append(f"{segment}比例偏差{abs(pred_ratio - actual_ratio):.3f}")
        
        # 3. 大用户预测不准
        large_users = df_compare[df_compare['总电量(kWh)_actual'] > 10000]
        if len(large_users) > 0:
            large_user_mape = np.mean(np.abs((large_users['总电量(kWh)_pred'] - large_users['总电量(kWh)_actual']) / (large_users['总电量(kWh)_actual'] + 1))) * 100
            problems.append(f"大用户(>10000kWh)平均误差{large_user_mape:.1f}%")
        
        # 4. 零用电用户处理问题
        zero_users = df_compare[df_compare['总电量(kWh)_actual'] == 0]
        if len(zero_users) > 0:
            problems.append(f"{len(zero_users)}个零用电用户预测为非零")
        
        print(f"\n❌ 发现的主要问题:")
        for i, problem in enumerate(problems, 1):
            print(f"  {i}. {problem}")
        
        return df_compare, problems
    
    def create_user_profiles(self):
        """
        创建详细的用户画像
        """
        print("\n👤 创建用户画像...")
        
        # 加载训练数据
        train_file = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        df_train = pd.read_excel(train_file)
        
        # 加载真实18-19号数据
        actual_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250718-20250719.xlsx'
        df_actual = pd.read_excel(actual_file)
        
        # 按用户统计历史用电特征
        user_stats = df_train.groupby('户号').agg({
            '总电量(kWh)': ['mean', 'std', 'min', 'max', 'count'],
            '尖电量(kWh)': ['mean', 'sum'],
            '峰电量(kWh)': ['mean', 'sum'],
            '平电量(kWh)': ['mean', 'sum'],
            '谷电量(kWh)': ['mean', 'sum'],
            '公司名称': 'first',
            '地区': 'first'
        }).round(2)
        
        # 扁平化列名
        user_stats.columns = ['_'.join(col).strip() for col in user_stats.columns]
        user_stats = user_stats.reset_index()
        
        # 计算用电特征
        user_stats['日均用电'] = user_stats['总电量(kWh)_mean']
        user_stats['用电稳定性'] = user_stats['总电量(kWh)_std'] / (user_stats['总电量(kWh)_mean'] + 1)
        
        # 计算真实分时比例
        total_sum = user_stats['总电量(kWh)_mean'] * user_stats['总电量(kWh)_count']
        user_stats['真实尖电比例'] = user_stats['尖电量(kWh)_sum'] / (total_sum + 1)
        user_stats['真实峰电比例'] = user_stats['峰电量(kWh)_sum'] / (total_sum + 1)
        user_stats['真实平电比例'] = user_stats['平电量(kWh)_sum'] / (total_sum + 1)
        user_stats['真实谷电比例'] = user_stats['谷电量(kWh)_sum'] / (total_sum + 1)
        
        # 用户分类
        user_stats['用电规模'] = pd.cut(user_stats['日均用电'], 
                                   bins=[0, 1000, 5000, 15000, 50000, np.inf], 
                                   labels=['微型', '小型', '中型', '大型', '超大型'])
        
        # 添加真实18-19号数据
        df_actual['时间'] = pd.to_datetime(df_actual['时间'])
        df_actual_18 = df_actual[df_actual['时间'].dt.date == pd.to_datetime('2025-07-18').date()]
        
        # 合并真实数据
        if len(df_actual_18) > 0:
            user_stats = user_stats.merge(
                df_actual_18[['户号', '总电量(kWh)', '尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']],
                on='户号', how='left', suffixes=('', '_real_18')
            )
        else:
            # 如果没有18号数据，创建空列
            user_stats['总电量(kWh)_real_18'] = np.nan
            user_stats['尖电量(kWh)_real_18'] = np.nan
            user_stats['峰电量(kWh)_real_18'] = np.nan
            user_stats['平电量(kWh)_real_18'] = np.nan
            user_stats['谷电量(kWh)_real_18'] = np.nan
        
        self.user_profiles = user_stats
        
        print(f"✅ 创建了 {len(user_stats)} 个用户画像")
        print(f"📊 用电规模分布: {user_stats['用电规模'].value_counts().to_dict()}")
        
        return user_stats
    
    def train_improved_models(self):
        """
        训练改进的预测模型
        """
        print("\n🤖 训练改进的预测模型...")
        
        if not hasattr(self, 'user_profiles'):
            self.create_user_profiles()
        
        # 准备训练数据 - 使用历史数据作为目标
        df_train = self.user_profiles.copy()

        # 如果有真实18号数据，使用它；否则使用历史平均
        if '总电量(kWh)_real_18' in df_train.columns:
            df_train = df_train.dropna(subset=['总电量(kWh)_real_18'])
            target_column = '总电量(kWh)_real_18'
        else:
            target_column = '总电量(kWh)_mean'

        print(f"有效训练样本: {len(df_train)}")
        print(f"使用目标列: {target_column}")

        # 特征选择
        feature_columns = [
            '日均用电', '用电稳定性',
            '总电量(kWh)_mean', '总电量(kWh)_std', '总电量(kWh)_max',
            '真实尖电比例', '真实峰电比例', '真实平电比例', '真实谷电比例'
        ]
        
        # 分类特征编码
        categorical_features = ['公司名称_first', '地区_first', '用电规模']
        
        for cat_feature in categorical_features:
            if cat_feature in df_train.columns:
                encoder = LabelEncoder()
                df_train[f'{cat_feature}_encoded'] = encoder.fit_transform(df_train[cat_feature].astype(str))
                feature_columns.append(f'{cat_feature}_encoded')
                self.encoders[cat_feature] = encoder
        
        # 准备特征和目标
        X = df_train[feature_columns].fillna(0)
        y = df_train[target_column]
        
        print(f"特征数: {len(feature_columns)}")
        print(f"训练样本: {len(X)}")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 训练多种模型
        models = {
            'RandomForest': RandomForestRegressor(n_estimators=200, random_state=42),
            'GradientBoosting': GradientBoostingRegressor(n_estimators=200, random_state=42),
            'LinearRegression': LinearRegression()
        }
        
        best_model = None
        best_score = -np.inf
        
        for name, model in models.items():
            print(f"\n训练 {name}...")
            
            if name == 'LinearRegression':
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)
                
                model.fit(X_train_scaled, y_train)
                y_pred = model.predict(X_test_scaled)
                
                self.scalers[name] = scaler
            else:
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
            
            # 评估
            r2 = r2_score(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            rmse = np.sqrt(mean_squared_error(y_test, y_pred))
            
            print(f"  R²: {r2:.4f}")
            print(f"  MAE: {mae:.1f} kWh")
            print(f"  RMSE: {rmse:.1f} kWh")
            
            # 交叉验证
            if name == 'LinearRegression':
                cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5, scoring='r2')
            else:
                cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='r2')
            
            print(f"  交叉验证R²: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
            
            self.models[name] = model
            
            if r2 > best_score:
                best_score = r2
                best_model = name
        
        print(f"\n🏆 最佳模型: {best_model} (R² = {best_score:.4f})")
        
        self.best_model_name = best_model
        self.feature_names = feature_columns
        
        return True

    def create_improved_predictions(self, target_date='2025-07-19'):
        """
        使用改进模型创建预测
        """
        print(f"\n🔮 使用改进模型预测 {target_date}...")

        if not hasattr(self, 'best_model_name'):
            print("❌ 请先训练模型")
            return None

        # 获取所有用户的画像数据
        df_pred_data = self.user_profiles.copy()

        # 准备预测特征 - 只使用存在的特征
        available_features = [col for col in self.feature_names if col in df_pred_data.columns]
        missing_features = [col for col in self.feature_names if col not in df_pred_data.columns]

        if missing_features:
            print(f"⚠️ 缺失特征: {missing_features}")
            # 为缺失的特征添加默认值
            for feature in missing_features:
                df_pred_data[feature] = 0

        X_pred = df_pred_data[self.feature_names].fillna(0)

        # 使用最佳模型预测
        best_model = self.models[self.best_model_name]

        if self.best_model_name in self.scalers:
            scaler = self.scalers[self.best_model_name]
            X_pred_scaled = scaler.transform(X_pred)
            predictions = best_model.predict(X_pred_scaled)
        else:
            predictions = best_model.predict(X_pred)

        # 确保预测值为正数
        predictions = np.maximum(predictions, 0)

        # 创建预测结果
        result_data = []

        for i, (_, user_data) in enumerate(df_pred_data.iterrows()):
            user_id = user_data['户号']
            total_power = predictions[i]

            # 使用用户真实的分时比例
            ratios = {
                '尖': user_data['真实尖电比例'],
                '峰': user_data['真实峰电比例'],
                '平': user_data['真实平电比例'],
                '谷': user_data['真实谷电比例']
            }

            # 确保比例和为1
            total_ratio = sum(ratios.values())
            if total_ratio > 0:
                for key in ratios:
                    ratios[key] = ratios[key] / total_ratio
            else:
                ratios = {'尖': 0.15, '峰': 0.27, '平': 0.13, '谷': 0.45}  # 基于真实数据的平均比例

            result_data.append({
                '表计号': user_id,  # 简化处理
                '户号': user_id,
                '时间': target_date,
                '总电量(kWh)': round(total_power, 1),
                '尖电量(kWh)': round(total_power * ratios['尖'], 1),
                '峰电量(kWh)': round(total_power * ratios['峰'], 1),
                '平电量(kWh)': round(total_power * ratios['平'], 1),
                '谷电量(kWh)': round(total_power * ratios['谷'], 1)
            })

        df_result = pd.DataFrame(result_data)

        print(f"✅ 改进预测完成: {len(df_result)} 个用户")
        print(f"📊 总预测电量: {df_result['总电量(kWh)'].sum():.1f} kWh")
        print(f"📊 平均用电量: {df_result['总电量(kWh)'].mean():.1f} kWh")

        return df_result

    def evaluate_improved_model(self, df_improved_pred, target_date='2025-07-19'):
        """
        评估改进模型的效果
        """
        print(f"\n📈 评估改进模型效果...")

        # 读取真实数据
        actual_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250718-20250719.xlsx'
        df_actual = pd.read_excel(actual_file)

        # 提取目标日期数据
        df_actual['时间'] = pd.to_datetime(df_actual['时间'])
        target_date_obj = pd.to_datetime(target_date).date()
        df_actual_target = df_actual[df_actual['时间'].dt.date == target_date_obj].copy()

        if len(df_actual_target) == 0:
            print(f"❌ 没有找到 {target_date} 的真实数据")
            return None

        # 合并预测和真实数据
        df_compare = pd.merge(df_improved_pred, df_actual_target, on='户号', suffixes=('_pred', '_actual'))

        print(f"匹配用户数: {len(df_compare)}")

        if len(df_compare) == 0:
            print("❌ 没有匹配的用户")
            return None

        # 计算误差指标
        mae = mean_absolute_error(df_compare['总电量(kWh)_actual'], df_compare['总电量(kWh)_pred'])
        rmse = np.sqrt(mean_squared_error(df_compare['总电量(kWh)_actual'], df_compare['总电量(kWh)_pred']))
        r2 = r2_score(df_compare['总电量(kWh)_actual'], df_compare['总电量(kWh)_pred'])

        # 总量对比
        pred_total = df_compare['总电量(kWh)_pred'].sum()
        actual_total = df_compare['总电量(kWh)_actual'].sum()
        total_error_pct = (pred_total - actual_total) / actual_total * 100

        print(f"\n📊 改进模型评估结果:")
        print(f"  MAE: {mae:.1f} kWh")
        print(f"  RMSE: {rmse:.1f} kWh")
        print(f"  R²: {r2:.4f}")
        print(f"  总量误差: {total_error_pct:.1f}%")

        # 分时电量评估
        time_segments = ['尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']

        print(f"\n📊 分时电量评估:")
        for segment in time_segments:
            pred_col = f'{segment}_pred'
            actual_col = f'{segment}_actual'

            if pred_col in df_compare.columns and actual_col in df_compare.columns:
                pred_sum = df_compare[pred_col].sum()
                actual_sum = df_compare[actual_col].sum()
                error_pct = (pred_sum - actual_sum) / actual_sum * 100 if actual_sum > 0 else 0

                segment_r2 = r2_score(df_compare[actual_col], df_compare[pred_col])

                print(f"  {segment}: 误差{error_pct:.1f}%, R²={segment_r2:.4f}")

        return {
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'total_error_pct': total_error_pct,
            'comparison_data': df_compare
        }

    def save_improved_results(self, df_improved_pred, evaluation_results, target_date='2025-07-19'):
        """
        保存改进的预测结果
        """
        print(f"\n💾 保存改进的预测结果...")

        date_str = target_date.replace('-', '')
        output_file = f"/Users/<USER>/RiderProjects/Solution3/改进的用电量预测_{date_str}.xlsx"

        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 1. 改进的预测结果
                df_improved_pred.to_excel(writer, sheet_name='改进预测结果', index=False)

                # 2. 模型评估结果
                if evaluation_results:
                    eval_data = {
                        '指标': ['MAE (kWh)', 'RMSE (kWh)', 'R²', '总量误差 (%)'],
                        '数值': [
                            evaluation_results['mae'],
                            evaluation_results['rmse'],
                            evaluation_results['r2'],
                            evaluation_results['total_error_pct']
                        ]
                    }
                    pd.DataFrame(eval_data).to_excel(writer, sheet_name='模型评估', index=False)

                    # 3. 详细对比
                    if 'comparison_data' in evaluation_results:
                        comparison_cols = ['户号', '总电量(kWh)_pred', '总电量(kWh)_actual']
                        comparison_cols.extend([f'{seg}_pred' for seg in ['尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']])
                        comparison_cols.extend([f'{seg}_actual' for seg in ['尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']])

                        available_cols = [col for col in comparison_cols if col in evaluation_results['comparison_data'].columns]
                        evaluation_results['comparison_data'][available_cols].to_excel(writer, sheet_name='详细对比', index=False)

                # 4. 用户画像摘要
                if hasattr(self, 'user_profiles'):
                    profile_summary = self.user_profiles[['户号', '用电规模', '日均用电', '用电稳定性']].copy()
                    profile_summary.to_excel(writer, sheet_name='用户画像', index=False)

            print(f"✅ 改进结果已保存到: {output_file}")
            return output_file

        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return None

def main():
    """
    主函数
    """
    print("🔧 改进预测模型分析")
    print("📊 基于真实18-19号数据的误差分析和优化")
    print("="*70)

    analyzer = ImprovedPredictionModel()

    try:
        # 1. 分析当前预测误差
        print("步骤1: 分析当前预测误差")
        df_compare, problems = analyzer.analyze_prediction_errors()

        # 2. 创建用户画像
        print("\n步骤2: 创建详细用户画像")
        user_profiles = analyzer.create_user_profiles()

        # 3. 训练改进模型
        print("\n步骤3: 训练改进模型")
        model_success = analyzer.train_improved_models()

        if model_success:
            # 4. 创建改进预测（预测7月19日）
            print("\n步骤4: 创建改进预测")
            df_improved_pred = analyzer.create_improved_predictions('2025-07-19')

            if df_improved_pred is not None:
                # 5. 评估改进效果
                print("\n步骤5: 评估改进效果")
                evaluation_results = analyzer.evaluate_improved_model(df_improved_pred, '2025-07-19')

                # 6. 保存结果
                print("\n步骤6: 保存改进结果")
                output_file = analyzer.save_improved_results(df_improved_pred, evaluation_results, '2025-07-19')

                if output_file:
                    print(f"\n🎉 改进预测模型完成！")
                    print(f"📁 结果文件: {output_file}")

                    if evaluation_results:
                        print(f"\n📈 改进效果:")
                        print(f"  MAE: {evaluation_results['mae']:.1f} kWh")
                        print(f"  R²: {evaluation_results['r2']:.4f}")
                        print(f"  总量误差: {evaluation_results['total_error_pct']:.1f}%")

            else:
                print("❌ 改进预测失败")
        else:
            print("❌ 模型训练失败")

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
