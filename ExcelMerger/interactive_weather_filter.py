#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式连续天气筛选工具
允许用户自定义筛选条件
"""

import pandas as pd
import os
from consecutive_weather_filter import ConsecutiveWeatherFilter

def interactive_filter():
    """
    交互式筛选功能
    """
    print("="*60)
    print("交互式连续天气筛选工具")
    print("="*60)
    
    # 文件选择
    possible_files = [
        "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/合并结果_6月用电量含更新天气信息包含星期(1).xlsx",
        "最终合并结果_6月用电量含完整天气信息.xlsx",
        "合并结果_6月用电量含完整天气信息.xlsx",
        "合并结果_6月用电量信息含地区_含天气.xlsx"
    ]
    
    print("\n可用的输入文件:")
    available_files = []
    for i, file_path in enumerate(possible_files, 1):
        if os.path.exists(file_path):
            available_files.append(file_path)
            print(f"{i}. {os.path.basename(file_path)}")
    
    if not available_files:
        print("错误: 未找到可用的输入文件")
        return
    
    # 选择文件
    if len(available_files) == 1:
        input_file = available_files[0]
        print(f"\n自动选择文件: {os.path.basename(input_file)}")
    else:
        while True:
            try:
                choice = int(input(f"\n请选择文件 (1-{len(available_files)}): "))
                if 1 <= choice <= len(available_files):
                    input_file = available_files[choice-1]
                    break
                else:
                    print("无效选择，请重新输入")
            except ValueError:
                print("请输入数字")
    
    # 设置连续天数
    while True:
        try:
            min_days = int(input("\n请输入最少连续天数 (建议3-7天): "))
            if min_days >= 1:
                break
            else:
                print("连续天数必须大于0")
        except ValueError:
            print("请输入有效数字")
    
    # 设置输出文件名
    default_output = f"连续{min_days}天相同天气筛选结果.xlsx"
    output_file = input(f"\n输出文件名 (默认: {default_output}): ").strip()
    if not output_file:
        output_file = default_output
    
    # 执行筛选
    print(f"\n开始筛选连续{min_days}天相同天气的数据...")
    filter_tool = ConsecutiveWeatherFilter()
    success = filter_tool.process(input_file, output_file, min_days=min_days)
    
    if success:
        print(f"\n筛选完成！结果已保存到: {output_file}")
        
        # 询问是否查看详细统计
        view_stats = input("\n是否查看详细统计信息? (y/n): ").lower().strip()
        if view_stats in ['y', 'yes', '是']:
            show_detailed_stats(output_file)
    else:
        print("\n筛选失败，请检查输入文件和参数")

def show_detailed_stats(result_file):
    """
    显示详细统计信息
    """
    try:
        if not os.path.exists(result_file):
            print("结果文件不存在")
            return
        
        df = pd.read_excel(result_file)
        
        print(f"\n" + "="*50)
        print(f"详细统计信息")
        print(f"="*50)
        
        print(f"\n基本信息:")
        print(f"  总记录数: {len(df)}")
        print(f"  时间范围: {df['时间'].min()} 到 {df['时间'].max()}")
        
        if '连续天气类型' in df.columns:
            print(f"\n连续天气时间段:")
            periods = df.groupby(['地区', '连续天气类型', '连续开始日期', '连续结束日期', '连续天数']).size().reset_index()
            periods.columns = ['地区', '天气类型', '开始日期', '结束日期', '连续天数', '记录数']
            
            for _, period in periods.iterrows():
                print(f"  {period['地区']}: {period['天气类型']} "
                      f"({period['开始日期'].strftime('%m-%d')} 到 {period['结束日期'].strftime('%m-%d')}, "
                      f"{period['连续天数']}天, {period['记录数']}条记录)")
        
        print(f"\n用电量统计:")
        print(f"  总用电量: {df['总电量(kWh)'].sum():.1f} kWh")
        print(f"  平均用电量: {df['总电量(kWh)'].mean():.1f} kWh")
        print(f"  最大用电量: {df['总电量(kWh)'].max():.1f} kWh")
        print(f"  最小用电量: {df['总电量(kWh)'].min():.1f} kWh")
        
        # 按天统计
        if len(df) > 0:
            print(f"\n每日用电量统计:")
            daily_stats = df.groupby(df['时间'].dt.date)['总电量(kWh)'].agg(['count', 'sum', 'mean']).round(2)
            daily_stats.columns = ['记录数', '总用电量', '平均用电量']
            print(daily_stats)
        
        # 按地区统计
        if '地区' in df.columns:
            print(f"\n各地区用电量统计:")
            region_stats = df.groupby('地区')['总电量(kWh)'].agg(['count', 'sum', 'mean']).round(2)
            region_stats.columns = ['记录数', '总用电量', '平均用电量']
            print(region_stats)
            
    except Exception as e:
        print(f"显示统计信息时出错: {e}")

def quick_analysis():
    """
    快速分析不同连续天数的结果
    """
    print("="*60)
    print("快速分析 - 不同连续天数的筛选结果")
    print("="*60)
    
    # 选择输入文件
    possible_files = [
        "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/合并结果_6月用电量含更新天气信息包含星期(1).xlsx",
        "最终合并结果_6月用电量含完整天气信息.xlsx",
        "合并结果_6月用电量含完整天气信息.xlsx"
    ]
    
    input_file = None
    for file_path in possible_files:
        if os.path.exists(file_path):
            input_file = file_path
            break
    
    if input_file is None:
        print("错误: 未找到可用的输入文件")
        return
    
    print(f"使用文件: {os.path.basename(input_file)}")
    
    # 分析不同连续天数
    days_to_test = [3, 4, 5, 6, 7]
    results_summary = []
    
    for min_days in days_to_test:
        print(f"\n分析连续{min_days}天相同天气...")
        
        filter_tool = ConsecutiveWeatherFilter()
        filter_tool.load_data(input_file)
        filter_tool.preprocess_data()
        filter_tool.find_consecutive_weather_periods(min_days)
        
        period_count = len(filter_tool.consecutive_periods)
        
        if period_count > 0:
            # 统计天气类型
            weather_types = {}
            total_days = 0
            for period in filter_tool.consecutive_periods:
                weather = period['天气']
                if weather not in weather_types:
                    weather_types[weather] = 0
                weather_types[weather] += 1
                total_days += period['连续天数']
            
            results_summary.append({
                '连续天数': min_days,
                '时间段数量': period_count,
                '总天数': total_days,
                '天气类型': list(weather_types.keys()),
                '详情': filter_tool.consecutive_periods
            })
            
            print(f"  找到 {period_count} 个时间段，总计 {total_days} 天")
            print(f"  天气类型: {', '.join(weather_types.keys())}")
        else:
            print(f"  未找到连续{min_days}天相同天气的时间段")
    
    # 显示汇总结果
    print(f"\n" + "="*60)
    print(f"汇总结果")
    print(f"="*60)
    
    for result in results_summary:
        print(f"\n连续{result['连续天数']}天: {result['时间段数量']}个时间段")
        for period in result['详情']:
            print(f"  {period['地区']}: {period['天气']} "
                  f"({period['开始日期']} 到 {period['结束日期']}, {period['连续天数']}天)")

def main():
    """主函数"""
    while True:
        print("\n" + "="*60)
        print("连续天气筛选工具菜单")
        print("="*60)
        print("1. 交互式筛选")
        print("2. 快速分析（测试不同连续天数）")
        print("3. 退出")
        
        choice = input("\n请选择功能 (1-3): ").strip()
        
        if choice == '1':
            interactive_filter()
        elif choice == '2':
            quick_analysis()
        elif choice == '3':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
