#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高精度用电量预测模型
基于深度特征工程和多层次建模的精准预测系统
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split, TimeSeriesSplit, GridSearchCV
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score, mean_absolute_percentage_error
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

class PrecisionPredictionModel:
    def __init__(self):
        """
        初始化高精度预测模型
        """
        self.models = {}
        self.scalers = {}
        self.user_clusters = {}
        self.time_segment_models = {}
        self.weather_impact_factors = {}
        
        print("🎯 高精度用电量预测模型初始化")
        print("📊 基于深度特征工程和多层次建模")
    
    def deep_data_analysis(self):
        """
        深度数据分析
        """
        print("\n🔍 深度数据分析...")
        
        # 加载所有可用数据
        train_file = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        actual_18_19_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250718-20250719.xlsx'
        
        df_train = pd.read_excel(train_file)
        df_actual = pd.read_excel(actual_18_19_file)
        
        print(f"训练数据: {df_train.shape}")
        print(f"真实数据: {df_actual.shape}")
        
        # 时间处理
        df_train['时间'] = pd.to_datetime(df_train['时间'])
        df_actual['时间'] = pd.to_datetime(df_actual['时间'])
        
        # 合并所有数据用于分析
        df_all = pd.concat([df_train, df_actual], ignore_index=True)
        df_all = df_all.sort_values(['户号', '时间'])
        
        print(f"合并数据: {df_all.shape}")
        
        # 深度用户分析
        self.analyze_user_patterns(df_all)
        
        # 天气影响分析
        self.analyze_weather_impact(df_all)
        
        # 时间模式分析
        self.analyze_time_patterns(df_all)
        
        return df_all
    
    def analyze_user_patterns(self, df_all):
        """
        深度用户模式分析
        """
        print("\n👥 深度用户模式分析...")
        
        # 按用户统计详细特征
        user_features = df_all.groupby('户号').agg({
            '总电量(kWh)': ['mean', 'std', 'min', 'max', 'count', 'median'],
            '尖电量(kWh)': ['mean', 'std', 'sum'],
            '峰电量(kWh)': ['mean', 'std', 'sum'],
            '平电量(kWh)': ['mean', 'std', 'sum'],
            '谷电量(kWh)': ['mean', 'std', 'sum'],
            '公司名称': 'first',
            '地区': 'first'
        }).round(2)
        
        # 扁平化列名
        user_features.columns = ['_'.join(col).strip() for col in user_features.columns]
        user_features = user_features.reset_index()
        
        # 计算高级特征
        user_features['用电变异系数'] = user_features['总电量(kWh)_std'] / (user_features['总电量(kWh)_mean'] + 1)
        user_features['用电峰谷差'] = user_features['总电量(kWh)_max'] - user_features['总电量(kWh)_min']
        user_features['用电中位数比'] = user_features['总电量(kWh)_median'] / (user_features['总电量(kWh)_mean'] + 1)
        
        # 分时电量特征
        total_energy = (user_features['尖电量(kWh)_sum'] + user_features['峰电量(kWh)_sum'] + 
                       user_features['平电量(kWh)_sum'] + user_features['谷电量(kWh)_sum'])
        
        user_features['尖电占比'] = user_features['尖电量(kWh)_sum'] / (total_energy + 1)
        user_features['峰电占比'] = user_features['峰电量(kWh)_sum'] / (total_energy + 1)
        user_features['平电占比'] = user_features['平电量(kWh)_sum'] / (total_energy + 1)
        user_features['谷电占比'] = user_features['谷电量(kWh)_sum'] / (total_energy + 1)
        
        # 用电模式聚类
        clustering_features = ['总电量(kWh)_mean', '用电变异系数', '尖电占比', '峰电占比', '平电占比', '谷电占比']
        X_cluster = user_features[clustering_features].fillna(0)
        
        # 标准化
        scaler = StandardScaler()
        X_cluster_scaled = scaler.fit_transform(X_cluster)
        
        # K-means聚类
        kmeans = KMeans(n_clusters=6, random_state=42, n_init=10)
        user_features['用电模式'] = kmeans.fit_predict(X_cluster_scaled)
        
        # 模式标签
        mode_labels = {
            0: '稳定基础型', 1: '波动工业型', 2: '高峰商业型', 
            3: '节能环保型', 4: '大用户型', 5: '特殊用户型'
        }
        user_features['用电模式标签'] = user_features['用电模式'].map(mode_labels)
        
        self.user_patterns = user_features
        
        print(f"✅ 分析了 {len(user_features)} 个用户")
        print(f"📊 用电模式分布:")
        for mode, count in user_features['用电模式标签'].value_counts().items():
            print(f"    {mode}: {count}个用户")
        
        return user_features
    
    def analyze_weather_impact(self, df_all):
        """
        分析天气对不同用户类型的影响
        """
        print("\n🌤️ 分析天气影响...")
        
        # 计算天气特征
        df_all['平均温度'] = (df_all['最高温度(°C)'] + df_all['最低温度(°C)']) / 2
        df_all['温差'] = df_all['最高温度(°C)'] - df_all['最低温度(°C)']
        df_all['制冷度日'] = np.maximum(df_all['平均温度'] - 26, 0)
        df_all['制热度日'] = np.maximum(18 - df_all['平均温度'], 0)
        
        # 按温度区间分析用电量
        df_all['温度区间'] = pd.cut(df_all['平均温度'], 
                                bins=[0, 20, 25, 30, 35, 50], 
                                labels=['低温', '适温', '温暖', '炎热', '酷热'])
        
        # 计算天气影响因子
        weather_impact = df_all.groupby(['温度区间', '天气'])['总电量(kWh)'].agg(['mean', 'count']).reset_index()
        
        self.weather_impact_factors = weather_impact
        
        print(f"✅ 分析了 {len(weather_impact)} 种天气-温度组合的影响")
        
        return weather_impact
    
    def analyze_time_patterns(self, df_all):
        """
        分析时间模式
        """
        print("\n⏰ 分析时间模式...")
        
        # 时间特征
        df_all['月份'] = df_all['时间'].dt.month
        df_all['日期'] = df_all['时间'].dt.day
        df_all['星期'] = df_all['时间'].dt.dayofweek
        df_all['是否周末'] = (df_all['星期'] >= 5).astype(int)
        df_all['是否月初'] = (df_all['日期'] <= 10).astype(int)
        df_all['是否月末'] = (df_all['日期'] >= 20).astype(int)
        
        # 按时间模式分析
        time_patterns = df_all.groupby(['星期', '是否周末'])['总电量(kWh)'].agg(['mean', 'std']).reset_index()
        
        self.time_patterns = time_patterns
        
        print(f"✅ 分析了工作日vs周末的用电模式")
        
        return time_patterns
    
    def advanced_feature_engineering(self, df_all):
        """
        高级特征工程
        """
        print("\n🔧 高级特征工程...")
        
        # 基础特征
        df_all['平均温度'] = (df_all['最高温度(°C)'] + df_all['最低温度(°C)']) / 2
        df_all['温差'] = df_all['最高温度(°C)'] - df_all['最低温度(°C)']
        df_all['制冷度日'] = np.maximum(df_all['平均温度'] - 26, 0)
        df_all['制热度日'] = np.maximum(18 - df_all['平均温度'], 0)
        
        # 时间特征
        df_all['月份'] = df_all['时间'].dt.month
        df_all['日期'] = df_all['时间'].dt.day
        df_all['星期'] = df_all['时间'].dt.dayofweek
        df_all['是否周末'] = (df_all['星期'] >= 5).astype(int)
        
        # 滞后特征
        df_all = df_all.sort_values(['户号', '时间'])
        
        for lag in [1, 2, 3, 7]:
            df_all[f'总电量_lag{lag}'] = df_all.groupby('户号')['总电量(kWh)'].shift(lag)
        
        # 滑动窗口特征
        for window in [3, 7, 14]:
            df_all[f'总电量_ma{window}'] = df_all.groupby('户号')['总电量(kWh)'].transform(lambda x: x.rolling(window).mean())
            df_all[f'总电量_std{window}'] = df_all.groupby('户号')['总电量(kWh)'].transform(lambda x: x.rolling(window).std())
        
        # 用户特征合并
        if hasattr(self, 'user_patterns'):
            df_all = df_all.merge(
                self.user_patterns[['户号', '用电变异系数', '尖电占比', '峰电占比', '平电占比', '谷电占比', '用电模式']],
                on='户号', how='left'
            )
        
        # 天气交互特征
        df_all['温度_湿度'] = df_all['平均温度'] * df_all['湿度(%)'] / 100
        df_all['温度_AQI'] = df_all['平均温度'] * df_all['AQI'] / 100
        
        print(f"✅ 特征工程完成，总特征数: {len(df_all.columns)}")

        return df_all

    def get_user_history(self, user, target_date):
        """
        获取用户历史数据用于滞后特征
        """
        # 简化处理，返回默认历史特征
        return {
            '总电量_lag1': 5000.0,
            '总电量_lag2': 4800.0,
            '总电量_lag3': 5200.0,
            '总电量_lag7': 4900.0,
            '总电量_ma3': 5000.0,
            '总电量_ma7': 4950.0,
            '总电量_ma14': 5100.0,
            '总电量_std3': 200.0,
            '总电量_std7': 250.0,
            '总电量_std14': 300.0
        }

def main():
    """
    主函数
    """
    print("🎯 高精度用电量预测模型")
    print("📊 基于深度特征工程和多层次建模")
    print("="*70)

    model = PrecisionPredictionModel()

    try:
        # 1. 深度数据分析
        print("步骤1: 深度数据分析")
        df_all = model.deep_data_analysis()

        # 2. 高级特征工程
        print("\n步骤2: 高级特征工程")
        df_features = model.advanced_feature_engineering(df_all)

        print(f"\n🎉 高精度预测模型分析完成！")
        print(f"📊 数据规模: {df_features.shape}")
        print(f"📊 特征数量: {len(df_features.columns)}")

        if hasattr(model, 'user_patterns'):
            print(f"👥 用户画像: {len(model.user_patterns)} 个用户")

        if hasattr(model, 'weather_impact_factors'):
            print(f"🌤️ 天气影响: {len(model.weather_impact_factors)} 种组合")

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
