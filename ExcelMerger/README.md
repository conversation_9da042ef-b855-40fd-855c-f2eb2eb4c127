# Excel文件合并工具

这个工具可以将售电签约客户.xlsx中的地区信息匹配到110kV以下用户历史用电量信息查询文件中。

## 功能说明

程序会根据户号将两个Excel文件进行匹配，将客户文件中的地区信息添加到用电量文件中。

## 文件说明

1. **excel_merger.py** - 自动版本，会自动识别列名进行匹配
2. **excel_merger_interactive.py** - 交互版本，允许用户手动选择列名
3. **requirements.txt** - Python依赖包列表

## 安装依赖

在运行程序前，请先安装所需的Python包：

```bash
pip install -r requirements.txt
```

或者单独安装：

```bash
pip install pandas openpyxl xlrd
```

## 使用方法

### 方法1：自动版本（推荐）

1. 将以下文件放在同一目录下：
   - `excel_merger.py`
   - `售电签约客户.xlsx`
   - `110kV以下用户历史用电量信息查询20250701-20250713(1).xlsx`

2. 运行程序：
   ```bash
   python excel_merger.py
   ```

3. 程序会自动识别列名并进行匹配，结果保存为 `合并结果_用电量信息含地区.xlsx`

### 方法2：交互版本

1. 运行程序：
   ```bash
   python excel_merger_interactive.py
   ```

2. 按照提示输入文件路径（可以使用默认值）

3. 程序会显示文件预览，然后让您选择：
   - 客户文件中的户号列
   - 客户文件中的地区列  
   - 用电量文件中的户号列

4. 确认选择后，程序会进行匹配并保存结果

## 程序特点

- **自动列名识别**：程序会尝试自动识别包含"户号"、"地区"等关键词的列
- **匹配统计**：显示匹配成功率和未匹配的记录数量
- **数据预览**：处理前后都会显示数据预览
- **错误处理**：包含完善的错误处理机制
- **灵活配置**：支持自动和手动两种模式

## 注意事项

1. 确保Excel文件格式正确，包含必要的户号和地区列
2. 户号格式需要在两个文件中保持一致
3. 程序会在用电量文件中新增"地区"列
4. 如果存在未匹配的记录，程序会显示相关统计信息

## 常见问题

**Q: 程序提示找不到文件怎么办？**
A: 请确保Excel文件与Python程序在同一目录下，或使用交互版本手动指定文件路径。

**Q: 匹配率很低怎么办？**
A: 请检查两个文件中的户号格式是否一致，可能需要进行数据清理。

**Q: 程序无法自动识别列名怎么办？**
A: 使用交互版本 `excel_merger_interactive.py`，手动选择正确的列名。

## 输出文件

程序会生成一个新的Excel文件，包含原用电量文件的所有数据，并新增"地区"列。匹配成功的记录会显示对应的地区信息，未匹配的记录地区列为空。
