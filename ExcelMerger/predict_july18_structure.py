#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仿照实际用电量表结构预测7月18日的户号用电量
基于7月16日表结构：表计号、户号、时间、总电量(kWh)、尖电量(kWh)、峰电量(kWh)、平电量(kWh)、谷电量(kWh)
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
from bs4 import BeautifulSoup
import time
import random
import re
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class July18StructurePrediction:
    def __init__(self):
        """
        初始化7月18日按结构预测系统
        """
        self.model = None
        self.encoders = {}
        self.feature_names = []
        self.weather_data = {}
        self.user_mapping = {}  # 户号到表计号的映射
        
        print("📊 7月18日按结构预测系统初始化")
        print("🎯 仿照实际表结构生成预测结果")
    
    def load_target_structure(self, reference_file):
        """
        加载参考表结构
        """
        print(f"\n📋 加载参考表结构...")
        
        try:
            df_ref = pd.read_excel(reference_file)
            print(f"参考表结构: {df_ref.shape}")
            print(f"列名: {list(df_ref.columns)}")
            
            # 提取用户映射关系
            self.user_mapping = df_ref.set_index('户号')['表计号'].to_dict()
            
            print(f"✅ 成功加载 {len(self.user_mapping)} 个用户的映射关系")
            
            return df_ref
            
        except Exception as e:
            print(f"❌ 加载参考表失败: {e}")
            return None
    
    def scrape_weather_for_july18(self):
        """
        爬取7月18日的天气数据
        """
        print(f"\n🌤️ 爬取7月18日的天气数据...")
        
        cities = {
            '杭州': 'hangzhou',
            '宁波': 'ningbo', 
            '温州': 'wenzhou',
            '嘉兴': 'jiaxing',
            '湖州': 'huzhou',
            '绍兴': 'shaoxing'
        }
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive'
        }
        
        weather_results = {}
        
        for city_name, city_code in cities.items():
            try:
                url = f"https://www.tianqi24.com/{city_code}/history202507.html"
                print(f"  爬取 {city_name}: {url}")
                
                response = requests.get(url, headers=headers, timeout=15)
                response.encoding = 'utf-8'
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 查找7月18日数据
                    weather_lists = soup.find_all('ul', class_='col6')
                    
                    for ul in weather_lists:
                        items = ul.find_all('li')
                        
                        for item in items[1:]:  # 跳过表头
                            divs = item.find_all('div')
                            
                            if len(divs) >= 7:
                                date_text = divs[0].get_text().strip()
                                
                                if '07-18' in date_text or ('18' in date_text and '07' in date_text):
                                    print(f"    ✅ 找到{city_name}7月18日数据")
                                    
                                    # 解析天气数据
                                    weather_text = divs[1].get_text().strip()
                                    high_temp = self.extract_number(divs[2].get_text())
                                    low_temp = self.extract_number(divs[3].get_text())
                                    aqi = self.extract_number(divs[4].get_text())
                                    wind = divs[5].get_text().strip()
                                    precipitation = self.extract_float(divs[6].get_text())
                                    
                                    weather_results[city_name] = {
                                        'weather': weather_text.split('/')[0] if '/' in weather_text else weather_text,
                                        'temp_max': high_temp,
                                        'temp_min': low_temp,
                                        'aqi': aqi,
                                        'wind': wind,
                                        'precipitation': precipitation
                                    }
                                    
                                    print(f"      天气: {weather_results[city_name]['weather']}")
                                    print(f"      温度: {high_temp}°C/{low_temp}°C")
                                    print(f"      AQI: {aqi}")
                                    break
                
                # 请求间隔
                time.sleep(random.uniform(1, 3))
                
            except Exception as e:
                print(f"    ❌ {city_name}爬取失败: {e}")
        
        if weather_results:
            # 计算平均天气数据
            temps_max = [w['temp_max'] for w in weather_results.values() if w['temp_max']]
            temps_min = [w['temp_min'] for w in weather_results.values() if w['temp_min']]
            aqis = [w['aqi'] for w in weather_results.values() if w['aqi']]
            precipitations = [w['precipitation'] for w in weather_results.values() if w['precipitation']]
            
            weathers = [w['weather'] for w in weather_results.values()]
            main_weather = max(set(weathers), key=weathers.count) if weathers else '晴'
            
            self.weather_data = {
                '最高温度(°C)': np.mean(temps_max) if temps_max else 37.0,
                '最低温度(°C)': np.mean(temps_min) if temps_min else 28.0,
                '湿度(%)': 72.0,  # 7月18日典型湿度
                'AQI': np.mean(aqis) if aqis else 35.0,
                '降水量(mm)': np.mean(precipitations) if precipitations else 0.0,
                '气压(hPa)': 1013.0,
                '天气': main_weather,
                '风向': '南风',
                '日期类型': '工作日'  # 7月18日是周五，工作日
            }
            
            print(f"✅ 7月18日天气数据获取成功: {len(weather_results)}个城市")
            print(f"📊 平均天气条件:")
            for key, value in self.weather_data.items():
                if isinstance(value, float):
                    print(f"   {key}: {value:.1f}")
                else:
                    print(f"   {key}: {value}")
            
            return True
        else:
            print("❌ 天气数据爬取失败，使用默认值")
            self.weather_data = {
                '最高温度(°C)': 37.0,
                '最低温度(°C)': 28.0,
                '湿度(%)': 72.0,
                'AQI': 35.0,
                '降水量(mm)': 0.0,
                '气压(hPa)': 1013.0,
                '天气': '晴',
                '风向': '南风',
                '日期类型': '工作日'
            }
            return False
    
    def extract_number(self, text):
        """提取数字"""
        match = re.search(r'(\d+)', str(text))
        return int(match.group(1)) if match else None
    
    def extract_float(self, text):
        """提取浮点数"""
        match = re.search(r'(\d+(?:\.\d+)?)', str(text))
        return float(match.group(1)) if match else 0.0
    
    def train_prediction_model(self):
        """
        训练预测模型
        """
        print("\n🤖 训练预测模型...")
        
        # 加载6月训练数据
        train_file = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        df = pd.read_excel(train_file)
        
        print(f"训练数据: {df.shape}")
        
        # 数据清洗
        df = df[df['总电量(kWh)'] >= 0]
        df = df[df['总电量(kWh)'] < df['总电量(kWh)'].quantile(0.99)]
        df = df.dropna(subset=['户号', '总电量(kWh)'])
        
        # 特征工程
        df['时间'] = pd.to_datetime(df['时间'])
        df['月份'] = df['时间'].dt.month
        df['日期'] = df['时间'].dt.day
        df['星期'] = df['时间'].dt.dayofweek
        df['是否周末'] = (df['星期'] >= 5).astype(int)
        
        # 天气特征
        df['平均温度'] = (df['最高温度(°C)'] + df['最低温度(°C)']) / 2
        df['温差'] = df['最高温度(°C)'] - df['最低温度(°C)']
        df['制冷度日'] = np.maximum(df['平均温度'] - 26, 0)
        
        # 用户历史特征
        df = df.sort_values(['户号', '时间'])
        df['历史平均用电'] = df.groupby('户号')['总电量(kWh)'].transform(lambda x: x.expanding().mean().shift(1))
        
        # 选择特征
        feature_columns = [
            '月份', '日期', '星期', '是否周末',
            '最高温度(°C)', '最低温度(°C)', '平均温度', '温差',
            '湿度(%)', 'AQI', '降水量(mm)',
            '制冷度日', '历史平均用电'
        ]
        
        # 分类特征编码
        categorical_features = ['天气', '风向', '地区', '日期类型']
        
        for cat_feature in categorical_features:
            if cat_feature in df.columns:
                encoder = LabelEncoder()
                df[f'{cat_feature}_encoded'] = encoder.fit_transform(df[cat_feature].astype(str))
                feature_columns.append(f'{cat_feature}_encoded')
                self.encoders[cat_feature] = encoder
        
        # 准备训练数据
        available_features = [col for col in feature_columns if col in df.columns]
        X = df[available_features].fillna(0)
        y = df['总电量(kWh)']
        
        print(f"特征数: {len(available_features)}")
        print(f"样本数: {len(X)}")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 训练模型
        self.model = GradientBoostingRegressor(n_estimators=100, random_state=42)
        self.model.fit(X_train, y_train)
        
        # 评估模型
        y_pred = self.model.predict(X_test)
        r2 = r2_score(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        
        print(f"✅ 模型训练完成")
        print(f"   R²: {r2:.4f}")
        print(f"   MAE: {mae:.2f} kWh")
        
        self.feature_names = available_features

        return True

    def predict_july18_by_structure(self, reference_file):
        """
        按照参考表结构预测7月18日的用电量
        """
        print(f"\n🔮 预测7月18日的用电量...")

        if self.model is None:
            print("❌ 模型未训练")
            return None

        # 加载参考表获取用户列表
        df_ref = pd.read_excel(reference_file)
        target_users = df_ref['户号'].unique()

        print(f"目标用户数: {len(target_users)}")

        # 加载训练数据获取用户历史信息
        train_file = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        df_train = pd.read_excel(train_file)

        # 创建预测数据
        pred_data = []

        for user in target_users:
            # 查找用户历史信息
            user_info = df_train[df_train['户号'] == user]

            if len(user_info) > 0:
                avg_power = user_info['总电量(kWh)'].mean()
                region = user_info['地区'].iloc[0] if '地区' in user_info.columns else '杭州'
            else:
                avg_power = 3000.0  # 默认值
                region = '杭州'

            pred_data.append({
                '户号': user,
                '历史平均用电': avg_power,
                '地区': region,
                '时间': '2025-07-18'
            })

        df_pred = pd.DataFrame(pred_data)

        # 添加天气特征
        for key, value in self.weather_data.items():
            df_pred[key] = value

        # 添加时间特征 - 7月18日是周五
        df_pred['时间'] = pd.to_datetime(df_pred['时间'])
        df_pred['月份'] = 7
        df_pred['日期'] = 18
        df_pred['星期'] = 4  # 周五
        df_pred['是否周末'] = 0  # 工作日

        # 添加天气计算特征
        df_pred['平均温度'] = (df_pred['最高温度(°C)'] + df_pred['最低温度(°C)']) / 2
        df_pred['温差'] = df_pred['最高温度(°C)'] - df_pred['最低温度(°C)']
        df_pred['制冷度日'] = np.maximum(df_pred['平均温度'] - 26, 0)

        # 分类特征编码
        for cat_feature, encoder in self.encoders.items():
            if cat_feature in df_pred.columns:
                try:
                    df_pred[f'{cat_feature}_encoded'] = encoder.transform(df_pred[cat_feature].astype(str))
                except:
                    df_pred[f'{cat_feature}_encoded'] = 0

        # 准备预测特征
        X_pred = df_pred[self.feature_names].fillna(0)

        # 进行预测
        predictions = self.model.predict(X_pred)
        predictions = np.maximum(predictions, 0)  # 确保非负

        # 创建结果表，完全仿照参考表结构
        result_data = []

        for i, user in enumerate(target_users):
            # 获取表计号
            meter_id = self.user_mapping.get(user, user)  # 如果没有映射，使用户号

            # 预测总电量
            total_power = predictions[i]

            # 基于历史比例分配分时电量
            ratios = self.get_time_segment_ratios(user, df_train)

            result_data.append({
                '表计号': meter_id,
                '户号': user,
                '时间': '2025-07-18',
                '总电量(kWh)': round(total_power, 1),
                '尖电量(kWh)': round(total_power * ratios['尖'], 1),
                '峰电量(kWh)': round(total_power * ratios['峰'], 1),
                '平电量(kWh)': round(total_power * ratios['平'], 1),
                '谷电量(kWh)': round(total_power * ratios['谷'], 1)
            })

        df_result = pd.DataFrame(result_data)

        print(f"✅ 预测完成: {len(df_result)} 个用户")
        print(f"📊 总预测电量: {df_result['总电量(kWh)'].sum():.1f} kWh")
        print(f"📊 平均用电量: {df_result['总电量(kWh)'].mean():.1f} kWh")

        return df_result

    def get_time_segment_ratios(self, user, df_train):
        """
        获取用户的分时电量比例
        """
        user_data = df_train[df_train['户号'] == user]

        if len(user_data) > 0:
            # 计算该用户的历史分时比例
            total_sum = user_data['总电量(kWh)'].sum()
            if total_sum > 0:
                ratios = {
                    '尖': user_data['尖电量(kWh)'].sum() / total_sum,
                    '峰': user_data['峰电量(kWh)'].sum() / total_sum,
                    '平': user_data['平电量(kWh)'].sum() / total_sum,
                    '谷': user_data['谷电量(kWh)'].sum() / total_sum
                }
            else:
                ratios = {'尖': 0.05, '峰': 0.35, '平': 0.40, '谷': 0.20}
        else:
            # 使用默认比例
            ratios = {'尖': 0.05, '峰': 0.35, '平': 0.40, '谷': 0.20}

        # 确保比例和为1
        total_ratio = sum(ratios.values())
        if total_ratio > 0:
            for key in ratios:
                ratios[key] = ratios[key] / total_ratio

        return ratios

    def save_july18_prediction(self, df_result):
        """
        保存7月18日预测结果，完全仿照参考表格式
        """
        print(f"\n💾 保存7月18日预测结果...")

        # 生成文件名
        output_file = f"/Users/<USER>/RiderProjects/Solution3/110kV以下用户用电量预测_20250718.xlsx"

        try:
            # 保存为Excel文件，格式完全一致
            df_result.to_excel(output_file, index=False)

            print(f"✅ 预测结果已保存到: {output_file}")
            print(f"📊 文件结构与参考表完全一致")
            print(f"📊 包含 {len(df_result)} 条记录")

            # 显示统计信息
            print(f"\n📈 7月18日预测统计:")
            print(f"   总电量: {df_result['总电量(kWh)'].sum():.1f} kWh")
            print(f"   尖电量: {df_result['尖电量(kWh)'].sum():.1f} kWh")
            print(f"   峰电量: {df_result['峰电量(kWh)'].sum():.1f} kWh")
            print(f"   平电量: {df_result['平电量(kWh)'].sum():.1f} kWh")
            print(f"   谷电量: {df_result['谷电量(kWh)'].sum():.1f} kWh")

            return output_file

        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return None

def main():
    """
    主函数 - 预测7月18日
    """
    print("📊 7月18日按结构预测系统")
    print("🎯 仿照实际表结构生成7月18日预测结果")
    print("="*60)

    # 参数设置
    reference_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250716-20250716.xlsx'

    predictor = July18StructurePrediction()

    try:
        # 1. 加载参考表结构
        print("步骤1: 加载参考表结构")
        df_ref = predictor.load_target_structure(reference_file)

        if df_ref is None:
            print("❌ 无法加载参考表")
            return

        # 2. 爬取7月18日天气数据
        print(f"\n步骤2: 爬取7月18日天气数据")
        weather_success = predictor.scrape_weather_for_july18()

        # 3. 训练预测模型
        print(f"\n步骤3: 训练预测模型")
        model_success = predictor.train_prediction_model()

        if model_success:
            # 4. 进行预测
            print(f"\n步骤4: 预测7月18日用电量")
            predictions = predictor.predict_july18_by_structure(reference_file)

            if predictions is not None:
                # 5. 保存结果
                print(f"\n步骤5: 保存预测结果")
                output_file = predictor.save_july18_prediction(predictions)

                if output_file:
                    print(f"\n🎉 7月18日预测完成！")
                    print(f"📁 结果文件: {output_file}")
                    print(f"📊 文件格式与参考表完全一致")

                    # 显示使用的天气条件
                    print(f"\n🌤️ 使用的7月18日天气条件:")
                    for key, value in predictor.weather_data.items():
                        if isinstance(value, float):
                            print(f"   {key}: {value:.1f}")
                        else:
                            print(f"   {key}: {value}")

                    # 显示前5条预测结果
                    print(f"\n📋 前5条预测结果:")
                    print(predictions.head().to_string(index=False))

                    # 显示用电量最大的5个用户
                    top5 = predictions.nlargest(5, '总电量(kWh)')
                    print(f"\n🏆 预测用电量前5名:")
                    for i, (_, row) in enumerate(top5.iterrows(), 1):
                        print(f"   {i}. 户号{row['户号']}: {row['总电量(kWh)']} kWh")

            else:
                print("❌ 预测失败")
        else:
            print("❌ 模型训练失败")

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
