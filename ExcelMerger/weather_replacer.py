#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气数据替换工具
根据日期和地区将天气数据替换到用电量文件中
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

class WeatherReplacer:
    def __init__(self, weather_file, electricity_file):
        """
        初始化天气替换器
        
        Args:
            weather_file: 天气数据文件路径
            electricity_file: 用电量数据文件路径
        """
        self.weather_file = weather_file
        self.electricity_file = electricity_file
        self.weather_df = None
        self.electricity_df = None
        self.load_data()
    
    def load_data(self):
        """加载数据文件"""
        try:
            # 加载天气数据
            print(f"正在加载天气数据: {os.path.basename(self.weather_file)}")
            self.weather_df = pd.read_excel(self.weather_file)
            print(f"天气数据加载成功，共 {len(self.weather_df)} 行")
            
            # 加载用电量数据
            print(f"正在加载用电量数据: {os.path.basename(self.electricity_file)}")
            self.electricity_df = pd.read_excel(self.electricity_file)
            print(f"用电量数据加载成功，共 {len(self.electricity_df)} 行")
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def prepare_weather_data(self):
        """准备天气数据，建立日期-地区-天气的映射"""
        try:
            # 处理天气数据
            weather_clean = self.weather_df.copy()
            
            # 确保日期格式正确
            weather_clean['日期'] = pd.to_datetime(weather_clean['日期'])
            weather_clean['日期_str'] = weather_clean['日期'].dt.strftime('%Y-%m-%d')
            
            # 地区名称标准化（天气数据中是"城市"列）
            weather_clean['地区'] = weather_clean['城市']
            
            # 创建日期-地区的组合键
            weather_clean['date_region_key'] = weather_clean['日期_str'] + '_' + weather_clean['地区']
            
            # 创建天气映射字典
            weather_mapping = dict(zip(weather_clean['date_region_key'], weather_clean['天气情况']))
            
            print(f"\n天气数据准备完成:")
            print(f"  时间范围: {weather_clean['日期'].min().date()} 至 {weather_clean['日期'].max().date()}")
            print(f"  包含地区: {list(weather_clean['地区'].unique())}")
            print(f"  天气状况: {list(weather_clean['天气情况'].unique())}")
            print(f"  映射记录数: {len(weather_mapping)}")
            
            return weather_mapping
            
        except Exception as e:
            print(f"天气数据准备失败: {e}")
            return None
    
    def replace_weather_data(self, output_file):
        """
        替换天气数据
        
        Args:
            output_file: 输出文件路径
        """
        try:
            # 准备天气映射
            weather_mapping = self.prepare_weather_data()
            if weather_mapping is None:
                return False
            
            # 处理用电量数据
            elec_data = self.electricity_df.copy()

            # 保存原始时间列
            original_time = elec_data['时间'].copy()

            # 创建临时时间列用于日期提取（不修改原始时间列）
            temp_time = pd.to_datetime(elec_data['时间'])
            elec_data['日期_str'] = temp_time.dt.strftime('%Y-%m-%d')
            
            # 创建日期-地区的组合键
            elec_data['date_region_key'] = elec_data['日期_str'] + '_' + elec_data['地区']
            
            print(f"\n用电量数据处理:")
            print(f"  时间范围: {temp_time.min().date()} 至 {temp_time.max().date()}")
            print(f"  包含地区: {list(elec_data['地区'].unique())}")
            
            # 替换天气数据
            print(f"\n正在替换天气数据...")
            
            # 备份原始天气数据
            if '天气' in elec_data.columns:
                elec_data['原始天气'] = elec_data['天气']
                print(f"已备份原始天气数据到'原始天气'列")
            
            # 执行天气数据替换
            elec_data['天气'] = elec_data['date_region_key'].map(weather_mapping)
            
            # 统计替换结果
            successful_replacements = elec_data['天气'].notna().sum()
            failed_replacements = elec_data['天气'].isna().sum()
            
            print(f"\n替换结果统计:")
            print(f"  成功替换: {successful_replacements} 条记录 ({successful_replacements/len(elec_data)*100:.1f}%)")
            print(f"  替换失败: {failed_replacements} 条记录 ({failed_replacements/len(elec_data)*100:.1f}%)")
            
            # 处理替换失败的记录
            if failed_replacements > 0:
                print(f"\n替换失败的记录分析:")
                failed_data = elec_data[elec_data['天气'].isna()]
                
                # 按地区统计失败情况
                failed_by_region = failed_data.groupby('地区').size()
                print(f"按地区统计失败数:")
                for region, count in failed_by_region.items():
                    print(f"  {region}: {count} 条")
                
                # 按日期统计失败情况
                failed_by_date = failed_data.groupby('日期_str').size()
                print(f"失败日期数: {len(failed_by_date)}")
                if len(failed_by_date) <= 10:
                    print(f"失败日期: {list(failed_by_date.index)}")
                
                # 对失败的记录，如果有原始天气数据，则保留
                if '原始天气' in elec_data.columns:
                    elec_data.loc[elec_data['天气'].isna(), '天气'] = elec_data.loc[elec_data['天气'].isna(), '原始天气']
                    print(f"已将失败记录的天气恢复为原始数据")
            
            # 恢复原始时间格式
            elec_data['时间'] = original_time

            # 删除临时列
            elec_data = elec_data.drop(columns=['日期_str', 'date_region_key'])
            if '原始天气' in elec_data.columns:
                elec_data = elec_data.drop(columns=['原始天气'])
            
            # 保存结果
            elec_data.to_excel(output_file, index=False)
            print(f"\n结果已保存到: {output_file}")
            
            # 显示替换后的天气分布
            print(f"\n替换后天气状况分布:")
            weather_distribution = elec_data['天气'].value_counts()
            for weather, count in weather_distribution.items():
                print(f"  {weather}: {count} 条记录 ({count/len(elec_data)*100:.1f}%)")
            
            # 显示各地区天气替换情况
            print(f"\n各地区天气替换验证:")
            for region in elec_data['地区'].unique():
                region_data = elec_data[elec_data['地区'] == region]
                region_weather = region_data['天气'].value_counts()
                print(f"  {region}: {dict(region_weather)}")
            
            # 显示时间序列验证（前几天）
            print(f"\n时间序列验证（前5天）:")
            for date in sorted(temp_time.dt.date.unique())[:5]:
                date_data = elec_data[temp_time.dt.date == date]
                date_weather = date_data.groupby(['地区', '天气']).size().reset_index(name='count')
                print(f"  {date}:")
                for _, row in date_weather.iterrows():
                    print(f"    {row['地区']}: {row['天气']} ({row['count']}条)")
            
            return True
            
        except Exception as e:
            print(f"天气数据替换失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    print("天气数据替换工具")
    print("=" * 60)
    print("根据日期和地区将天气数据替换到用电量文件中")
    print("=" * 60)
    
    # 文件路径
    weather_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/6月份全部地方天气(1).xlsx"
    
    electricity_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/合并结果_6月用电量含完整天气信息(1)(1).xlsx"
    
    output_file = "/Users/<USER>/Desktop/合并结果_6月用电量含更新天气信息.xlsx"
    
    # 检查输入文件
    if not os.path.exists(weather_file):
        print(f"错误: 天气数据文件不存在 - {weather_file}")
        return
    
    if not os.path.exists(electricity_file):
        print(f"错误: 用电量数据文件不存在 - {electricity_file}")
        return
    
    # 创建替换器
    replacer = WeatherReplacer(weather_file, electricity_file)
    
    if replacer.weather_df is not None and replacer.electricity_df is not None:
        print(f"\n输入文件:")
        print(f"  天气数据: {os.path.basename(weather_file)}")
        print(f"  用电量数据: {os.path.basename(electricity_file)}")
        print(f"  输出文件: {os.path.basename(output_file)}")
        
        print(f"\n开始替换天气数据...")
        
        # 执行替换
        success = replacer.replace_weather_data(output_file)
        
        if success:
            print(f"\n✅ 天气数据替换完成！")
            print(f"📁 输出文件: {output_file}")
            print(f"\n📊 替换说明:")
            print(f"   • 根据日期和地区精确匹配天气数据")
            print(f"   • 保持原有数据格式不变")
            print(f"   • 替换失败的记录保留原始天气数据")
            print(f"   • 已验证替换结果的准确性")
        else:
            print(f"\n❌ 天气数据替换失败！")
    else:
        print(f"\n❌ 数据加载失败，无法进行替换")

if __name__ == "__main__":
    main()
