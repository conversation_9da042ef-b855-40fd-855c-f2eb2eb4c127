#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
售电公司用电量预测分析工具
基于天气和节假日工作日数据建立预测模型
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
from scipy import stats
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import LabelEncoder
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class PowerSalesPredictionAnalysis:
    def __init__(self):
        """
        初始化售电公司预测分析器
        """
        self.df1 = None  # 详细用电量数据
        self.df2 = None  # 日电量汇总数据
        self.daily_data = None  # 每日汇总数据
        self.prediction_model = None  # 预测模型
        self.feature_importance = None  # 特征重要性
        
    def load_and_preprocess_data(self):
        """
        加载和预处理数据
        """
        try:
            # 文件路径
            file1 = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/合并结果_6月用电量含更新天气信息包含星期(1).xlsx'
            
            print("正在加载售电数据...")
            
            # 加载主数据文件
            if os.path.exists(file1):
                self.df1 = pd.read_excel(file1)
                print(f"数据加载成功: {len(self.df1)} 条记录")
            else:
                print("错误: 数据文件不存在")
                return False
            
            # 数据预处理
            self.df1['时间'] = pd.to_datetime(self.df1['时间'])
            self.df1['日期'] = self.df1['时间'].dt.date
            self.df1['月份'] = self.df1['时间'].dt.month
            self.df1['日'] = self.df1['时间'].dt.day
            self.df1['星期'] = self.df1['时间'].dt.dayofweek  # 0=周一, 6=周日
            
            # 创建每日汇总数据
            self.daily_data = self.df1.groupby(['日期', '地区']).agg({
                '总电量(kWh)': 'sum',
                '尖电量(kWh)': 'sum',
                '峰电量(kWh)': 'sum',
                '平电量(kWh)': 'sum',
                '谷电量(kWh)': 'sum',
                '天气': 'first',
                '日期类型': 'first',
                '月份': 'first',
                '日': 'first',
                '星期': 'first'
            }).reset_index()
            
            # 添加特征工程
            self.add_features()
            
            print(f"每日数据汇总完成: {len(self.daily_data)} 条记录")
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def add_features(self):
        """
        添加特征工程
        """
        # 天气编码
        weather_mapping = {
            '晴': 4, '晴天': 4,
            '多云': 3,
            '阴': 2, '阴天': 2,
            '雨': 1, '小雨': 1,
            '云': 2
        }
        self.daily_data['天气编码'] = self.daily_data['天气'].map(weather_mapping).fillna(2)
        
        # 日期类型编码
        def encode_date_type(date_type):
            if '端午节' in str(date_type):
                return 0  # 节假日
            elif date_type in ['星期六', '星期日']:
                return 1  # 周末
            else:
                return 2  # 工作日
        
        self.daily_data['日类型编码'] = self.daily_data['日期类型'].apply(encode_date_type)
        
        # 地区编码
        le_region = LabelEncoder()
        self.daily_data['地区编码'] = le_region.fit_transform(self.daily_data['地区'])
        
        # 时间特征
        self.daily_data['是否月初'] = (self.daily_data['日'] <= 10).astype(int)
        self.daily_data['是否月中'] = ((self.daily_data['日'] > 10) & (self.daily_data['日'] <= 20)).astype(int)
        self.daily_data['是否月末'] = (self.daily_data['日'] > 20).astype(int)
        
        # 滞后特征（前一天用电量）
        self.daily_data = self.daily_data.sort_values(['地区', '日期'])
        self.daily_data['前一天用电量'] = self.daily_data.groupby('地区')['总电量(kWh)'].shift(1)
        
        # 移动平均特征
        self.daily_data['3日均值'] = self.daily_data.groupby('地区')['总电量(kWh)'].rolling(3, min_periods=1).mean().reset_index(0, drop=True)
        self.daily_data['7日均值'] = self.daily_data.groupby('地区')['总电量(kWh)'].rolling(7, min_periods=1).mean().reset_index(0, drop=True)
    
    def analyze_patterns_for_sales(self):
        """
        分析售电公司关心的用电规律
        """
        print(f"\n" + "="*60)
        print(f"售电公司用电规律分析")
        print(f"="*60)
        
        # 1. 总体用电量趋势
        daily_total = self.daily_data.groupby('日期')['总电量(kWh)'].sum().reset_index()
        daily_total['日期'] = pd.to_datetime(daily_total['日期'])
        
        print(f"\n1. 6月份总体用电趋势:")
        print(f"   月总用电量: {daily_total['总电量(kWh)'].sum():,.0f} kWh")
        print(f"   日均用电量: {daily_total['总电量(kWh)'].mean():,.0f} kWh")
        print(f"   最高日用电量: {daily_total['总电量(kWh)'].max():,.0f} kWh")
        print(f"   最低日用电量: {daily_total['总电量(kWh)'].min():,.0f} kWh")
        print(f"   用电量波动系数: {daily_total['总电量(kWh)'].std()/daily_total['总电量(kWh)'].mean():.3f}")
        
        # 2. 不同时段用电结构分析
        print(f"\n2. 用电结构分析:")
        total_power = self.daily_data[['尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']].sum()
        total_sum = total_power.sum()
        
        for period, amount in total_power.items():
            percentage = amount / total_sum * 100
            print(f"   {period}: {amount:,.0f} kWh ({percentage:.1f}%)")
        
        # 3. 天气对用电量的影响（售电角度）
        weather_impact = self.daily_data.groupby('天气').agg({
            '总电量(kWh)': ['count', 'sum', 'mean'],
            '尖电量(kWh)': 'sum',
            '峰电量(kWh)': 'sum'
        }).round(0)
        
        print(f"\n3. 天气对售电量的影响:")
        for weather in weather_impact.index:
            count = weather_impact.loc[weather, ('总电量(kWh)', 'count')]
            total = weather_impact.loc[weather, ('总电量(kWh)', 'sum')]
            avg = weather_impact.loc[weather, ('总电量(kWh)', 'mean')]
            peak = weather_impact.loc[weather, ('尖电量(kWh)', 'sum')] + weather_impact.loc[weather, ('峰电量(kWh)', 'sum')]
            print(f"   {weather}: {count}天, 总售电量 {total:,.0f} kWh, 日均 {avg:,.0f} kWh, 高峰电量 {peak:,.0f} kWh")
        
        # 4. 工作日vs周末vs节假日售电分析
        day_type_analysis = self.daily_data.groupby('日类型编码').agg({
            '总电量(kWh)': ['count', 'sum', 'mean'],
            '尖电量(kWh)': 'sum',
            '峰电量(kWh)': 'sum'
        }).round(0)
        
        day_type_names = {0: '节假日', 1: '周末', 2: '工作日'}
        
        print(f"\n4. 不同日期类型售电分析:")
        for code, name in day_type_names.items():
            if code in day_type_analysis.index:
                count = day_type_analysis.loc[code, ('总电量(kWh)', 'count')]
                total = day_type_analysis.loc[code, ('总电量(kWh)', 'sum')]
                avg = day_type_analysis.loc[code, ('总电量(kWh)', 'mean')]
                peak = day_type_analysis.loc[code, ('尖电量(kWh)', 'sum')] + day_type_analysis.loc[code, ('峰电量(kWh)', 'sum')]
                print(f"   {name}: {count}天, 总售电量 {total:,.0f} kWh, 日均 {avg:,.0f} kWh, 高峰电量 {peak:,.0f} kWh")
        
        # 5. 地区售电贡献分析
        region_contribution = self.daily_data.groupby('地区').agg({
            '总电量(kWh)': 'sum',
            '尖电量(kWh)': 'sum',
            '峰电量(kWh)': 'sum'
        }).round(0)
        
        region_contribution['贡献率'] = (region_contribution['总电量(kWh)'] / region_contribution['总电量(kWh)'].sum() * 100).round(1)
        region_contribution = region_contribution.sort_values('总电量(kWh)', ascending=False)
        
        print(f"\n5. 各地区售电贡献分析:")
        for region, row in region_contribution.iterrows():
            print(f"   {region}: {row['总电量(kWh)']:,.0f} kWh ({row['贡献率']}%), 高峰 {row['尖电量(kWh)']+row['峰电量(kWh)']:,.0f} kWh")
        
        return daily_total, weather_impact, day_type_analysis, region_contribution
    
    def build_prediction_model(self):
        """
        构建用电量预测模型 - 改进版
        """
        print(f"\n" + "="*60)
        print(f"构建用电量预测模型")
        print(f"="*60)

        # 准备特征和目标变量
        feature_columns = [
            '天气编码', '日类型编码', '地区编码', '日', '星期',
            '是否月初', '是否月中', '是否月末'
        ]

        # 只使用基础特征，避免数据泄露
        model_data = self.daily_data.dropna(subset=feature_columns + ['总电量(kWh)'])

        X = model_data[feature_columns]
        y = model_data['总电量(kWh)']

        print(f"模型训练数据: {len(model_data)} 条记录")

        # 按地区分别建模，提高预测准确性
        region_models = {}
        region_performance = {}

        for region in model_data['地区'].unique():
            region_data = model_data[model_data['地区'] == region]

            if len(region_data) < 10:  # 数据太少跳过
                continue

            # 准备该地区的特征（去除地区编码）
            region_features = [col for col in feature_columns if col != '地区编码']
            X_region = region_data[region_features]
            y_region = region_data['总电量(kWh)']

            # 简单的时间序列分割
            split_point = max(1, int(len(region_data) * 0.8))
            X_train = X_region.iloc[:split_point]
            X_test = X_region.iloc[split_point:]
            y_train = y_region.iloc[:split_point]
            y_test = y_region.iloc[split_point:]

            if len(X_test) == 0:  # 测试集为空，使用全部数据训练
                X_train, y_train = X_region, y_region
                X_test, y_test = X_region.iloc[-3:], y_region.iloc[-3:]  # 使用最后3个点作为测试

            # 训练模型
            model = RandomForestRegressor(
                n_estimators=50,
                max_depth=5,
                random_state=42,
                n_jobs=-1
            )

            model.fit(X_train, y_train)
            region_models[region] = model

            # 评估
            if len(X_test) > 0:
                y_pred = model.predict(X_test)
                mae = mean_absolute_error(y_test, y_pred)
                r2 = r2_score(y_test, y_pred) if len(y_test) > 1 else 0

                region_performance[region] = {
                    'mae': mae,
                    'r2': r2,
                    'mean_actual': y_test.mean()
                }

        self.prediction_model = region_models

        # 显示各地区模型性能
        print(f"\n各地区模型性能:")
        total_mae = 0
        total_regions = 0

        for region, perf in region_performance.items():
            mae = perf['mae']
            r2 = perf['r2']
            mean_actual = perf['mean_actual']
            relative_error = mae / mean_actual * 100 if mean_actual > 0 else 0

            print(f"   {region}: MAE={mae:,.0f} kWh, R²={r2:.3f}, 相对误差={relative_error:.1f}%")
            total_mae += mae
            total_regions += 1

        avg_mae = total_mae / total_regions if total_regions > 0 else 0

        print(f"\n整体模型性能:")
        print(f"   平均MAE: {avg_mae:,.0f} kWh")
        print(f"   模型数量: {len(region_models)} 个地区模型")

        return avg_mae, 0, 0  # 返回平均MAE
    
    def predict_next_month(self, next_month_weather_forecast=None):
        """
        预测下个月用电量 - 改进版
        """
        print(f"\n" + "="*60)
        print(f"7月份用电量预测")
        print(f"="*60)

        if not isinstance(self.prediction_model, dict) or len(self.prediction_model) == 0:
            print("错误: 请先构建预测模型")
            return None

        # 生成7月份的日期
        july_dates = pd.date_range('2025-07-01', '2025-07-31', freq='D')

        # 默认天气预测（基于6月份天气分布）
        if next_month_weather_forecast is None:
            weather_dist = self.daily_data['天气编码'].value_counts(normalize=True)
            np.random.seed(42)  # 固定随机种子
            next_month_weather_forecast = np.random.choice(
                weather_dist.index,
                size=len(july_dates),
                p=weather_dist.values
            )

        predictions = []

        for region in self.prediction_model.keys():
            model = self.prediction_model[region]

            # 获取该地区6月份的平均用电量作为基准
            region_data = self.daily_data[self.daily_data['地区'] == region]
            avg_power = region_data['总电量(kWh)'].mean()

            for i, date in enumerate(july_dates):
                # 构建预测特征
                day_type = 2  # 默认工作日
                if date.weekday() >= 5:  # 周末
                    day_type = 1

                # 7月份节假日判断（可以根据实际情况调整）
                if date.day == 1:  # 假设7月1日是节假日
                    day_type = 0

                features = {
                    '天气编码': next_month_weather_forecast[i],
                    '日类型编码': day_type,
                    '日': date.day,
                    '星期': date.weekday(),
                    '是否月初': 1 if date.day <= 10 else 0,
                    '是否月中': 1 if 10 < date.day <= 20 else 0,
                    '是否月末': 1 if date.day > 20 else 0
                }

                # 预测
                try:
                    X_pred = pd.DataFrame([features])
                    predicted_power = model.predict(X_pred)[0]

                    # 确保预测值合理（不超过历史最大值的1.5倍，不低于最小值的0.5倍）
                    max_power = region_data['总电量(kWh)'].max() * 1.5
                    min_power = region_data['总电量(kWh)'].min() * 0.5
                    predicted_power = max(min_power, min(max_power, predicted_power))

                except Exception as e:
                    # 如果预测失败，使用历史平均值
                    predicted_power = avg_power

                predictions.append({
                    '日期': date,
                    '地区': region,
                    '预测用电量': predicted_power
                })

        # 转换为DataFrame
        predictions_df = pd.DataFrame(predictions)

        # 汇总预测结果
        daily_predictions = predictions_df.groupby('日期')['预测用电量'].sum().reset_index()
        monthly_total = daily_predictions['预测用电量'].sum()
        daily_average = daily_predictions['预测用电量'].mean()

        print(f"\n7月份用电量预测结果:")
        print(f"   预测月总用电量: {monthly_total:,.0f} kWh")
        print(f"   预测日均用电量: {daily_average:,.0f} kWh")
        print(f"   与6月份对比: {(monthly_total/self.daily_data['总电量(kWh)'].sum()-1)*100:+.1f}%")

        # 按地区预测
        region_predictions = predictions_df.groupby('地区')['预测用电量'].sum().sort_values(ascending=False)
        print(f"\n各地区7月份预测用电量:")
        for region, predicted_power in region_predictions.items():
            june_power = self.daily_data[self.daily_data['地区'] == region]['总电量(kWh)'].sum()
            change = (predicted_power / june_power - 1) * 100
            print(f"   {region}: {predicted_power:,.0f} kWh ({change:+.1f}%)")

        return predictions_df, daily_predictions
    
    def generate_sales_insights(self):
        """
        生成售电公司业务洞察
        """
        print(f"\n" + "="*60)
        print(f"售电公司业务洞察与建议")
        print(f"="*60)
        
        # 1. 客户分层分析
        region_stats = self.daily_data.groupby('地区').agg({
            '总电量(kWh)': ['sum', 'mean', 'std'],
            '尖电量(kWh)': 'sum',
            '峰电量(kWh)': 'sum'
        }).round(0)
        
        print(f"\n1. 客户分层建议:")
        total_power = region_stats[('总电量(kWh)', 'sum')].sum()
        
        for region in region_stats.index:
            power_sum = region_stats.loc[region, ('总电量(kWh)', 'sum')]
            power_std = region_stats.loc[region, ('总电量(kWh)', 'std')]
            contribution = power_sum / total_power * 100
            
            if contribution > 50:
                tier = "核心大客户"
            elif contribution > 10:
                tier = "重要客户"
            else:
                tier = "一般客户"
            
            stability = "稳定" if power_std < power_sum * 0.3 else "波动较大"
            
            print(f"   {region}: {tier} (贡献率{contribution:.1f}%, 用电{stability})")
        
        # 2. 定价策略建议
        print(f"\n2. 定价策略建议:")
        
        # 分析尖峰平谷比例
        time_periods = ['尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']
        period_totals = self.daily_data[time_periods].sum()
        total_consumption = period_totals.sum()
        
        for period in time_periods:
            percentage = period_totals[period] / total_consumption * 100
            period_name = period.replace('电量(kWh)', '')
            print(f"   {period_name}时段占比: {percentage:.1f}%")
        
        # 3. 风险管理建议
        print(f"\n3. 风险管理建议:")
        
        # 天气风险
        weather_volatility = self.daily_data.groupby('天气')['总电量(kWh)'].std()
        high_risk_weather = weather_volatility.nlargest(3)
        
        print(f"   高风险天气类型:")
        for weather, volatility in high_risk_weather.items():
            print(f"     {weather}: 波动性 {volatility:,.0f} kWh")
        
        # 4. 营销策略建议
        print(f"\n4. 营销策略建议:")
        
        # 找出用电量增长潜力
        region_growth_potential = self.daily_data.groupby('地区').agg({
            '总电量(kWh)': ['mean', 'max']
        })
        
        for region in region_growth_potential.index:
            avg_power = region_growth_potential.loc[region, ('总电量(kWh)', 'mean')]
            max_power = region_growth_potential.loc[region, ('总电量(kWh)', 'max')]
            potential = (max_power - avg_power) / avg_power * 100
            
            if potential > 50:
                strategy = "重点开发，提供优惠套餐"
            elif potential > 20:
                strategy = "适度营销，关注用电习惯"
            else:
                strategy = "维护为主，提供增值服务"
            
            print(f"   {region}: 增长潜力{potential:.1f}% - {strategy}")
    
    def run_complete_analysis(self):
        """
        运行完整的售电分析
        """
        print("售电公司用电量预测与分析系统")
        print("="*80)
        
        # 1. 加载数据
        if not self.load_and_preprocess_data():
            return False
        
        # 2. 分析用电规律
        daily_total, weather_impact, day_type_analysis, region_contribution = self.analyze_patterns_for_sales()
        
        # 3. 构建预测模型
        mae, rmse, r2 = self.build_prediction_model()
        
        # 4. 预测下个月用电量
        predictions_df, daily_predictions = self.predict_next_month()
        
        # 5. 生成业务洞察
        self.generate_sales_insights()
        
        print(f"\n✅ 售电分析完成！")
        print(f"📊 模型准确率: {r2:.1%}")
        print(f"📈 7月预测总用电量: {daily_predictions['预测用电量'].sum():,.0f} kWh")
        
        return True

def main():
    """主函数"""
    analyzer = PowerSalesPredictionAnalysis()
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
