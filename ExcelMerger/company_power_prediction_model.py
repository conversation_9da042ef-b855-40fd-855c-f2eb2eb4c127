#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于户号公司用电量的日预测数学模型
结合天气信息进行精准预测
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
from scipy import stats
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score, mean_absolute_percentage_error
from sklearn.preprocessing import StandardScaler, LabelEncoder
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class CompanyPowerPredictionModel:
    def __init__(self):
        """
        初始化公司用电量预测模型
        """
        self.data = None
        self.models = {}
        self.scalers = {}
        self.label_encoders = {}
        self.feature_importance = {}
        self.prediction_results = {}
        
        # 定义模型集合
        self.model_configs = {
            '线性回归': LinearRegression(),
            '岭回归': Ridge(alpha=1.0),
            'Lasso回归': Lasso(alpha=1.0),
            '弹性网络': ElasticNet(alpha=1.0, l1_ratio=0.5),
            '随机森林': RandomForestRegressor(n_estimators=100, random_state=42),
            '梯度提升': GradientBoostingRegressor(n_estimators=100, random_state=42),
            '极端随机树': ExtraTreesRegressor(n_estimators=100, random_state=42),
            '支持向量机': SVR(kernel='rbf', C=1.0),
            '神经网络': MLPRegressor(hidden_layer_sizes=(100, 50), max_iter=500, random_state=42)
        }
    
    def load_data(self, file_path):
        """
        加载包含公司名称的用电量数据
        """
        print("正在加载用电量数据...")
        
        try:
            self.data = pd.read_excel(file_path, sheet_name='完整数据')
            print(f"✅ 成功加载数据: {len(self.data)} 条记录")
            
            # 数据预处理
            self.data['时间'] = pd.to_datetime(self.data['时间'])
            self.data['年'] = self.data['时间'].dt.year
            self.data['月'] = self.data['时间'].dt.month
            self.data['日'] = self.data['时间'].dt.day
            self.data['星期'] = self.data['时间'].dt.dayofweek
            self.data['是否周末'] = (self.data['星期'] >= 5).astype(int)
            
            # 显示数据概况
            print(f"   时间范围: {self.data['时间'].min()} 到 {self.data['时间'].max()}")
            print(f"   公司数量: {self.data['公司名称'].nunique()}")
            print(f"   户号数量: {self.data['户号'].nunique()}")
            print(f"   地区数量: {self.data['地区'].nunique()}")
            
            # 检查缺失值
            missing_info = self.data.isnull().sum()
            print(f"\n📊 数据完整性检查:")
            for col, missing_count in missing_info.items():
                if missing_count > 0:
                    print(f"   {col}: 缺失 {missing_count} 个 ({missing_count/len(self.data)*100:.1f}%)")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def prepare_features(self):
        """
        准备特征工程
        """
        print("\n正在进行特征工程...")
        
        # 基础特征
        feature_columns = [
            '年', '月', '日', '星期', '是否周末',
            '最高温度(°C)', '最低温度(°C)', 'AQI', '降水量(mm)', '湿度(%)', '气压(hPa)'
        ]
        
        # 处理分类特征
        categorical_features = ['地区', '公司名称', '白天天气', '晚上天气', '风向', '合同状态', '居间/销售']
        
        for col in categorical_features:
            if col in self.data.columns:
                le = LabelEncoder()
                self.data[f'{col}_编码'] = le.fit_transform(self.data[col].fillna('未知'))
                self.label_encoders[col] = le
                feature_columns.append(f'{col}_编码')
        
        # 创建温度相关特征
        self.data['温差'] = self.data['最高温度(°C)'] - self.data['最低温度(°C)']
        self.data['平均温度'] = (self.data['最高温度(°C)'] + self.data['最低温度(°C)']) / 2
        self.data['高温指标'] = (self.data['最高温度(°C)'] >= 30).astype(int)
        self.data['低温指标'] = (self.data['最低温度(°C)'] <= 10).astype(int)
        
        feature_columns.extend(['温差', '平均温度', '高温指标', '低温指标'])
        
        # 创建时间特征
        self.data['月份sin'] = np.sin(2 * np.pi * self.data['月'] / 12)
        self.data['月份cos'] = np.cos(2 * np.pi * self.data['月'] / 12)
        self.data['日期sin'] = np.sin(2 * np.pi * self.data['日'] / 31)
        self.data['日期cos'] = np.cos(2 * np.pi * self.data['日'] / 31)
        
        feature_columns.extend(['月份sin', '月份cos', '日期sin', '日期cos'])
        
        # 创建滞后特征（前一天的用电量）
        self.data = self.data.sort_values(['户号', '时间'])
        self.data['前一天用电量'] = self.data.groupby('户号')['总电量(kWh)'].shift(1)
        self.data['前三天平均用电量'] = self.data.groupby('户号')['总电量(kWh)'].rolling(window=3).mean().reset_index(0, drop=True)
        
        feature_columns.extend(['前一天用电量', '前三天平均用电量'])
        
        # 移除缺失值
        self.data = self.data.dropna(subset=feature_columns + ['总电量(kWh)'])
        
        print(f"✅ 特征工程完成:")
        print(f"   特征数量: {len(feature_columns)}")
        print(f"   有效记录: {len(self.data)}")
        print(f"   特征列表: {feature_columns}")
        
        return feature_columns
    
    def train_models(self, feature_columns, test_size=0.2):
        """
        训练多种预测模型
        """
        print(f"\n正在训练预测模型...")
        
        # 准备训练数据
        X = self.data[feature_columns]
        y = self.data['总电量(kWh)']
        
        # 数据标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        self.scalers['features'] = scaler
        
        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=test_size, random_state=42, stratify=self.data['公司名称_编码']
        )
        
        print(f"   训练集: {len(X_train)} 条记录")
        print(f"   测试集: {len(X_test)} 条记录")
        
        # 训练各种模型
        model_performance = {}
        
        for model_name, model in self.model_configs.items():
            print(f"\n正在训练 {model_name}...")
            
            try:
                # 训练模型
                model.fit(X_train, y_train)
                
                # 预测
                y_pred_train = model.predict(X_train)
                y_pred_test = model.predict(X_test)
                
                # 评估指标
                train_mae = mean_absolute_error(y_train, y_pred_train)
                test_mae = mean_absolute_error(y_test, y_pred_test)
                train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
                test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
                train_r2 = r2_score(y_train, y_pred_train)
                test_r2 = r2_score(y_test, y_pred_test)
                test_mape = mean_absolute_percentage_error(y_test, y_pred_test) * 100
                
                # 交叉验证
                cv_scores = cross_val_score(model, X_scaled, y, cv=5, scoring='r2')
                
                performance = {
                    'train_mae': train_mae,
                    'test_mae': test_mae,
                    'train_rmse': train_rmse,
                    'test_rmse': test_rmse,
                    'train_r2': train_r2,
                    'test_r2': test_r2,
                    'test_mape': test_mape,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std()
                }
                
                model_performance[model_name] = performance
                self.models[model_name] = model
                
                print(f"   测试集 R²: {test_r2:.3f}")
                print(f"   测试集 MAE: {test_mae:.2f}")
                print(f"   测试集 MAPE: {test_mape:.2f}%")
                print(f"   交叉验证 R²: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
                
                # 特征重要性（如果模型支持）
                if hasattr(model, 'feature_importances_'):
                    importance = pd.DataFrame({
                        'feature': feature_columns,
                        'importance': model.feature_importances_
                    }).sort_values('importance', ascending=False)
                    self.feature_importance[model_name] = importance
                
            except Exception as e:
                print(f"   ❌ {model_name} 训练失败: {e}")
                continue
        
        # 选择最佳模型
        if model_performance:
            best_model_name = max(model_performance.keys(), key=lambda x: model_performance[x]['test_r2'])
            print(f"\n✅ 最佳模型: {best_model_name}")
            print(f"   测试集 R²: {model_performance[best_model_name]['test_r2']:.3f}")
            print(f"   测试集 MAE: {model_performance[best_model_name]['test_mae']:.2f}")
            print(f"   测试集 MAPE: {model_performance[best_model_name]['test_mape']:.2f}%")
            
            self.prediction_results = {
                'performance': model_performance,
                'best_model': best_model_name,
                'feature_columns': feature_columns,
                'X_test': X_test,
                'y_test': y_test,
                'y_pred_test': self.models[best_model_name].predict(X_test)
            }
            
            return True
        else:
            print("❌ 所有模型训练失败")
            return False
    
    def predict_company_consumption(self, company_name, prediction_days=7):
        """
        预测指定公司未来几天的用电量
        """
        print(f"\n正在预测 {company_name} 未来 {prediction_days} 天的用电量...")
        
        if not self.models or not self.prediction_results:
            print("❌ 请先训练模型")
            return None
        
        # 获取公司历史数据
        company_data = self.data[self.data['公司名称'] == company_name].copy()
        
        if company_data.empty:
            print(f"❌ 未找到公司 {company_name} 的数据")
            return None
        
        print(f"   公司历史记录: {len(company_data)} 条")
        
        # 获取最新数据作为基础
        latest_data = company_data.iloc[-1].copy()
        
        # 生成未来日期的预测
        predictions = []
        feature_columns = self.prediction_results['feature_columns']
        best_model = self.models[self.prediction_results['best_model']]
        scaler = self.scalers['features']
        
        for i in range(1, prediction_days + 1):
            future_date = latest_data['时间'] + timedelta(days=i)
            
            # 创建预测特征
            pred_features = latest_data.copy()
            pred_features['时间'] = future_date
            pred_features['年'] = future_date.year
            pred_features['月'] = future_date.month
            pred_features['日'] = future_date.day
            pred_features['星期'] = future_date.weekday()
            pred_features['是否周末'] = 1 if future_date.weekday() >= 5 else 0
            
            # 时间特征
            pred_features['月份sin'] = np.sin(2 * np.pi * future_date.month / 12)
            pred_features['月份cos'] = np.cos(2 * np.pi * future_date.month / 12)
            pred_features['日期sin'] = np.sin(2 * np.pi * future_date.day / 31)
            pred_features['日期cos'] = np.cos(2 * np.pi * future_date.day / 31)
            
            # 天气特征（使用历史平均值或季节性模式）
            month_avg = company_data[company_data['月'] == future_date.month]
            if not month_avg.empty:
                pred_features['最高温度(°C)'] = month_avg['最高温度(°C)'].mean()
                pred_features['最低温度(°C)'] = month_avg['最低温度(°C)'].mean()
                pred_features['AQI'] = month_avg['AQI'].mean()
                pred_features['降水量(mm)'] = month_avg['降水量(mm)'].mean()
                pred_features['湿度(%)'] = month_avg['湿度(%)'].mean()
                pred_features['气压(hPa)'] = month_avg['气压(hPa)'].mean()
            
            # 计算衍生特征
            pred_features['温差'] = pred_features['最高温度(°C)'] - pred_features['最低温度(°C)']
            pred_features['平均温度'] = (pred_features['最高温度(°C)'] + pred_features['最低温度(°C)']) / 2
            pred_features['高温指标'] = 1 if pred_features['最高温度(°C)'] >= 30 else 0
            pred_features['低温指标'] = 1 if pred_features['最低温度(°C)'] <= 10 else 0
            
            # 滞后特征
            if i == 1:
                pred_features['前一天用电量'] = latest_data['总电量(kWh)']
                pred_features['前三天平均用电量'] = company_data.tail(3)['总电量(kWh)'].mean()
            else:
                # 使用之前的预测值
                pred_features['前一天用电量'] = predictions[-1]['预测用电量'] if predictions else latest_data['总电量(kWh)']
                if len(predictions) >= 2:
                    recent_predictions = [p['预测用电量'] for p in predictions[-2:]]
                    recent_predictions.append(latest_data['总电量(kWh)'])
                    pred_features['前三天平均用电量'] = np.mean(recent_predictions)
                else:
                    pred_features['前三天平均用电量'] = company_data.tail(3)['总电量(kWh)'].mean()
            
            # 准备预测特征向量
            X_pred = np.array([pred_features[col] for col in feature_columns]).reshape(1, -1)
            X_pred_scaled = scaler.transform(X_pred)
            
            # 预测
            predicted_consumption = best_model.predict(X_pred_scaled)[0]
            
            predictions.append({
                '日期': future_date.strftime('%Y-%m-%d'),
                '星期': ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][future_date.weekday()],
                '预测用电量': round(predicted_consumption, 2),
                '预测最高温度': round(pred_features['最高温度(°C)'], 1),
                '预测最低温度': round(pred_features['最低温度(°C)'], 1),
                '是否周末': '是' if pred_features['是否周末'] else '否'
            })
        
        print(f"✅ 预测完成")
        return predictions

    def create_visualizations(self):
        """
        创建模型性能可视化
        """
        print("\n正在生成可视化图表...")

        if not self.prediction_results:
            print("❌ 没有预测结果可视化")
            return False

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('用电量预测模型性能分析', fontsize=16, fontweight='bold')

        # 1. 模型性能对比
        performance = self.prediction_results['performance']
        model_names = list(performance.keys())
        test_r2_scores = [performance[name]['test_r2'] for name in model_names]
        test_mae_scores = [performance[name]['test_mae'] for name in model_names]

        axes[0, 0].bar(range(len(model_names)), test_r2_scores, color='skyblue', alpha=0.7)
        axes[0, 0].set_xlabel('模型')
        axes[0, 0].set_ylabel('R² 分数')
        axes[0, 0].set_title('模型 R² 性能对比')
        axes[0, 0].set_xticks(range(len(model_names)))
        axes[0, 0].set_xticklabels(model_names, rotation=45, ha='right')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 预测值vs实际值散点图
        y_test = self.prediction_results['y_test']
        y_pred = self.prediction_results['y_pred_test']

        axes[0, 1].scatter(y_test, y_pred, alpha=0.6, color='coral')
        axes[0, 1].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
        axes[0, 1].set_xlabel('实际用电量 (kWh)')
        axes[0, 1].set_ylabel('预测用电量 (kWh)')
        axes[0, 1].set_title('预测值 vs 实际值')
        axes[0, 1].grid(True, alpha=0.3)

        # 3. 残差分布
        residuals = y_test - y_pred
        axes[1, 0].hist(residuals, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[1, 0].set_xlabel('残差 (kWh)')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].set_title('残差分布')
        axes[1, 0].axvline(x=0, color='red', linestyle='--', alpha=0.8)
        axes[1, 0].grid(True, alpha=0.3)

        # 4. 特征重要性（如果有）
        best_model_name = self.prediction_results['best_model']
        if best_model_name in self.feature_importance:
            importance_df = self.feature_importance[best_model_name].head(10)
            axes[1, 1].barh(range(len(importance_df)), importance_df['importance'], color='gold', alpha=0.7)
            axes[1, 1].set_yticks(range(len(importance_df)))
            axes[1, 1].set_yticklabels(importance_df['feature'])
            axes[1, 1].set_xlabel('重要性')
            axes[1, 1].set_title(f'{best_model_name} - 特征重要性 (Top 10)')
            axes[1, 1].grid(True, alpha=0.3)
        else:
            axes[1, 1].text(0.5, 0.5, '该模型不支持\n特征重要性分析',
                           ha='center', va='center', transform=axes[1, 1].transAxes, fontsize=12)
            axes[1, 1].set_title('特征重要性')

        plt.tight_layout()

        # 保存图表
        chart_path = "/Users/<USER>/RiderProjects/Solution3/用电量预测模型性能分析.png"
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        print(f"✅ 可视化图表已保存: {chart_path}")

        plt.show()
        return True

    def save_prediction_results(self, company_predictions=None, output_file="用电量预测模型结果.xlsx"):
        """
        保存预测结果和模型性能
        """
        print(f"\n正在保存预测结果...")

        try:
            output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"

            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 1. 模型性能对比
                if self.prediction_results:
                    performance_df = pd.DataFrame(self.prediction_results['performance']).T
                    performance_df = performance_df.round(4)
                    performance_df.to_excel(writer, sheet_name='模型性能对比')
                    print(f"  ✅ 模型性能对比表")

                # 2. 特征重要性
                if self.feature_importance:
                    for model_name, importance_df in self.feature_importance.items():
                        sheet_name = f'{model_name}_特征重要性'[:31]  # Excel工作表名称限制
                        importance_df.to_excel(writer, sheet_name=sheet_name, index=False)
                        print(f"  ✅ {model_name} 特征重要性")

                # 3. 公司预测结果
                if company_predictions:
                    for company_name, predictions in company_predictions.items():
                        if predictions:
                            pred_df = pd.DataFrame(predictions)
                            sheet_name = f'{company_name[:20]}_预测'  # 限制长度
                            pred_df.to_excel(writer, sheet_name=sheet_name, index=False)
                            print(f"  ✅ {company_name} 预测结果")

                # 4. 数据统计摘要
                if self.data is not None:
                    summary_stats = self.data.groupby('公司名称').agg({
                        '总电量(kWh)': ['count', 'mean', 'std', 'min', 'max'],
                        '最高温度(°C)': 'mean',
                        '最低温度(°C)': 'mean',
                        'AQI': 'mean'
                    }).round(2)

                    summary_stats.columns = ['记录数', '平均用电量', '用电量标准差', '最小用电量', '最大用电量', '平均最高温度', '平均最低温度', '平均AQI']
                    summary_stats = summary_stats.reset_index()
                    summary_stats.to_excel(writer, sheet_name='公司用电统计', index=False)
                    print(f"  ✅ 公司用电统计")

                # 5. 模型配置信息
                config_info = {
                    '模型类型': list(self.model_configs.keys()),
                    '最佳模型': [self.prediction_results.get('best_model', '未确定')] * len(self.model_configs),
                    '特征数量': [len(self.prediction_results.get('feature_columns', []))] * len(self.model_configs),
                    '训练数据量': [len(self.data) if self.data is not None else 0] * len(self.model_configs)
                }
                config_df = pd.DataFrame(config_info)
                config_df.to_excel(writer, sheet_name='模型配置信息', index=False)
                print(f"  ✅ 模型配置信息")

            print(f"✅ 预测结果已保存到: {output_path}")
            return True

        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False

    def run_comprehensive_analysis(self, target_companies=None, prediction_days=7):
        """
        运行综合分析
        """
        print("="*80)
        print("公司用电量日预测数学模型 - 综合分析")
        print("="*80)

        # 1. 特征工程
        feature_columns = self.prepare_features()
        if not feature_columns:
            return False

        # 2. 训练模型
        if not self.train_models(feature_columns):
            return False

        # 3. 生成可视化
        self.create_visualizations()

        # 4. 公司预测
        company_predictions = {}
        if target_companies:
            for company in target_companies:
                predictions = self.predict_company_consumption(company, prediction_days)
                if predictions:
                    company_predictions[company] = predictions

                    print(f"\n📊 {company} 未来{prediction_days}天预测:")
                    for pred in predictions:
                        print(f"   {pred['日期']} ({pred['星期']}): {pred['预测用电量']:,.2f} kWh, "
                              f"温度 {pred['预测最低温度']}-{pred['预测最高温度']}°C")
        else:
            # 自动选择用电量最大的前5个公司进行预测
            top_companies = self.data.groupby('公司名称')['总电量(kWh)'].sum().nlargest(5).index.tolist()
            print(f"\n自动选择用电量最大的前5个公司进行预测: {top_companies}")

            for company in top_companies:
                predictions = self.predict_company_consumption(company, prediction_days)
                if predictions:
                    company_predictions[company] = predictions

        # 5. 保存结果
        self.save_prediction_results(company_predictions)

        # 6. 显示总结
        print(f"\n🎉 综合分析完成！")
        print(f"📊 最佳模型: {self.prediction_results['best_model']}")
        print(f"🎯 预测精度: R² = {self.prediction_results['performance'][self.prediction_results['best_model']]['test_r2']:.3f}")
        print(f"🏢 预测公司数: {len(company_predictions)}")
        print(f"📁 结果文件: 用电量预测模型结果.xlsx")
        print(f"📈 可视化图表: 用电量预测模型性能分析.png")

        return True

def main():
    """
    主函数
    """
    # 数据文件路径
    data_file = "/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx"

    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return

    # 创建预测模型
    model = CompanyPowerPredictionModel()

    # 加载数据
    if not model.load_data(data_file):
        return

    # 指定要预测的公司（可以修改这个列表）
    target_companies = [
        '浙江博世华环保科技有限公司',
        '桐昆集团股份有限公司',
        '万城万充（杭州）新能源投资有限公司',
        '永康市兆加装饰工程有限公司'
    ]

    # 运行综合分析
    model.run_comprehensive_analysis(target_companies=target_companies, prediction_days=7)

if __name__ == "__main__":
    main()
