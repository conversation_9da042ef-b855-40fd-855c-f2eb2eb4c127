#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中栋电厂出力曲线处理工具 - 修正版
从现货结算表提取电能数据，转换为15分钟间隔，计算功率
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

class PowerCurveProcessor:
    def __init__(self):
        """
        初始化出力曲线处理器
        """
        self.settlement_data = None
        self.processed_data = None
        
        # 15分钟时间点列表 (96个点)
        self.time_points = []
        for hour in range(24):
            self.time_points.append(f"{hour:02d}:15")
            self.time_points.append(f"{hour:02d}:30")
            self.time_points.append(f"{hour:02d}:45")
            if hour < 23:
                self.time_points.append(f"{hour+1:02d}:00")
        self.time_points.append("24:00")  # 最后一个点
    
    def load_settlement_data(self, file_path):
        """
        加载现货结算表数据
        """
        print("正在加载中栋电厂现货结算表...")
        
        try:
            # 跳过前3行，从第4行开始读取
            df = pd.read_excel(file_path, skiprows=3)
            
            # 重新命名列
            df.columns = ['项目名称', '结算单元', '空列1', '时间', '计量电量', '结算电量', 
                         '出清电价', '结算电价', '空列2', '结算电费', '空列3']
            
            # 过滤掉表头行和空行
            df = df[df['时间'].notna()]
            df = df[df['时间'] != '时间']
            
            # 转换时间格式
            df['时间'] = pd.to_datetime(df['时间'])
            
            # 转换数值列
            numeric_cols = ['计量电量', '结算电量', '出清电价', '结算电价', '结算电费']
            for col in numeric_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 过滤掉无效数据
            df = df.dropna(subset=['时间', '结算电量'])
            
            self.settlement_data = df
            
            print(f"✅ 成功加载现货结算数据: {len(df)} 条记录")
            print(f"   时间范围: {df['时间'].min()} 到 {df['时间'].max()}")
            print(f"   结算电量范围: {df['结算电量'].min():.2f} - {df['结算电量'].max():.2f} MWh")
            
            # 显示前几条数据用于验证
            print(f"\n📊 前5条数据:")
            for i, row in df.head(5).iterrows():
                print(f"   {row['时间']}: {row['结算电量']:.4f} MWh")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载现货结算数据失败: {e}")
            return False
    
    def convert_to_15min_power(self):
        """
        将半小时电量数据转换为15分钟功率数据
        逻辑：半小时电量 / 0.5小时 = 平均功率，每15分钟功率相同
        """
        print("\n正在转换为15分钟功率数据...")
        
        if self.settlement_data is None:
            print("❌ 请先加载现货结算数据")
            return False
        
        # 按日期分组处理
        all_results = []
        
        for date, group in self.settlement_data.groupby(self.settlement_data['时间'].dt.date):
            print(f"  处理日期: {date}")
            
            # 排序数据
            group_sorted = group.sort_values('时间').reset_index(drop=True)
            
            # 创建15分钟结果数据
            results = []
            
            # 处理每个半小时数据点
            for i, row in group_sorted.iterrows():
                time_30min = row['时间']
                energy_30min = row['结算电量']  # 半小时电量 MWh
                
                # 计算平均功率 MW = MWh / 0.5h
                avg_power = energy_30min / 0.5
                
                # 计算15分钟电量 MWh = MW * 0.25h
                energy_15min = avg_power * 0.25
                
                # 生成两个15分钟时间点
                # 例如：00:30的数据对应00:15和00:30两个时间点
                hour = time_30min.hour
                minute = time_30min.minute
                
                if minute == 30:
                    # 00:30的数据对应00:15和00:30
                    time1 = f"{hour:02d}:15"
                    time2 = f"{hour:02d}:30"
                elif minute == 0:
                    # 01:00的数据对应00:45和01:00
                    if hour == 0:
                        time1 = "23:45"  # 前一天的23:45
                        time2 = "24:00"  # 当天的00:00表示为24:00
                    else:
                        time1 = f"{hour-1:02d}:45"
                        time2 = f"{hour:02d}:00"
                
                # 添加两个15分钟数据点
                results.append({
                    '时间': time1,
                    '电量(MWh)': energy_15min,
                    '功率(MW)': avg_power,
                    '日期': date.strftime('%Y-%m-%d'),
                    '原始时间': time_30min,
                    '原始电量': energy_30min
                })
                
                results.append({
                    '时间': time2,
                    '电量(MWh)': energy_15min,
                    '功率(MW)': avg_power,
                    '日期': date.strftime('%Y-%m-%d'),
                    '原始时间': time_30min,
                    '原始电量': energy_30min
                })
            
            all_results.extend(results)
        
        # 转换为DataFrame
        self.processed_data = pd.DataFrame(all_results)
        
        # 按时间排序
        def time_sort_key(time_str):
            if time_str == "24:00":
                return 24 * 60
            hour, minute = map(int, time_str.split(':'))
            return hour * 60 + minute
        
        self.processed_data['sort_key'] = self.processed_data['时间'].apply(time_sort_key)
        self.processed_data = self.processed_data.sort_values(['日期', 'sort_key']).drop('sort_key', axis=1).reset_index(drop=True)
        
        print(f"✅ 15分钟功率数据生成完成: {len(self.processed_data)} 条记录")
        
        # 显示统计信息
        total_energy = self.processed_data['电量(MWh)'].sum()
        avg_power = self.processed_data['功率(MW)'].mean()
        print(f"   总电量: {total_energy:.2f} MWh")
        print(f"   平均功率: {avg_power:.2f} MW")
        print(f"   功率范围: {self.processed_data['功率(MW)'].min():.2f} - {self.processed_data['功率(MW)'].max():.2f} MW")
        
        return True
    
    def save_results(self, output_file="中栋电厂15分钟出力曲线_修正版.xlsx"):
        """
        保存处理结果
        """
        print(f"\n正在保存结果...")
        
        if self.processed_data is None:
            print("❌ 没有数据可保存")
            return False
        
        try:
            output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 1. 完整15分钟数据
                output_data = self.processed_data[['时间', '电量(MWh)', '功率(MW)', '日期']].copy()
                output_data.to_excel(writer, sheet_name='15分钟出力曲线', index=False)
                
                # 2. 按日期分表
                for date in output_data['日期'].unique():
                    daily_data = output_data[output_data['日期'] == date].copy()
                    daily_data = daily_data[['时间', '电量(MWh)', '功率(MW)']]  # 移除日期列
                    sheet_name = f"出力曲线_{date}"
                    daily_data.to_excel(writer, sheet_name=sheet_name, index=False)
                    print(f"    {date}: {len(daily_data)} 个时间点")
                
                # 3. 统计汇总
                summary_data = output_data.groupby('日期').agg({
                    '电量(MWh)': ['sum', 'mean', 'min', 'max'],
                    '功率(MW)': ['mean', 'min', 'max']
                }).round(3)
                
                summary_data.columns = ['总电量', '平均电量', '最小电量', '最大电量', 
                                      '平均功率', '最小功率', '最大功率']
                summary_data = summary_data.reset_index()
                summary_data.to_excel(writer, sheet_name='日统计汇总', index=False)
                
                # 4. 原始数据对比
                if self.settlement_data is not None:
                    comparison_data = self.settlement_data[['时间', '结算电量', '出清电价']].copy()
                    comparison_data.to_excel(writer, sheet_name='原始半小时数据', index=False)
                
                # 5. 详细对比数据（包含原始信息）
                detailed_data = self.processed_data.copy()
                detailed_data.to_excel(writer, sheet_name='详细转换数据', index=False)
            
            print(f"✅ 结果已保存到: {output_path}")
            
            # 显示统计信息
            print(f"\n📊 处理统计:")
            print(f"   处理日期数: {output_data['日期'].nunique()}")
            print(f"   总时间点数: {len(output_data)}")
            print(f"   总电量: {output_data['电量(MWh)'].sum():.2f} MWh")
            print(f"   平均功率: {output_data['功率(MW)'].mean():.2f} MW")
            
            # 显示7月1日的前几个数据点用于验证
            print(f"\n🔍 7月1日前10个数据点验证:")
            july1_data = output_data[output_data['日期'] == '2025-07-01'].head(10)
            for _, row in july1_data.iterrows():
                print(f"   {row['时间']}: 电量={row['电量(MWh)']:.4f} MWh, 功率={row['功率(MW)']:.4f} MW")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False

def main():
    """
    主函数
    """
    print("中栋电厂出力曲线处理工具 - 修正版")
    print("="*60)
    
    # 文件路径
    settlement_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/现货结算表/中栋电厂2025-07-01-day_sbs_gen_pub_detail_25.xlsx"
    
    if not os.path.exists(settlement_file):
        print(f"❌ 现货结算表文件不存在: {settlement_file}")
        return
    
    processor = PowerCurveProcessor()
    
    # 1. 加载数据
    if not processor.load_settlement_data(settlement_file):
        return
    
    # 2. 转换为15分钟功率数据
    if not processor.convert_to_15min_power():
        return
    
    # 3. 保存结果
    if processor.save_results():
        print(f"\n🎉 处理完成！")
        print(f"📊 成功将半小时电量数据转换为15分钟功率数据")
        print(f"⚡ 功率计算公式: 功率(MW) = 半小时电量(MWh) / 0.5h")
        print(f"📁 结果已保存到Excel文件")
    else:
        print(f"\n❌ 处理失败！")

if __name__ == "__main__":
    main()
