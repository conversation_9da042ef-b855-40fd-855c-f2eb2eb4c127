#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级公司用电量日预测数学模型
基于6月数据，结合天气信息的智能预测系统
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.preprocessing import StandardScaler, LabelEncoder, PolynomialFeatures
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score, mean_absolute_percentage_error
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

class AdvancedCompanyPowerModel:
    def __init__(self):
        """
        初始化高级公司用电量预测模型
        """
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.feature_importance = {}
        self.company_profiles = {}
        self.weather_patterns = {}
        self.model_performance = {}
        self.prediction_results = {}
        
        print("🏢 高级公司用电量预测模型")
        print("📊 支持多种机器学习算法")
        print("🌤️ 深度集成天气信息")
        print("📈 提供公司用电特征分析")
        print("🎯 高精度日预测能力")
    
    def load_and_preprocess_data(self, file_path):
        """
        加载和预处理6月数据
        """
        print("\n📋 加载6月用电数据...")
        
        # 读取数据
        self.df = pd.read_excel(file_path)
        print(f"原始数据: {self.df.shape}")
        
        # 数据预处理
        self.df['时间'] = pd.to_datetime(self.df['时间'])
        
        # 数据清洗
        print("🧹 数据清洗...")
        original_count = len(self.df)
        
        # 移除异常值
        self.df = self.df[self.df['总电量(kWh)'] >= 0]
        self.df = self.df[self.df['总电量(kWh)'] < self.df['总电量(kWh)'].quantile(0.995)]  # 移除极端异常值
        
        # 移除缺失关键信息的记录
        self.df = self.df.dropna(subset=['公司名称', '户号', '总电量(kWh)'])
        
        print(f"清洗后数据: {self.df.shape} (移除了{original_count - len(self.df)}条记录)")
        
        # 基础统计
        self.analyze_data_overview()
        
        return self.df
    
    def analyze_data_overview(self):
        """
        数据概览分析
        """
        print(f"\n📊 数据概览:")
        print(f"  时间跨度: {self.df['时间'].min().date()} 到 {self.df['时间'].max().date()}")
        print(f"  总天数: {self.df['时间'].dt.date.nunique()}")
        print(f"  总户号数: {self.df['户号'].nunique()}")
        print(f"  总公司数: {self.df['公司名称'].nunique()}")
        print(f"  总记录数: {len(self.df)}")
        
        # 用电量统计
        print(f"\n⚡ 用电量统计:")
        print(f"  总电量平均: {self.df['总电量(kWh)'].mean():.2f} kWh")
        print(f"  总电量中位数: {self.df['总电量(kWh)'].median():.2f} kWh")
        print(f"  总电量标准差: {self.df['总电量(kWh)'].std():.2f} kWh")
        print(f"  总电量范围: {self.df['总电量(kWh)'].min():.2f} - {self.df['总电量(kWh)'].max():.2f} kWh")
        
        # 天气统计
        print(f"\n🌤️ 天气统计:")
        print(f"  温度范围: {self.df['最低温度(°C)'].min():.1f}°C - {self.df['最高温度(°C)'].max():.1f}°C")
        print(f"  平均温度: {((self.df['最高温度(°C)'] + self.df['最低温度(°C)']) / 2).mean():.1f}°C")
        print(f"  湿度范围: {self.df['湿度(%)'].min():.1f}% - {self.df['湿度(%)'].max():.1f}%")
        print(f"  AQI范围: {self.df['AQI'].min():.1f} - {self.df['AQI'].max():.1f}")
        print(f"  天气类型: {self.df['天气'].nunique()}种 {list(self.df['天气'].unique())}")
    
    def create_company_profiles(self):
        """
        创建详细的公司用电特征画像
        """
        print("\n🏢 创建公司用电特征画像...")
        
        # 按公司统计用电特征
        company_stats = self.df.groupby('公司名称').agg({
            '总电量(kWh)': ['mean', 'std', 'min', 'max', 'count'],
            '尖电量(kWh)': ['mean', 'sum'],
            '峰电量(kWh)': ['mean', 'sum'],
            '平电量(kWh)': ['mean', 'sum'],
            '谷电量(kWh)': ['mean', 'sum'],
            '户号': 'nunique',
            '地区': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.iloc[0],
            '签约地区': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.iloc[0]
        }).round(2)
        
        # 扁平化列名
        company_stats.columns = ['_'.join(col).strip() for col in company_stats.columns]
        company_stats = company_stats.reset_index()
        
        # 计算用电特征指标
        company_stats['日均用电'] = company_stats['总电量(kWh)_mean']
        company_stats['用电稳定性'] = company_stats['总电量(kWh)_std'] / (company_stats['总电量(kWh)_mean'] + 1)
        company_stats['用电变异系数'] = company_stats['总电量(kWh)_std'] / (company_stats['总电量(kWh)_mean'] + 1)
        
        # 用电结构分析
        company_stats['尖电占比'] = company_stats['尖电量(kWh)_sum'] / (company_stats['总电量(kWh)_mean'] * company_stats['总电量(kWh)_count'] + 1)
        company_stats['峰电占比'] = company_stats['峰电量(kWh)_sum'] / (company_stats['总电量(kWh)_mean'] * company_stats['总电量(kWh)_count'] + 1)
        company_stats['平电占比'] = company_stats['平电量(kWh)_sum'] / (company_stats['总电量(kWh)_mean'] * company_stats['总电量(kWh)_count'] + 1)
        company_stats['谷电占比'] = company_stats['谷电量(kWh)_sum'] / (company_stats['总电量(kWh)_mean'] * company_stats['总电量(kWh)_count'] + 1)
        
        # 用电模式特征
        company_stats['尖峰比'] = company_stats['尖电量(kWh)_mean'] / (company_stats['峰电量(kWh)_mean'] + 1)
        company_stats['峰谷比'] = company_stats['峰电量(kWh)_mean'] / (company_stats['谷电量(kWh)_mean'] + 1)
        
        # 公司规模分类
        company_stats['用电规模'] = pd.cut(company_stats['日均用电'], 
                                       bins=[0, 500, 2000, 8000, 30000, np.inf], 
                                       labels=['微型', '小型', '中型', '大型', '超大型'])
        
        # 用电模式聚类分析
        features_for_clustering = ['日均用电', '用电稳定性', '尖峰比', '峰谷比', '尖电占比', '峰电占比']
        X_cluster = company_stats[features_for_clustering].fillna(0)
        
        # 标准化聚类特征
        scaler = StandardScaler()
        X_cluster_scaled = scaler.fit_transform(X_cluster)
        
        # K-means聚类
        kmeans = KMeans(n_clusters=5, random_state=42, n_init=10)
        company_stats['用电模式'] = kmeans.fit_predict(X_cluster_scaled)
        
        # 模式标签（基于聚类中心特征）
        mode_labels = {0: '稳定节能型', 1: '高峰集中型', 2: '波动敏感型', 3: '全天均衡型', 4: '大用户型'}
        company_stats['用电模式标签'] = company_stats['用电模式'].map(mode_labels)
        
        self.company_profiles = company_stats
        
        print(f"✅ 完成 {len(company_stats)} 家公司的特征画像")
        print(f"📊 公司规模分布:")
        for scale, count in company_stats['用电规模'].value_counts().items():
            print(f"    {scale}: {count}家")
        
        print(f"🔄 用电模式分布:")
        for mode, count in company_stats['用电模式标签'].value_counts().items():
            print(f"    {mode}: {count}家")
        
        return company_stats
    
    def analyze_weather_impact(self):
        """
        深度分析天气对用电量的影响
        """
        print("\n🌤️ 深度分析天气影响...")
        
        # 计算天气特征
        self.df['平均温度'] = (self.df['最高温度(°C)'] + self.df['最低温度(°C)']) / 2
        self.df['温差'] = self.df['最高温度(°C)'] - self.df['最低温度(°C)']
        
        # 温度区间分析
        self.df['温度区间'] = pd.cut(self.df['平均温度'], 
                                 bins=[0, 18, 22, 26, 30, 35, 50], 
                                 labels=['寒冷', '凉爽', '舒适', '温暖', '炎热', '酷热'])
        
        # 按温度区间统计用电量
        temp_impact = self.df.groupby('温度区间')['总电量(kWh)'].agg(['mean', 'std', 'count', 'median']).round(2)
        
        # 湿度影响分析
        self.df['湿度区间'] = pd.cut(self.df['湿度(%)'], 
                                 bins=[0, 50, 65, 75, 85, 100], 
                                 labels=['干燥', '适中', '潮湿', '很潮湿', '极潮湿'])
        
        humidity_impact = self.df.groupby('湿度区间')['总电量(kWh)'].agg(['mean', 'std', 'count', 'median']).round(2)
        
        # 天气类型影响
        weather_impact = self.df.groupby('天气')['总电量(kWh)'].agg(['mean', 'std', 'count', 'median']).round(2)
        
        # AQI影响分析
        self.df['AQI区间'] = pd.cut(self.df['AQI'], 
                                bins=[0, 50, 100, 150, 200, 300, 500], 
                                labels=['优', '良', '轻度污染', '中度污染', '重度污染', '严重污染'])
        
        aqi_impact = self.df.groupby('AQI区间')['总电量(kWh)'].agg(['mean', 'std', 'count', 'median']).round(2)
        
        # 综合天气指数
        self.df['制冷度日'] = np.maximum(self.df['平均温度'] - 26, 0)  # 制冷需求
        self.df['制热度日'] = np.maximum(18 - self.df['平均温度'], 0)  # 制热需求
        self.df['体感温度'] = self.df['平均温度'] + 0.1 * (self.df['湿度(%)'] - 50)  # 体感温度
        
        self.weather_patterns = {
            'temperature': temp_impact,
            'humidity': humidity_impact,
            'weather_type': weather_impact,
            'aqi': aqi_impact
        }
        
        print(f"🌡️ 温度影响分析:")
        for idx, row in temp_impact.iterrows():
            if pd.notna(idx):
                print(f"    {idx}: 平均{row['mean']:.0f}kWh, 中位数{row['median']:.0f}kWh ({row['count']}天)")
        
        print(f"💧 湿度影响分析:")
        for idx, row in humidity_impact.iterrows():
            if pd.notna(idx):
                print(f"    {idx}: 平均{row['mean']:.0f}kWh, 中位数{row['median']:.0f}kWh ({row['count']}天)")
        
        print(f"☁️ 天气类型影响:")
        for idx, row in weather_impact.iterrows():
            print(f"    {idx}: 平均{row['mean']:.0f}kWh, 中位数{row['median']:.0f}kWh ({row['count']}天)")
        
        return self.weather_patterns

    def advanced_feature_engineering(self):
        """
        高级特征工程
        """
        print("\n🔧 高级特征工程...")

        df_fe = self.df.copy()

        # 时间特征
        df_fe['月份'] = df_fe['时间'].dt.month
        df_fe['日期'] = df_fe['时间'].dt.day
        df_fe['星期'] = df_fe['时间'].dt.dayofweek
        df_fe['是否周末'] = (df_fe['星期'] >= 5).astype(int)
        df_fe['是否月初'] = (df_fe['日期'] <= 10).astype(int)
        df_fe['是否月末'] = (df_fe['日期'] >= 20).astype(int)

        # 天气特征（已在analyze_weather_impact中计算）
        if '平均温度' not in df_fe.columns:
            df_fe['平均温度'] = (df_fe['最高温度(°C)'] + df_fe['最低温度(°C)']) / 2
            df_fe['温差'] = df_fe['最高温度(°C)'] - df_fe['最低温度(°C)']
            df_fe['制冷度日'] = np.maximum(df_fe['平均温度'] - 26, 0)
            df_fe['制热度日'] = np.maximum(18 - df_fe['平均温度'], 0)
            df_fe['体感温度'] = df_fe['平均温度'] + 0.1 * (df_fe['湿度(%)'] - 50)

        # 温度的非线性特征
        df_fe['温度平方'] = df_fe['平均温度'] ** 2
        df_fe['温度立方'] = df_fe['平均温度'] ** 3
        df_fe['温度对数'] = np.log(df_fe['平均温度'] + 1)

        # 湿度和温度交互特征
        df_fe['温湿指数'] = df_fe['平均温度'] * df_fe['湿度(%)'] / 100
        df_fe['干燥指数'] = df_fe['平均温度'] / (df_fe['湿度(%)'] + 1)

        # 空气质量特征
        df_fe['AQI对数'] = np.log(df_fe['AQI'] + 1)
        df_fe['污染严重'] = (df_fe['AQI'] > 100).astype(int)

        # 降水特征
        df_fe['有降水'] = (df_fe['降水量(mm)'] > 0).astype(int)
        df_fe['降水等级'] = pd.cut(df_fe['降水量(mm)'],
                               bins=[0, 0.1, 10, 25, 50, 1000],
                               labels=['无', '小雨', '中雨', '大雨', '暴雨'])

        # 公司特征（从公司画像中获取）
        if hasattr(self, 'company_profiles'):
            company_features = self.company_profiles[['公司名称', '日均用电', '用电稳定性', '用电规模', '用电模式标签', '尖峰比', '峰谷比']].copy()
            df_fe = df_fe.merge(company_features, on='公司名称', how='left')

        # 历史用电特征
        df_fe = df_fe.sort_values(['户号', '时间'])

        # 滑动窗口特征
        for window in [3, 7]:
            df_fe[f'历史{window}日平均用电'] = df_fe.groupby('户号')['总电量(kWh)'].transform(lambda x: x.rolling(window).mean().shift(1))
            df_fe[f'历史{window}日最大用电'] = df_fe.groupby('户号')['总电量(kWh)'].transform(lambda x: x.rolling(window).max().shift(1))
            df_fe[f'历史{window}日最小用电'] = df_fe.groupby('户号')['总电量(kWh)'].transform(lambda x: x.rolling(window).min().shift(1))

        # 用电趋势特征
        df_fe['用电增长率'] = df_fe.groupby('户号')['总电量(kWh)'].pct_change()
        df_fe['用电波动率'] = df_fe.groupby('户号')['总电量(kWh)'].transform(lambda x: x.rolling(7).std().shift(1))

        # 地区特征
        region_stats = df_fe.groupby('地区')['总电量(kWh)'].agg(['mean', 'std']).reset_index()
        region_stats.columns = ['地区', '地区平均用电', '地区用电标准差']
        df_fe = df_fe.merge(region_stats, on='地区', how='left')

        # 同行业特征（基于公司名称关键词）
        df_fe['行业类型'] = '其他'
        df_fe.loc[df_fe['公司名称'].str.contains('科技|技术|电子|信息', na=False), '行业类型'] = '科技'
        df_fe.loc[df_fe['公司名称'].str.contains('制造|机械|设备|工业', na=False), '行业类型'] = '制造'
        df_fe.loc[df_fe['公司名称'].str.contains('贸易|商贸|销售', na=False), '行业类型'] = '贸易'
        df_fe.loc[df_fe['公司名称'].str.contains('服装|纺织|袜业', na=False), '行业类型'] = '纺织'
        df_fe.loc[df_fe['公司名称'].str.contains('建筑|装饰|工程', na=False), '行业类型'] = '建筑'

        # 行业平均用电
        industry_avg = df_fe.groupby('行业类型')['总电量(kWh)'].mean()
        df_fe['行业平均用电'] = df_fe['行业类型'].map(industry_avg)

        self.df_features = df_fe

        print(f"✅ 特征工程完成")
        print(f"📊 原始特征数: {len(self.df.columns)}")
        print(f"📊 最终特征数: {len(df_fe.columns)}")
        print(f"🆕 新增特征数: {len(df_fe.columns) - len(self.df.columns)}")

        return df_fe

    def prepare_model_features(self):
        """
        准备模型特征
        """
        print("\n🎯 准备模型特征...")

        if not hasattr(self, 'df_features'):
            self.advanced_feature_engineering()

        df_model = self.df_features.copy()

        # 选择数值特征
        numeric_features = [
            # 时间特征
            '月份', '日期', '星期', '是否周末', '是否月初', '是否月末',

            # 天气特征
            '最高温度(°C)', '最低温度(°C)', '平均温度', '温差', '温度平方', '温度立方', '温度对数',
            '湿度(%)', '温湿指数', '干燥指数', '体感温度',
            'AQI', 'AQI对数', '污染严重',
            '降水量(mm)', '有降水',
            '气压(hPa)',
            '制冷度日', '制热度日',

            # 公司特征
            '日均用电', '用电稳定性', '尖峰比', '峰谷比',

            # 历史特征
            '历史3日平均用电', '历史7日平均用电',
            '历史3日最大用电', '历史7日最大用电',
            '历史3日最小用电', '历史7日最小用电',
            '用电增长率', '用电波动率',

            # 地区特征
            '地区平均用电', '地区用电标准差',

            # 行业特征
            '行业平均用电'
        ]

        # 分类特征
        categorical_features = ['天气', '风向', '日期类型', '地区', '用电规模', '用电模式标签', '行业类型', '降水等级']

        # 选择存在的特征
        available_numeric = [col for col in numeric_features if col in df_model.columns]
        available_categorical = [col for col in categorical_features if col in df_model.columns]

        print(f"📊 可用数值特征: {len(available_numeric)} 个")
        print(f"📊 可用分类特征: {len(available_categorical)} 个")

        # 处理分类特征
        for cat_feature in available_categorical:
            if cat_feature not in self.encoders:
                self.encoders[cat_feature] = LabelEncoder()
                df_model[f'{cat_feature}_encoded'] = self.encoders[cat_feature].fit_transform(df_model[cat_feature].astype(str))
            else:
                df_model[f'{cat_feature}_encoded'] = self.encoders[cat_feature].transform(df_model[cat_feature].astype(str))
            available_numeric.append(f'{cat_feature}_encoded')

        # 最终特征集
        final_features = available_numeric

        # 处理缺失值和无穷大值
        X = df_model[final_features].fillna(0)

        # 处理无穷大值
        X = X.replace([np.inf, -np.inf], 0)

        # 处理异常大的值
        for col in X.columns:
            if X[col].dtype in ['float64', 'int64']:
                # 将超过99.9%分位数的值限制在99.9%分位数
                upper_limit = X[col].quantile(0.999)
                lower_limit = X[col].quantile(0.001)
                X[col] = X[col].clip(lower=lower_limit, upper=upper_limit)

        # 目标变量
        y = df_model['总电量(kWh)']

        print(f"✅ 模型特征准备完成")
        print(f"📊 最终特征数: {len(final_features)}")
        print(f"📊 样本数: {len(X)}")

        return X, y, final_features

    def train_multiple_models(self, X, y, final_features):
        """
        训练多种机器学习模型
        """
        print("\n🤖 训练多种机器学习模型...")

        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # 特征标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)

        # 定义模型
        models = {
            'RandomForest': RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1),
            'GradientBoosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'ExtraTrees': ExtraTreesRegressor(n_estimators=100, random_state=42, n_jobs=-1),
            'Ridge': Ridge(alpha=1.0),
            'Lasso': Lasso(alpha=1.0),
            'ElasticNet': ElasticNet(alpha=1.0, l1_ratio=0.5),
            'LinearRegression': LinearRegression()
        }

        # 训练和评估模型
        results = {}

        for model_name, model in models.items():
            print(f"\n训练 {model_name}...")

            # 选择是否使用标准化数据
            if model_name in ['Ridge', 'Lasso', 'ElasticNet', 'LinearRegression']:
                X_train_use = X_train_scaled
                X_test_use = X_test_scaled
            else:
                X_train_use = X_train
                X_test_use = X_test

            # 训练模型
            model.fit(X_train_use, y_train)

            # 预测
            y_pred_train = model.predict(X_train_use)
            y_pred_test = model.predict(X_test_use)

            # 评估指标
            train_mae = mean_absolute_error(y_train, y_pred_train)
            test_mae = mean_absolute_error(y_test, y_pred_test)
            train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
            test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
            train_r2 = r2_score(y_train, y_pred_train)
            test_r2 = r2_score(y_test, y_pred_test)
            test_mape = mean_absolute_percentage_error(y_test, y_pred_test) * 100

            # 交叉验证
            cv_scores = cross_val_score(model, X_train_use, y_train, cv=5, scoring='r2')

            results[model_name] = {
                'model': model,
                'train_mae': train_mae,
                'test_mae': test_mae,
                'train_rmse': train_rmse,
                'test_rmse': test_rmse,
                'train_r2': train_r2,
                'test_r2': test_r2,
                'test_mape': test_mape,
                'cv_r2_mean': cv_scores.mean(),
                'cv_r2_std': cv_scores.std(),
                'scaler': scaler if model_name in ['Ridge', 'Lasso', 'ElasticNet', 'LinearRegression'] else None
            }

            print(f"  训练R²: {train_r2:.4f}, 测试R²: {test_r2:.4f}")
            print(f"  测试MAE: {test_mae:.2f}, 测试RMSE: {test_rmse:.2f}")
            print(f"  测试MAPE: {test_mape:.2f}%")
            print(f"  交叉验证R²: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")

        # 选择最佳模型
        best_model_name = max(results.keys(), key=lambda k: results[k]['test_r2'])
        best_model_info = results[best_model_name]

        print(f"\n🏆 最佳模型: {best_model_name}")
        print(f"   测试R²: {best_model_info['test_r2']:.4f}")
        print(f"   测试MAE: {best_model_info['test_mae']:.2f} kWh")
        print(f"   测试MAPE: {best_model_info['test_mape']:.2f}%")

        # 保存模型和结果
        self.models = {name: info['model'] for name, info in results.items()}
        self.scalers = {name: info['scaler'] for name, info in results.items() if info['scaler'] is not None}
        self.model_performance = results
        self.best_model_name = best_model_name
        self.final_features = final_features

        # 特征重要性分析
        if hasattr(best_model_info['model'], 'feature_importances_'):
            importance = pd.DataFrame({
                'feature': final_features,
                'importance': best_model_info['model'].feature_importances_
            }).sort_values('importance', ascending=False)

            self.feature_importance = importance

            print(f"\n📊 {best_model_name} 前10个重要特征:")
            for i, row in importance.head(10).iterrows():
                print(f"   {row['feature']}: {row['importance']:.4f}")

        return results

    def predict_future_consumption(self, target_date, weather_data=None, target_users_file=None):
        """
        预测未来用电量
        """
        print(f"\n🔮 预测 {target_date} 的用电量...")

        if not hasattr(self, 'best_model_name'):
            print("❌ 请先训练模型")
            return None

        # 如果提供了目标用户文件，使用该文件中的用户列表
        if target_users_file:
            try:
                df_target_users = pd.read_excel(target_users_file)
                target_users = df_target_users['户号'].unique()
                print(f"📋 从目标文件加载用户: {len(target_users)} 个")

                # 创建预测数据框架
                pred_data = []
                for user in target_users:
                    # 从训练数据中查找该用户的公司信息
                    user_info = self.df[self.df['户号'] == user]
                    if len(user_info) > 0:
                        company = user_info['公司名称'].iloc[0]
                    else:
                        company = '未知公司'  # 如果用户不在训练数据中

                    pred_data.append({
                        '公司名称': company,
                        '户号': user,
                        '时间': pd.to_datetime(target_date)
                    })

                df_pred = pd.DataFrame(pred_data)
                print(f"✅ 基于目标文件创建预测数据: {len(df_pred)} 个用户")

            except Exception as e:
                print(f"❌ 无法读取目标用户文件: {e}")
                print("🔄 使用训练数据中的用户")
                # 回退到原来的方法
                companies = self.df['公司名称'].unique()
                users = self.df['户号'].unique()

                pred_data = []
                for company in companies:
                    company_users = self.df[self.df['公司名称'] == company]['户号'].unique()
                    for user in company_users:
                        if pd.notna(user):  # 排除NaN用户
                            pred_data.append({
                                '公司名称': company,
                                '户号': user,
                                '时间': pd.to_datetime(target_date)
                            })

                df_pred = pd.DataFrame(pred_data)
        else:
            # 使用训练数据中的所有用户
            companies = self.df['公司名称'].unique()
            users = self.df['户号'].unique()

            pred_data = []
            for company in companies:
                company_users = self.df[self.df['公司名称'] == company]['户号'].unique()
                for user in company_users:
                    if pd.notna(user):  # 排除NaN用户
                        pred_data.append({
                            '公司名称': company,
                            '户号': user,
                            '时间': pd.to_datetime(target_date)
                        })

            df_pred = pd.DataFrame(pred_data)

        # 添加默认天气数据（如果未提供）
        if weather_data is None:
            # 使用历史平均天气数据
            weather_defaults = {
                '最高温度(°C)': self.df['最高温度(°C)'].mean(),
                '最低温度(°C)': self.df['最低温度(°C)'].mean(),
                '湿度(%)': self.df['湿度(%)'].mean(),
                'AQI': self.df['AQI'].mean(),
                '降水量(mm)': 0.0,
                '气压(hPa)': self.df['气压(hPa)'].mean(),
                '天气': '晴',
                '风向': '南风',
                '日期类型': '工作日'
            }
        else:
            weather_defaults = weather_data

        # 添加天气特征
        for key, value in weather_defaults.items():
            df_pred[key] = value

        # 添加地区信息（从历史数据中获取）
        user_regions = self.df.groupby('户号')['地区'].first().to_dict()
        df_pred['地区'] = df_pred['户号'].map(user_regions)

        # 特征工程
        df_pred = self.add_prediction_features(df_pred)

        # 准备特征 - 只选择存在的特征
        available_features = [col for col in self.final_features if col in df_pred.columns]
        missing_features = [col for col in self.final_features if col not in df_pred.columns]

        if missing_features:
            print(f"⚠️ 缺失特征: {len(missing_features)} 个，将用0填充")
            for feature in missing_features:
                df_pred[feature] = 0

        X_pred = df_pred[self.final_features].fillna(0)

        # 使用最佳模型预测
        best_model = self.models[self.best_model_name]

        if self.best_model_name in self.scalers:
            scaler = self.scalers[self.best_model_name]
            X_pred_scaled = scaler.transform(X_pred)
            predictions = best_model.predict(X_pred_scaled)
        else:
            predictions = best_model.predict(X_pred)

        # 确保预测值为正数
        predictions = np.maximum(predictions, 0)

        # 创建预测结果
        df_pred['预测总电量(kWh)'] = predictions

        # 基于历史比例预测分时电量
        df_pred = self.predict_time_based_consumption(df_pred)

        result = df_pred[['公司名称', '户号', '时间', '预测总电量(kWh)', '预测尖电量(kWh)', '预测峰电量(kWh)', '预测平电量(kWh)', '预测谷电量(kWh)']]

        print(f"✅ 完成 {len(result)} 个用户的预测")
        print(f"📊 预测总电量范围: {result['预测总电量(kWh)'].min():.2f} - {result['预测总电量(kWh)'].max():.2f} kWh")
        print(f"📊 预测总电量平均: {result['预测总电量(kWh)'].mean():.2f} kWh")

        return result

    def add_prediction_features(self, df_pred):
        """
        为预测数据添加特征
        """
        # 时间特征
        df_pred['月份'] = df_pred['时间'].dt.month
        df_pred['日期'] = df_pred['时间'].dt.day
        df_pred['星期'] = df_pred['时间'].dt.dayofweek
        df_pred['是否周末'] = (df_pred['星期'] >= 5).astype(int)
        df_pred['是否月初'] = (df_pred['日期'] <= 10).astype(int)
        df_pred['是否月末'] = (df_pred['日期'] >= 20).astype(int)

        # 天气特征
        df_pred['平均温度'] = (df_pred['最高温度(°C)'] + df_pred['最低温度(°C)']) / 2
        df_pred['温差'] = df_pred['最高温度(°C)'] - df_pred['最低温度(°C)']
        df_pred['温度平方'] = df_pred['平均温度'] ** 2
        df_pred['温度立方'] = df_pred['平均温度'] ** 3
        df_pred['温度对数'] = np.log(df_pred['平均温度'] + 1)
        df_pred['制冷度日'] = np.maximum(df_pred['平均温度'] - 26, 0)
        df_pred['制热度日'] = np.maximum(18 - df_pred['平均温度'], 0)
        df_pred['体感温度'] = df_pred['平均温度'] + 0.1 * (df_pred['湿度(%)'] - 50)
        df_pred['温湿指数'] = df_pred['平均温度'] * df_pred['湿度(%)'] / 100
        df_pred['干燥指数'] = df_pred['平均温度'] / (df_pred['湿度(%)'] + 1)
        df_pred['AQI对数'] = np.log(df_pred['AQI'] + 1)
        df_pred['污染严重'] = (df_pred['AQI'] > 100).astype(int)
        df_pred['有降水'] = (df_pred['降水量(mm)'] > 0).astype(int)

        # 公司特征
        if hasattr(self, 'company_profiles'):
            company_features = self.company_profiles[['公司名称', '日均用电', '用电稳定性', '用电规模', '用电模式标签', '尖峰比', '峰谷比']].copy()
            df_pred = df_pred.merge(company_features, on='公司名称', how='left')

        # 历史特征（使用历史平均值）
        user_history = self.df.groupby('户号')['总电量(kWh)'].agg(['mean', 'std']).reset_index()
        user_history.columns = ['户号', '历史平均用电', '历史用电标准差']
        df_pred = df_pred.merge(user_history, on='户号', how='left')

        # 填充历史特征
        for window in [3, 7]:
            df_pred[f'历史{window}日平均用电'] = df_pred['历史平均用电']
            df_pred[f'历史{window}日最大用电'] = df_pred['历史平均用电'] * 1.2
            df_pred[f'历史{window}日最小用电'] = df_pred['历史平均用电'] * 0.8

        df_pred['用电增长率'] = 0.0
        df_pred['用电波动率'] = df_pred['历史用电标准差'].fillna(0)

        # 地区特征
        if '地区平均用电' not in df_pred.columns:
            region_stats = self.df.groupby('地区')['总电量(kWh)'].agg(['mean', 'std']).reset_index()
            region_stats.columns = ['地区', '地区平均用电', '地区用电标准差']
            df_pred = df_pred.merge(region_stats, on='地区', how='left')

        # 行业特征
        df_pred['行业类型'] = '其他'
        df_pred.loc[df_pred['公司名称'].str.contains('科技|技术|电子|信息', na=False), '行业类型'] = '科技'
        df_pred.loc[df_pred['公司名称'].str.contains('制造|机械|设备|工业', na=False), '行业类型'] = '制造'
        df_pred.loc[df_pred['公司名称'].str.contains('贸易|商贸|销售', na=False), '行业类型'] = '贸易'
        df_pred.loc[df_pred['公司名称'].str.contains('服装|纺织|袜业', na=False), '行业类型'] = '纺织'
        df_pred.loc[df_pred['公司名称'].str.contains('建筑|装饰|工程', na=False), '行业类型'] = '建筑'

        # 从特征数据中获取行业平均用电
        if hasattr(self, 'df_features') and '行业类型' in self.df_features.columns:
            industry_avg = self.df_features.groupby('行业类型')['总电量(kWh)'].mean()
            df_pred['行业平均用电'] = df_pred['行业类型'].map(industry_avg)
        else:
            df_pred['行业平均用电'] = df_pred['历史平均用电']  # 使用历史平均作为备选

        # 处理分类特征编码
        categorical_features = ['天气', '风向', '日期类型', '地区', '用电规模', '用电模式标签', '行业类型']
        for cat_feature in categorical_features:
            if cat_feature in df_pred.columns and cat_feature in self.encoders:
                try:
                    df_pred[f'{cat_feature}_encoded'] = self.encoders[cat_feature].transform(df_pred[cat_feature].astype(str))
                except:
                    # 如果遇到未见过的类别，使用最常见的类别
                    df_pred[f'{cat_feature}_encoded'] = 0

        return df_pred

    def predict_time_based_consumption(self, df_pred):
        """
        基于历史比例预测分时电量
        """
        # 计算历史分时电量比例
        time_ratios = {
            '尖电量': self.df['尖电量(kWh)'].sum() / self.df['总电量(kWh)'].sum(),
            '峰电量': self.df['峰电量(kWh)'].sum() / self.df['总电量(kWh)'].sum(),
            '平电量': self.df['平电量(kWh)'].sum() / self.df['总电量(kWh)'].sum(),
            '谷电量': self.df['谷电量(kWh)'].sum() / self.df['总电量(kWh)'].sum()
        }

        # 应用比例预测分时电量
        df_pred['预测尖电量(kWh)'] = df_pred['预测总电量(kWh)'] * time_ratios['尖电量']
        df_pred['预测峰电量(kWh)'] = df_pred['预测总电量(kWh)'] * time_ratios['峰电量']
        df_pred['预测平电量(kWh)'] = df_pred['预测总电量(kWh)'] * time_ratios['平电量']
        df_pred['预测谷电量(kWh)'] = df_pred['预测总电量(kWh)'] * time_ratios['谷电量']

        return df_pred

    def save_model_results(self, predictions=None, output_file="公司用电量预测模型结果.xlsx"):
        """
        保存模型结果
        """
        print(f"\n💾 保存模型结果...")

        output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"

        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 1. 模型性能对比
            if hasattr(self, 'model_performance'):
                performance_data = []
                for model_name, metrics in self.model_performance.items():
                    performance_data.append({
                        '模型': model_name,
                        '训练R²': metrics['train_r2'],
                        '测试R²': metrics['test_r2'],
                        '测试MAE': metrics['test_mae'],
                        '测试RMSE': metrics['test_rmse'],
                        '测试MAPE(%)': metrics['test_mape'],
                        '交叉验证R²': metrics['cv_r2_mean'],
                        '交叉验证标准差': metrics['cv_r2_std']
                    })

                pd.DataFrame(performance_data).to_excel(writer, sheet_name='模型性能对比', index=False)

            # 2. 特征重要性
            if hasattr(self, 'feature_importance'):
                self.feature_importance.to_excel(writer, sheet_name='特征重要性', index=False)

            # 3. 公司特征画像
            if hasattr(self, 'company_profiles'):
                self.company_profiles.to_excel(writer, sheet_name='公司特征画像', index=False)

            # 4. 天气影响分析
            if hasattr(self, 'weather_patterns'):
                for pattern_name, pattern_data in self.weather_patterns.items():
                    sheet_name = f'天气影响_{pattern_name}'
                    pattern_data.to_excel(writer, sheet_name=sheet_name)

            # 5. 预测结果
            if predictions is not None:
                predictions.to_excel(writer, sheet_name='预测结果', index=False)

                # 按公司汇总预测结果
                company_summary = predictions.groupby('公司名称').agg({
                    '预测总电量(kWh)': ['sum', 'mean', 'count'],
                    '预测尖电量(kWh)': 'sum',
                    '预测峰电量(kWh)': 'sum',
                    '预测平电量(kWh)': 'sum',
                    '预测谷电量(kWh)': 'sum'
                }).round(2)

                company_summary.columns = ['_'.join(col).strip() for col in company_summary.columns]
                company_summary = company_summary.reset_index()
                company_summary.to_excel(writer, sheet_name='公司预测汇总', index=False)

        print(f"✅ 模型结果已保存到: {output_path}")

        return output_path

def main():
    """
    主函数
    """
    print("🏢 高级公司用电量日预测数学模型")
    print("📊 基于6月数据，结合天气信息的智能预测系统")
    print("="*70)

    # 初始化模型
    model = AdvancedCompanyPowerModel()

    try:
        # 1. 加载和预处理数据
        file_path = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        df = model.load_and_preprocess_data(file_path)

        # 2. 创建公司特征画像
        company_profiles = model.create_company_profiles()

        # 3. 分析天气影响
        weather_patterns = model.analyze_weather_impact()

        # 4. 高级特征工程
        df_features = model.advanced_feature_engineering()

        # 5. 准备模型特征
        X, y, final_features = model.prepare_model_features()

        # 6. 训练多种模型
        model_results = model.train_multiple_models(X, y, final_features)

        # 7. 预测示例（预测7月18日）
        print(f"\n🔮 预测示例：7月18日用电量")
        weather_data = {
            '最高温度(°C)': 33.0,
            '最低温度(°C)': 27.0,
            '湿度(%)': 75.0,
            'AQI': 60.0,
            '降水量(mm)': 0.0,
            '气压(hPa)': 1013.0,
            '天气': '晴',
            '风向': '南风',
            '日期类型': '工作日'
        }

        predictions = model.predict_future_consumption('2025-07-18', weather_data)

        # 8. 保存结果
        output_file = model.save_model_results(predictions)

        print(f"\n🎉 高级公司用电量预测模型完成！")
        print(f"📊 处理公司数: {len(company_profiles)}")
        print(f"🤖 训练模型数: {len(model_results)}")
        print(f"🏆 最佳模型: {model.best_model_name}")
        print(f"📈 最佳R²: {model.model_performance[model.best_model_name]['test_r2']:.4f}")
        print(f"📁 结果已保存到: {output_file}")

        # 显示预测汇总
        if predictions is not None:
            total_prediction = predictions['预测总电量(kWh)'].sum()
            avg_prediction = predictions['预测总电量(kWh)'].mean()
            print(f"\n📊 7月18日预测汇总:")
            print(f"   总预测电量: {total_prediction:.2f} kWh")
            print(f"   平均用户用电: {avg_prediction:.2f} kWh")
            print(f"   预测用户数: {len(predictions)}")

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
