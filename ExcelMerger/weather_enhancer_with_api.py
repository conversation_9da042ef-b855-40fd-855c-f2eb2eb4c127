#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气信息增强工具 - 支持真实API
为Excel文件中的地区数据添加真实天气信息
"""

import pandas as pd
import requests
import json
import time
import os
from datetime import datetime, timedelta

class WeatherEnhancerWithAPI:
    def __init__(self, api_key=None):
        """
        初始化天气增强器
        
        Args:
            api_key: OpenWeatherMap API密钥
        """
        self.api_key = api_key
        self.base_url = "http://api.openweathermap.org/data/2.5/weather"
        
        # 中国主要城市的坐标映射（更精确的定位）
        self.city_coordinates = {
            '衢州': {'lat': 28.9700, 'lon': 118.8700},
            '诸暨': {'lat': 29.7138, 'lon': 120.2317},
            '温州': {'lat': 28.0000, 'lon': 120.6700},
            '杭州': {'lat': 30.2741, 'lon': 120.1551},
            '宁波': {'lat': 29.8683, 'lon': 121.5440},
            '嘉兴': {'lat': 30.7522, 'lon': 120.7500},
            '湖州': {'lat': 30.8703, 'lon': 120.0933},
            '绍兴': {'lat': 30.0023, 'lon': 120.5810},
            '金华': {'lat': 29.1028, 'lon': 119.6498},
            '台州': {'lat': 28.6129, 'lon': 121.4200},
            '丽水': {'lat': 28.4517, 'lon': 119.9217}
        }
    
    def get_weather_by_coordinates(self, city_name):
        """
        通过坐标获取天气数据
        
        Args:
            city_name: 城市名称
            
        Returns:
            dict: 天气数据字典
        """
        if not self.api_key:
            print("错误: 需要API密钥才能获取真实天气数据")
            return None
        
        if city_name not in self.city_coordinates:
            print(f"警告: 未找到城市 {city_name} 的坐标信息")
            return None
        
        try:
            coords = self.city_coordinates[city_name]
            
            # 构建API请求URL
            params = {
                'lat': coords['lat'],
                'lon': coords['lon'],
                'appid': self.api_key,
                'units': 'metric',  # 使用摄氏度
                'lang': 'zh_cn'     # 中文描述
            }
            
            response = requests.get(self.base_url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'temperature': round(data['main']['temp'], 1),
                    'feels_like': round(data['main']['feels_like'], 1),
                    'humidity': data['main']['humidity'],
                    'weather': data['weather'][0]['description'],
                    'weather_main': data['weather'][0]['main'],
                    'wind_speed': round(data['wind'].get('speed', 0), 1),
                    'wind_direction': data['wind'].get('deg', 0),
                    'pressure': data['main']['pressure'],
                    'visibility': data.get('visibility', 0) / 1000,  # 转换为公里
                    'clouds': data['clouds']['all'],
                    'sunrise': datetime.fromtimestamp(data['sys']['sunrise']).strftime('%H:%M'),
                    'sunset': datetime.fromtimestamp(data['sys']['sunset']).strftime('%H:%M')
                }
            else:
                print(f"API请求失败 {city_name}: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"获取天气数据失败 {city_name}: {e}")
            return None
    
    def enhance_excel_with_weather(self, input_file, output_file):
        """
        为Excel文件添加天气信息
        
        Args:
            input_file: 输入Excel文件路径
            output_file: 输出Excel文件路径
        """
        try:
            # 读取Excel文件
            print(f"正在读取文件: {os.path.basename(input_file)}")
            df = pd.read_excel(input_file)
            print(f"读取成功，共 {len(df)} 行数据")
            
            # 检查是否有地区列
            if '地区' not in df.columns:
                print("错误: 文件中没有找到'地区'列")
                return False
            
            # 获取唯一的地区列表
            unique_regions = df['地区'].dropna().unique()
            print(f"发现 {len(unique_regions)} 个不同地区: {list(unique_regions)}")
            
            # 为每个地区获取天气数据
            weather_cache = {}
            for region in unique_regions:
                print(f"正在获取 {region} 的天气数据...")
                weather_data = self.get_weather_by_coordinates(region)
                if weather_data:
                    weather_cache[region] = weather_data
                    print(f"  {region}: {weather_data['temperature']}°C, {weather_data['weather']}")
                else:
                    print(f"  {region}: 获取失败")
                
                time.sleep(1)  # API请求间隔，避免超出限制
            
            # 添加天气列
            df['气温(°C)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('temperature', None))
            df['体感温度(°C)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('feels_like', None))
            df['湿度(%)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('humidity', None))
            df['天气状况'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('weather', None))
            df['天气类型'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('weather_main', None))
            df['风速(m/s)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('wind_speed', None))
            df['风向(度)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('wind_direction', None))
            df['气压(hPa)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('pressure', None))
            df['能见度(km)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('visibility', None))
            df['云量(%)'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('clouds', None))
            df['日出时间'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('sunrise', None))
            df['日落时间'] = df['地区'].map(lambda x: weather_cache.get(x, {}).get('sunset', None))
            
            # 保存结果
            df.to_excel(output_file, index=False)
            print(f"\n结果已保存到: {output_file}")
            
            # 显示预览
            print(f"\n数据预览 (前3行):")
            weather_cols = ['地区', '气温(°C)', '湿度(%)', '天气状况', '风速(m/s)']
            available_cols = [col for col in weather_cols if col in df.columns]
            print(df[available_cols].head(3))
            
            # 显示天气统计
            print(f"\n天气数据统计:")
            for region, data in weather_cache.items():
                print(f"  {region}: {data['temperature']}°C, 湿度{data['humidity']}%, {data['weather']}")
            
            return True
            
        except Exception as e:
            print(f"处理文件时发生错误: {e}")
            return False

def main():
    """主函数"""
    print("天气信息增强工具 - 真实API版本")
    print("=" * 60)
    
    # 输入和输出文件路径
    input_file = "/Users/<USER>/Desktop/副本合并结果_用电量信息含地区.xlsx"
    output_file = "/Users/<USER>/RiderProjects/Solution3/合并结果_含真实天气信息.xlsx"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在 - {input_file}")
        return
    
    # 使用提供的API密钥
    api_key = "3f8b89c1952b3df138580d523d69b2f9"  # 使用用户提供的密钥
    
    if not api_key:
        print("\n使用模拟天气数据...")
        # 使用之前的模拟数据版本
        from weather_enhancer import WeatherEnhancer
        enhancer = WeatherEnhancer()
        success = enhancer.enhance_excel_with_weather(input_file, output_file.replace('真实', '模拟'))
    else:
        print(f"\n使用真实API获取天气数据...")
        enhancer = WeatherEnhancerWithAPI(api_key)
        success = enhancer.enhance_excel_with_weather(input_file, output_file)
    
    if success:
        print(f"\n✅ 处理完成！")
    else:
        print(f"\n❌ 处理失败！")
    
    print(f"\n💡 提示:")
    print(f"- 如需免费API密钥，请访问: https://openweathermap.org/api")
    print(f"- 免费版本每分钟最多60次请求，每月100万次请求")

if __name__ == "__main__":
    main()
