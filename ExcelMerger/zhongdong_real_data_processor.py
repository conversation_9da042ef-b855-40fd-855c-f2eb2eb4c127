#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中栋电厂真实现货结算表数据处理工具
仿照完整实时电能15分钟出力曲线的格式，处理7月1-14号的真实数据并合并
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import glob

class ZhongdongRealDataProcessor:
    def __init__(self):
        """
        初始化真实数据处理器
        """
        self.folder_path = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/现货结算表/"
        self.all_real_data = []
        self.processed_15min_data = []
        self.daily_summaries = []
    
    def find_and_process_files(self):
        """
        查找并处理7月1-14号的所有现货结算表文件
        """
        print("正在处理7月1-14号现货结算表文件...")
        
        success_count = 0
        
        for day in range(1, 15):  # 7月1号到7月14号
            date_str = f"2025-07-{day:02d}"
            file_name = f"中栋电厂{date_str}-day_sbs_gen_pub_detail_25.xlsx"
            file_path = os.path.join(self.folder_path, file_name)
            
            if os.path.exists(file_path):
                print(f"  处理: {date_str}")
                daily_data = self.extract_daily_data(file_path, date_str)
                
                if daily_data:
                    self.all_real_data.extend(daily_data)
                    success_count += 1
                    print(f"    ✅ 提取到 {len(daily_data)} 条数据")
                else:
                    print(f"    ❌ 未找到数据")
            else:
                print(f"  ❌ 文件不存在: {file_name}")
        
        print(f"\n✅ 处理完成: {success_count}/14 个文件")
        print(f"   总数据条数: {len(self.all_real_data)}")
        
        return success_count > 0
    
    def extract_daily_data(self, file_path, date_str):
        """
        从单个文件中提取实时电能数据
        """
        try:
            # 使用之前成功的方法：跳过4行，从表头开始读取
            df = pd.read_excel(file_path, skiprows=4)
            df.columns = ['项目名称', '结算单元', '空列1', '时间', '计量电量', '结算电量', '出清电价', '结算电价', '空列2', '结算电费', '空列3']

            # 过滤掉表头行
            df = df[df['结算单元'] != '结算单元']
            df = df.dropna(subset=['项目名称', '结算单元'])

            # 筛选中栋电厂的实时电能数据
            realtime_data = df[(df['项目名称'] == '实时电能') & (df['结算单元'] == '中栋电厂')]

            daily_data = []

            if len(realtime_data) > 0:
                # 转换数据类型
                realtime_data = realtime_data.copy()
                realtime_data['时间'] = pd.to_datetime(realtime_data['时间'])
                realtime_data['计量电量'] = pd.to_numeric(realtime_data['计量电量'], errors='coerce')

                # 过滤无效数据
                realtime_data = realtime_data.dropna(subset=['计量电量'])

                # 排序
                realtime_data = realtime_data.sort_values('时间').reset_index(drop=True)

                # 转换为标准格式
                for _, row in realtime_data.iterrows():
                    time_obj = row['时间']
                    energy = row['计量电量']
                    power = energy / 0.5  # 半小时功率

                    daily_data.append({
                        '日期': date_str,
                        '时间': time_obj.strftime('%H:%M:%S'),
                        '计量电量(MWh)': energy,
                        '功率(MW)': power
                    })

            return daily_data

        except Exception as e:
            print(f"    处理文件失败: {e}")
            return []
    
    def generate_15min_curves_for_all_days(self):
        """
        为所有天生成15分钟出力曲线
        """
        print("\n正在为所有天生成15分钟出力曲线...")
        
        if not self.all_real_data:
            print("❌ 没有数据可处理")
            return False
        
        # 按日期分组
        daily_groups = {}
        for data in self.all_real_data:
            date = data['日期']
            if date not in daily_groups:
                daily_groups[date] = []
            daily_groups[date].append(data)
        
        # 为每一天生成15分钟数据
        for date, daily_data in daily_groups.items():
            print(f"  处理日期: {date}")
            
            # 如果只有2条数据，需要插值生成完整的96个15分钟时间点
            if len(daily_data) == 2:
                # 使用插值方法生成完整的15分钟数据
                interpolated_data = self.interpolate_to_96_points(daily_data, date)
                self.processed_15min_data.extend(interpolated_data)
            else:
                # 如果有更多数据，按原逻辑处理
                for data_point in daily_data:
                    # 每个半小时数据生成2个15分钟点
                    time_str = data_point['时间']
                    energy = data_point['计量电量(MWh)']
                    power = data_point['功率(MW)']
                    
                    # 15分钟数据
                    quarter_energy = energy / 2
                    quarter_power = quarter_energy / 0.25
                    
                    # 生成时间点
                    hour, minute, second = map(int, time_str.split(':'))
                    
                    if minute == 30:
                        times = [f"{hour:02d}:15", f"{hour:02d}:30"]
                    elif minute == 0:
                        if hour == 0:
                            times = ["23:45", "24:00"]
                        else:
                            times = [f"{hour-1:02d}:45", f"{hour:02d}:00"]
                    
                    for time_point in times:
                        self.processed_15min_data.append({
                            '日期': date,
                            '时间': time_point,
                            '电量(MWh)': quarter_energy,
                            '功率(MW)': quarter_power
                        })
        
        print(f"✅ 15分钟数据生成完成: {len(self.processed_15min_data)} 个时间点")
        return True
    
    def interpolate_to_96_points(self, daily_data, date):
        """
        将2个数据点插值为96个15分钟时间点
        """
        # 排序数据
        daily_data.sort(key=lambda x: x['时间'])
        
        # 生成96个15分钟时间点
        time_points = []
        for hour in range(24):
            time_points.extend([
                f"{hour:02d}:15", f"{hour:02d}:30", f"{hour:02d}:45"
            ])
            if hour < 23:
                time_points.append(f"{hour+1:02d}:00")
        time_points.append("24:00")
        
        interpolated_data = []
        
        # 如果有2个数据点，进行线性插值
        if len(daily_data) == 2:
            first_data = daily_data[0]
            second_data = daily_data[1]
            
            # 解析时间
            first_time = datetime.strptime(first_data['时间'], '%H:%M:%S')
            second_time = datetime.strptime(second_data['时间'], '%H:%M:%S')
            
            # 如果第二个时间是19:30，可能跨日，需要调整
            if second_time < first_time:
                second_time += timedelta(days=1)
            
            first_power = first_data['功率(MW)']
            second_power = second_data['功率(MW)']
            
            for time_point in time_points:
                if time_point == "24:00":
                    current_time = datetime.strptime("00:00:00", '%H:%M:%S') + timedelta(days=1)
                else:
                    current_time = datetime.strptime(f"{time_point}:00", '%H:%M:%S')
                
                # 线性插值
                if current_time <= first_time:
                    power = first_power
                elif current_time >= second_time:
                    power = second_power
                else:
                    # 线性插值
                    time_ratio = (current_time - first_time).total_seconds() / (second_time - first_time).total_seconds()
                    power = first_power + time_ratio * (second_power - first_power)
                
                # 15分钟电量
                energy = power * 0.25
                
                interpolated_data.append({
                    '日期': date,
                    '时间': time_point,
                    '电量(MWh)': energy,
                    '功率(MW)': power
                })
        
        return interpolated_data
    
    def generate_daily_summaries(self):
        """
        生成每日统计汇总
        """
        print("\n正在生成每日统计汇总...")
        
        # 按日期分组统计
        daily_groups = {}
        for data in self.processed_15min_data:
            date = data['日期']
            if date not in daily_groups:
                daily_groups[date] = []
            daily_groups[date].append(data)
        
        for date, daily_data in daily_groups.items():
            df_daily = pd.DataFrame(daily_data)
            
            summary = {
                '日期': date,
                '数据点数': len(daily_data),
                '总电量(MWh)': df_daily['电量(MWh)'].sum(),
                '平均电量(MWh)': df_daily['电量(MWh)'].mean(),
                '最大电量(MWh)': df_daily['电量(MWh)'].max(),
                '最小电量(MWh)': df_daily['电量(MWh)'].min(),
                '平均功率(MW)': df_daily['功率(MW)'].mean(),
                '最大功率(MW)': df_daily['功率(MW)'].max(),
                '最小功率(MW)': df_daily['功率(MW)'].min()
            }
            
            self.daily_summaries.append(summary)
        
        print(f"✅ 每日统计完成: {len(self.daily_summaries)} 天")
        return True

    def save_results(self, output_file="中栋电厂7月1-14日真实数据15分钟出力曲线.xlsx"):
        """
        保存处理结果，仿照之前的格式
        """
        print(f"\n正在保存结果...")

        if not self.processed_15min_data:
            print("❌ 没有数据可保存")
            return False

        try:
            output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"

            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 1. 按要求格式 - 仿照之前的格式，但合并所有日期
                format_data = []

                # 按日期分组
                daily_groups = {}
                for data in self.processed_15min_data:
                    date = data['日期']
                    if date not in daily_groups:
                        daily_groups[date] = []
                    daily_groups[date].append(data)

                # 为每个日期创建格式化数据
                for date in sorted(daily_groups.keys()):
                    daily_data = daily_groups[date]
                    date_short = date[5:]  # 07-01格式

                    for data_point in daily_data:
                        format_data.append({
                            f'{date_short}实时出力_时间': data_point['时间'],
                            f'{date_short}实时出力_MWH': f"{data_point['电量(MWh)']:.4f}",
                            f'{date_short}实时出力_MW_时间': data_point['时间'],
                            f'{date_short}实时出力_MW': f"{data_point['功率(MW)']:.4f}",
                            '总功率=总电量/总时长': f"{data_point['功率(MW)']:.4f}",
                            '日期': date
                        })

                format_df = pd.DataFrame(format_data)
                format_df.to_excel(writer, sheet_name='按要求格式_合并', index=False)

                # 2. 15分钟出力曲线汇总
                curve_data = pd.DataFrame(self.processed_15min_data)
                curve_data.to_excel(writer, sheet_name='15分钟出力曲线汇总', index=False)

                # 3. 原始半小时数据
                original_data = pd.DataFrame(self.all_real_data)
                original_data.to_excel(writer, sheet_name='原始半小时数据', index=False)

                # 4. 每日统计汇总
                if self.daily_summaries:
                    summary_df = pd.DataFrame(self.daily_summaries)
                    summary_df.to_excel(writer, sheet_name='每日统计汇总', index=False)

                # 5. 按日期分表 - 仿照之前的格式
                for date in sorted(daily_groups.keys()):
                    daily_data = daily_groups[date]
                    date_short = date[5:]  # 07-01格式

                    # 按要求格式的单日数据
                    daily_format_data = []
                    for data_point in daily_data:
                        daily_format_data.append({
                            f'{date_short}实时出力_时间': data_point['时间'],
                            f'{date_short}实时出力_MWH': f"{data_point['电量(MWh)']:.4f}",
                            f'{date_short}实时出力_MW_时间': data_point['时间'],
                            f'{date_short}实时出力_MW': f"{data_point['功率(MW)']:.4f}",
                            '总功率=总电量/总时长': f"{data_point['功率(MW)']:.4f}"
                        })

                    daily_format_df = pd.DataFrame(daily_format_data)
                    sheet_name = f"按要求格式_{date_short}"
                    daily_format_df.to_excel(writer, sheet_name=sheet_name, index=False)

                    # 标准15分钟数据
                    daily_standard_data = []
                    for data_point in daily_data:
                        daily_standard_data.append({
                            '时间': data_point['时间'],
                            '电量(MWh)': data_point['电量(MWh)'],
                            '功率(MW)': data_point['功率(MW)']
                        })

                    daily_standard_df = pd.DataFrame(daily_standard_data)
                    sheet_name = f"15分钟出力曲线_{date_short}"
                    daily_standard_df.to_excel(writer, sheet_name=sheet_name, index=False)

                # 6. 整体统计
                all_data_df = pd.DataFrame(self.processed_15min_data)
                overall_stats = {
                    '项目': [
                        '处理日期数', '总数据点数', '总电量(MWh)', '平均功率(MW)',
                        '最大功率(MW)', '最小功率(MW)', '功率标准差(MW)'
                    ],
                    '数值': [
                        f"{len(daily_groups)} 天",
                        f"{len(self.processed_15min_data)} 个",
                        f"{all_data_df['电量(MWh)'].sum():.2f} MWh",
                        f"{all_data_df['功率(MW)'].mean():.2f} MW",
                        f"{all_data_df['功率(MW)'].max():.2f} MW",
                        f"{all_data_df['功率(MW)'].min():.2f} MW",
                        f"{all_data_df['功率(MW)'].std():.2f} MW"
                    ]
                }
                overall_df = pd.DataFrame(overall_stats)
                overall_df.to_excel(writer, sheet_name='整体统计', index=False)

            print(f"✅ 结果已保存到: {output_path}")

            # 显示保存的工作表信息
            print(f"\n📊 保存的工作表:")
            print(f"   按要求格式_合并: {len(format_data)} 条数据")
            print(f"   15分钟出力曲线汇总: {len(self.processed_15min_data)} 个时间点")
            print(f"   原始半小时数据: {len(self.all_real_data)} 条")
            print(f"   每日统计汇总: {len(self.daily_summaries)} 天")
            print(f"   按日期分表: {len(daily_groups)} 天的数据")

            # 显示前几个数据点验证
            print(f"\n🔍 前10个数据点验证 (按要求格式):")
            for i, data in enumerate(format_data[:10]):
                date_key = [k for k in data.keys() if k.endswith('实时出力_时间')][0]
                mwh_key = [k for k in data.keys() if k.endswith('实时出力_MWH')][0]
                mw_key = [k for k in data.keys() if k.endswith('实时出力_MW')][0]

                print(f"   {data[date_key]}\\t{data[mwh_key]}\\t{data[mw_key]}\\t{data['总功率=总电量/总时长']}")

            return True

        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False

def main():
    """
    主函数
    """
    print("中栋电厂真实现货结算表数据处理工具")
    print("仿照完整实时电能15分钟出力曲线格式，处理7月1-14号真实数据")
    print("="*70)

    processor = ZhongdongRealDataProcessor()

    # 1. 查找并处理所有文件
    if not processor.find_and_process_files():
        return

    # 2. 生成15分钟出力曲线
    if not processor.generate_15min_curves_for_all_days():
        return

    # 3. 生成每日统计汇总
    if not processor.generate_daily_summaries():
        return

    # 4. 保存结果
    if processor.save_results():
        print(f"\n🎉 处理完成！")
        print(f"📊 成功处理 {len(set([d['日期'] for d in processor.all_real_data]))} 天的现货结算表")
        print(f"⚡ 基于 {len(processor.all_real_data)} 条真实半小时数据")
        print(f"📈 生成了 {len(processor.processed_15min_data)} 个15分钟时间点")
        print(f"📁 结果已按要求格式保存，仿照之前的文件结构")
    else:
        print(f"\n❌ 处理失败！")

if __name__ == "__main__":
    main()
