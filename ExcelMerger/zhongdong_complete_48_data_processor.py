#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中栋电厂完整48条半小时数据处理工具
正确提取现货结算表中的完整48条实时电能数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

class ZhongdongComplete48DataProcessor:
    def __init__(self):
        """
        初始化完整48条数据处理器
        """
        self.folder_path = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/现货结算表/"
        self.all_complete_data = []
        self.processed_15min_data = []
        self.daily_summaries = []
    
    def extract_complete_daily_data(self, file_path, date_str):
        """
        从单个文件中提取完整的48条半小时实时电能数据
        """
        try:
            print(f"    正在提取完整数据...")
            
            # 读取整个文件
            df_full = pd.read_excel(file_path, header=None)
            
            # 查找实时电能起始行
            realtime_start_row = None
            for i, row in df_full.iterrows():
                row_str = ' '.join([str(cell) for cell in row.values if pd.notna(cell)])
                if '实时电能' in row_str and '中栋电厂' in row_str:
                    realtime_start_row = i
                    break
            
            if realtime_start_row is None:
                print(f"    ❌ 未找到实时电能起始行")
                return []
            
            print(f"    找到实时电能起始行: {realtime_start_row}")
            
            # 从起始行开始提取连续的48条数据
            daily_data = []
            current_row = realtime_start_row
            
            while current_row < len(df_full) and len(daily_data) < 48:
                row = df_full.iloc[current_row]
                row_values = [cell for cell in row.values if pd.notna(cell)]
                
                if len(row_values) < 4:  # 数据不完整，跳过
                    current_row += 1
                    continue
                
                # 检查是否有时间和电量数据
                has_time = False
                has_energy = False
                time_val = None
                energy_val = None
                
                # 查找时间（通常在第3或第4列）
                for i, cell in enumerate(row_values):
                    if 'datetime' in str(type(cell)) or ('2025' in str(cell) and ':' in str(cell)):
                        has_time = True
                        time_val = cell
                        break
                
                # 查找电量（通常在时间后面的列）
                for i, cell in enumerate(row_values):
                    try:
                        num_val = float(cell)
                        if 0 < num_val < 100:  # 合理的电量范围
                            has_energy = True
                            energy_val = num_val
                            break
                    except:
                        continue
                
                if has_time and has_energy:
                    # 处理时间格式
                    if isinstance(time_val, datetime):
                        time_obj = time_val
                    else:
                        time_obj = pd.to_datetime(time_val)
                    
                    power = energy_val / 0.5  # 半小时功率
                    
                    daily_data.append({
                        '日期': date_str,
                        '时间': time_obj.strftime('%H:%M:%S'),
                        '计量电量(MWh)': energy_val,
                        '功率(MW)': power,
                        '原始行号': current_row
                    })
                    
                    print(f"      {time_obj.strftime('%H:%M:%S')}: {energy_val} MWh → {power:.4f} MW")
                
                current_row += 1
                
                # 防止无限循环
                if current_row > realtime_start_row + 100:
                    break
            
            print(f"    ✅ 提取到 {len(daily_data)} 条完整数据")
            return daily_data
            
        except Exception as e:
            print(f"    ❌ 处理文件失败: {e}")
            return []
    
    def process_all_files(self):
        """
        处理7月1-14号的所有文件
        """
        print("正在处理7月1-14号现货结算表文件，提取完整48条数据...")
        
        success_count = 0
        
        for day in range(1, 15):  # 7月1号到7月14号
            date_str = f"2025-07-{day:02d}"
            file_name = f"中栋电厂{date_str}-day_sbs_gen_pub_detail_25.xlsx"
            file_path = os.path.join(self.folder_path, file_name)
            
            if os.path.exists(file_path):
                print(f"  处理: {date_str}")
                daily_data = self.extract_complete_daily_data(file_path, date_str)
                
                if daily_data and len(daily_data) >= 40:  # 至少要有40条数据才算成功
                    self.all_complete_data.extend(daily_data)
                    success_count += 1
                    print(f"    ✅ 成功提取 {len(daily_data)} 条数据")
                else:
                    print(f"    ❌ 数据不完整，只有 {len(daily_data)} 条")
            else:
                print(f"  ❌ 文件不存在: {file_name}")
        
        print(f"\n✅ 处理完成: {success_count}/14 个文件")
        print(f"   总数据条数: {len(self.all_complete_data)}")
        
        if len(self.all_complete_data) > 0:
            print(f"   电量范围: {min([d['计量电量(MWh)'] for d in self.all_complete_data]):.4f} - {max([d['计量电量(MWh)'] for d in self.all_complete_data]):.4f} MWh")
            print(f"   功率范围: {min([d['功率(MW)'] for d in self.all_complete_data]):.4f} - {max([d['功率(MW)'] for d in self.all_complete_data]):.4f} MW")
        
        return success_count > 0
    
    def generate_15min_curves(self):
        """
        基于完整48条数据生成15分钟出力曲线
        """
        print("\n正在基于完整48条数据生成15分钟出力曲线...")
        
        if not self.all_complete_data:
            print("❌ 没有数据可处理")
            return False
        
        # 按日期分组
        daily_groups = {}
        for data in self.all_complete_data:
            date = data['日期']
            if date not in daily_groups:
                daily_groups[date] = []
            daily_groups[date].append(data)
        
        # 为每一天生成15分钟数据
        for date, daily_data in daily_groups.items():
            print(f"  处理日期: {date} ({len(daily_data)}条半小时数据)")
            
            # 排序数据
            daily_data.sort(key=lambda x: x['时间'])
            
            # 为每个半小时数据生成两个15分钟时间点
            for data_point in daily_data:
                time_str = data_point['时间']
                energy = data_point['计量电量(MWh)']
                power = data_point['功率(MW)']
                
                # 15分钟数据
                quarter_energy = energy / 2
                quarter_power = quarter_energy / 0.25
                
                # 生成时间点
                hour, minute, second = map(int, time_str.split(':'))
                
                if minute == 30:
                    times = [f"{hour:02d}:15", f"{hour:02d}:30"]
                elif minute == 0:
                    if hour == 0:
                        times = ["23:45", "24:00"]
                    else:
                        times = [f"{hour-1:02d}:45", f"{hour:02d}:00"]
                
                for time_point in times:
                    self.processed_15min_data.append({
                        '日期': date,
                        '时间': time_point,
                        '电量(MWh)': quarter_energy,
                        '功率(MW)': quarter_power,
                        '原始时间': time_str,
                        '原始电量': energy,
                        '原始功率': power
                    })
        
        print(f"✅ 15分钟数据生成完成: {len(self.processed_15min_data)} 个时间点")
        
        # 显示统计信息
        if self.processed_15min_data:
            all_energies = [d['电量(MWh)'] for d in self.processed_15min_data]
            all_powers = [d['功率(MW)'] for d in self.processed_15min_data]
            
            print(f"   总电量: {sum(all_energies):.2f} MWh")
            print(f"   平均功率: {sum(all_powers)/len(all_powers):.2f} MW")
            print(f"   功率范围: {min(all_powers):.2f} - {max(all_powers):.2f} MW")
        
        return True
    
    def generate_daily_summaries(self):
        """
        生成每日统计汇总
        """
        print("\n正在生成每日统计汇总...")
        
        # 按日期分组统计
        daily_groups = {}
        for data in self.processed_15min_data:
            date = data['日期']
            if date not in daily_groups:
                daily_groups[date] = []
            daily_groups[date].append(data)
        
        for date, daily_data in daily_groups.items():
            energies = [d['电量(MWh)'] for d in daily_data]
            powers = [d['功率(MW)'] for d in daily_data]
            
            summary = {
                '日期': date,
                '数据点数': len(daily_data),
                '总电量(MWh)': sum(energies),
                '平均电量(MWh)': sum(energies) / len(energies),
                '最大电量(MWh)': max(energies),
                '最小电量(MWh)': min(energies),
                '平均功率(MW)': sum(powers) / len(powers),
                '最大功率(MW)': max(powers),
                '最小功率(MW)': min(powers)
            }
            
            self.daily_summaries.append(summary)
        
        print(f"✅ 每日统计完成: {len(self.daily_summaries)} 天")
        return True

    def save_results(self, output_file="中栋电厂7月1-14日完整48条数据15分钟出力曲线.xlsx"):
        """
        保存处理结果，仿照之前的格式
        """
        print(f"\n正在保存结果...")

        if not self.processed_15min_data:
            print("❌ 没有数据可保存")
            return False

        try:
            output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"

            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 1. 按要求格式 - 仿照之前的格式，合并所有日期
                format_data = []

                # 按日期分组
                daily_groups = {}
                for data in self.processed_15min_data:
                    date = data['日期']
                    if date not in daily_groups:
                        daily_groups[date] = []
                    daily_groups[date].append(data)

                # 创建连续的格式化数据 - 从2025-07-01 00:15开始往下排
                for date in sorted(daily_groups.keys()):
                    daily_data = daily_groups[date]
                    # 按时间排序
                    daily_data.sort(key=lambda x: x['时间'])

                    for data_point in daily_data:
                        format_data.append({
                            '日期时间': f"{date} {data_point['时间']}",
                            '实时出力_MW': f"{data_point['功率(MW)']:.4f}",
                            '总功率=总电量/总时长': "",  # 电量列留空
                            '日期': date,
                            '时间': data_point['时间']
                        })

                # 按时间顺序排序
                format_df = pd.DataFrame(format_data)
                format_df['排序键'] = pd.to_datetime(format_df['日期'] + ' ' + format_df['时间'])
                format_df = format_df.sort_values('排序键').drop('排序键', axis=1).reset_index(drop=True)

                # 创建最终格式：日期时间、功率、电量(空)
                final_format = []
                for _, row in format_df.iterrows():
                    final_format.append({
                        '日期时间': row['日期时间'],
                        '实时出力_MW': row['实时出力_MW'],
                        '总功率=总电量/总时长': row['总功率=总电量/总时长']
                    })

                final_df = pd.DataFrame(final_format)
                final_df.to_excel(writer, sheet_name='连续时间出力曲线', index=False)

                # 也保存原来的格式作为备份
                format_df.to_excel(writer, sheet_name='按要求格式_合并', index=False)

                # 2. 15分钟出力曲线汇总
                curve_data = pd.DataFrame(self.processed_15min_data)
                curve_data.to_excel(writer, sheet_name='15分钟出力曲线汇总', index=False)

                # 3. 原始完整半小时数据
                original_data = pd.DataFrame(self.all_complete_data)
                original_data.to_excel(writer, sheet_name='原始完整半小时数据', index=False)

                # 4. 每日统计汇总
                if self.daily_summaries:
                    summary_df = pd.DataFrame(self.daily_summaries)
                    summary_df.to_excel(writer, sheet_name='每日统计汇总', index=False)

                # 5. 按日期分表 - 仿照之前的格式
                for date in sorted(daily_groups.keys()):
                    daily_data = daily_groups[date]
                    date_short = date[5:]  # 07-01格式

                    # 按要求格式的单日数据 - 新格式：时间、功率、电量(空)
                    daily_format_data = []
                    for data_point in daily_data:
                        daily_format_data.append({
                            f'{date_short}实时出力_时间': data_point['时间'],
                            f'{date_short}实时出力_MW': f"{data_point['功率(MW)']:.4f}",
                            '总功率=总电量/总时长': ""  # 电量列留空
                        })

                    daily_format_df = pd.DataFrame(daily_format_data)
                    sheet_name = f"按要求格式_{date_short}"
                    daily_format_df.to_excel(writer, sheet_name=sheet_name, index=False)

                    # 标准15分钟数据
                    daily_standard_data = []
                    for data_point in daily_data:
                        daily_standard_data.append({
                            '时间': data_point['时间'],
                            '电量(MWh)': data_point['电量(MWh)'],
                            '功率(MW)': data_point['功率(MW)']
                        })

                    daily_standard_df = pd.DataFrame(daily_standard_data)
                    sheet_name = f"15分钟出力曲线_{date_short}"
                    daily_standard_df.to_excel(writer, sheet_name=sheet_name, index=False)

                # 6. 整体统计
                all_data_df = pd.DataFrame(self.processed_15min_data)
                overall_stats = {
                    '项目': [
                        '处理日期数', '原始半小时数据', '生成15分钟数据', '总电量(MWh)',
                        '平均功率(MW)', '最大功率(MW)', '最小功率(MW)', '功率标准差(MW)'
                    ],
                    '数值': [
                        f"{len(daily_groups)} 天",
                        f"{len(self.all_complete_data)} 条",
                        f"{len(self.processed_15min_data)} 个",
                        f"{all_data_df['电量(MWh)'].sum():.2f} MWh",
                        f"{all_data_df['功率(MW)'].mean():.2f} MW",
                        f"{all_data_df['功率(MW)'].max():.2f} MW",
                        f"{all_data_df['功率(MW)'].min():.2f} MW",
                        f"{all_data_df['功率(MW)'].std():.2f} MW"
                    ]
                }
                overall_df = pd.DataFrame(overall_stats)
                overall_df.to_excel(writer, sheet_name='整体统计', index=False)

            print(f"✅ 结果已保存到: {output_path}")

            # 显示保存的工作表信息
            print(f"\n📊 保存的工作表:")
            print(f"   按要求格式_合并: {len(format_data)} 条数据")
            print(f"   15分钟出力曲线汇总: {len(self.processed_15min_data)} 个时间点")
            print(f"   原始完整半小时数据: {len(self.all_complete_data)} 条")
            print(f"   每日统计汇总: {len(self.daily_summaries)} 天")
            print(f"   按日期分表: {len(daily_groups)} 天的数据")

            return True

        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False

def main():
    """
    主函数
    """
    print("中栋电厂完整48条半小时数据处理工具")
    print("正确提取现货结算表中的完整48条实时电能数据")
    print("="*70)

    processor = ZhongdongComplete48DataProcessor()

    # 1. 处理所有文件，提取完整48条数据
    if not processor.process_all_files():
        return

    # 2. 生成15分钟出力曲线
    if not processor.generate_15min_curves():
        return

    # 3. 生成每日统计汇总
    if not processor.generate_daily_summaries():
        return

    # 4. 保存结果
    if processor.save_results():
        print(f"\n🎉 处理完成！")
        print(f"📊 成功提取 {len(processor.all_complete_data)} 条完整半小时数据")
        print(f"⚡ 生成了 {len(processor.processed_15min_data)} 个15分钟时间点")
        print(f"📁 结果已按要求格式保存")
        print(f"🎯 这次是基于真正完整的48条半小时数据！")
    else:
        print(f"\n❌ 处理失败！")

if __name__ == "__main__":
    main()
