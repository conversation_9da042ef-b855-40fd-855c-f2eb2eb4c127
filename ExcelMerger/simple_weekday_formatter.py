#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版：周天天气数据格式化工具
专门确保具体日期和用电量能正确显示在对应的周天列中
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from collections import defaultdict
from openpyxl import load_workbook

class SimpleWeekdayFormatter:
    def __init__(self):
        self.source_data = None
        self.target_workbook = None
        self.grouped_data = {}
        
        # 中文周天映射
        self.weekday_names = {
            0: '星期一',
            1: '星期二', 
            2: '星期三',
            3: '星期四',
            4: '星期五',
            5: '星期六',
            6: '星期日'
        }
    
    def load_data(self, source_file, target_file):
        """加载数据"""
        try:
            print(f"正在读取源数据文件: {os.path.basename(source_file)}")
            self.source_data = pd.read_excel(source_file, sheet_name='完整数据')
            print(f"源数据读取成功，共 {len(self.source_data)} 行数据")
            
            print(f"正在读取目标文件: {os.path.basename(target_file)}")
            self.target_workbook = load_workbook(target_file)
            print(f"发现工作表: {self.target_workbook.sheetnames}")
            
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def process_region_data(self, region):
        """处理单个地区的数据"""
        region_data = self.source_data[self.source_data['地区'] == region]
        if region_data.empty:
            return {}
        
        result = {}
        
        # 按天气类型分组
        for weather in region_data['天气'].unique():
            weather_data = region_data[region_data['天气'] == weather]
            
            # 按周天收集数据
            weekday_info = {}
            for _, row in weather_data.iterrows():
                weekday = row['周天']
                date = row['日期']
                power = row['总电量(kWh)']
                
                if weekday not in weekday_info:
                    weekday_info[weekday] = {
                        '日期列表': [],
                        '用电量列表': []
                    }
                
                weekday_info[weekday]['日期列表'].append(date)
                weekday_info[weekday]['用电量列表'].append(power)
            
            # 处理每个周天的数据
            processed_weekdays = {}
            for weekday, info in weekday_info.items():
                # 按日期排序
                combined = list(zip(info['日期列表'], info['用电量列表']))
                combined.sort(key=lambda x: x[0])
                
                dates, powers = zip(*combined) if combined else ([], [])
                
                processed_weekdays[weekday] = {
                    '总用电量': sum(powers),
                    '具体日期': dates[0] if dates else None,  # 最早日期
                    '具体用电量': powers[0] if len(powers) == 1 else sum(powers),  # 单条记录用原值，多条用总和
                    '所有记录': combined
                }
            
            result[weather] = {
                '周天数据': processed_weekdays,
                '代表日期': weather_data['日期'].iloc[0]
            }
        
        return result
    
    def add_data_to_worksheet(self, sheet_name, region_data):
        """将数据添加到工作表"""
        try:
            if sheet_name not in self.target_workbook.sheetnames:
                print(f"警告: 工作表 {sheet_name} 不存在")
                return False
            
            worksheet = self.target_workbook[sheet_name]
            last_row = worksheet.max_row
            current_row = last_row + 2
            
            # 添加标题
            worksheet.cell(row=current_row, column=1, value="=== 同一周天相同天气数据汇总 ===")
            current_row += 2
            
            weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
            
            # 为每种天气类型添加数据
            for weather_type, weather_info in region_data.items():
                weekday_data = weather_info['周天数据']
                
                print(f"  添加天气类型: {weather_type}")
                
                # 1. 日期行
                worksheet.cell(row=current_row, column=1, value="日期")
                worksheet.cell(row=current_row, column=2, value=weather_info['代表日期'].strftime('%Y-%m-%d'))
                current_row += 1
                
                # 2. 日期类型行（周天标题）
                worksheet.cell(row=current_row, column=1, value="日期类型")
                for i, weekday in enumerate(weekdays, 2):
                    worksheet.cell(row=current_row, column=i, value=weekday)
                current_row += 1
                
                # 3. 天气行
                worksheet.cell(row=current_row, column=1, value="天气")
                for i, weekday in enumerate(weekdays, 2):
                    worksheet.cell(row=current_row, column=i, value=weather_type)
                current_row += 1
                
                # 4. 用电量行
                worksheet.cell(row=current_row, column=1, value="用电量")
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weekday_data:
                        power = weekday_data[weekday]['总用电量']
                        worksheet.cell(row=current_row, column=i, value=round(power, 2))
                        print(f"    {weekday}: 总用电量 {power:.2f}")
                current_row += 1
                
                # 5. 具体日期行
                worksheet.cell(row=current_row, column=1, value="具体日期")
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weekday_data and weekday_data[weekday]['具体日期']:
                        date = weekday_data[weekday]['具体日期']
                        date_str = date.strftime('%Y-%m-%d')
                        worksheet.cell(row=current_row, column=i, value=date_str)
                        print(f"    {weekday}: 具体日期 {date_str}")
                current_row += 1
                
                # 6. 具体用电量行
                worksheet.cell(row=current_row, column=1, value="具体用电量")
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weekday_data:
                        power = weekday_data[weekday]['具体用电量']
                        worksheet.cell(row=current_row, column=i, value=round(power, 2))
                        print(f"    {weekday}: 具体用电量 {power:.2f}")
                current_row += 1
                
                current_row += 1  # 空行分隔
            
            print(f"成功添加 {len(region_data)} 种天气类型的数据到工作表 {sheet_name}")
            return True
            
        except Exception as e:
            print(f"添加数据到工作表 {sheet_name} 失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def process_all_regions(self):
        """处理所有地区"""
        regions = ['杭州', '金华', '宁波', '台州', '衢州']
        
        for region in regions:
            print(f"\n处理地区: {region}")
            
            region_data = self.process_region_data(region)
            if region_data:
                self.add_data_to_worksheet(region, region_data)
            else:
                print(f"警告: 未找到地区 {region} 的数据")
    
    def save_result(self, output_file):
        """保存结果"""
        try:
            self.target_workbook.save(output_file)
            print(f"\n✅ 处理完成！结果已保存到: {output_file}")
            return True
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False
    
    def process(self, source_file, target_file, output_file):
        """完整处理流程"""
        print(f"简化版：周天天气数据格式化工具")
        print(f"=" * 80)
        
        if not self.load_data(source_file, target_file):
            return False
        
        self.process_all_regions()
        
        if not self.save_result(output_file):
            return False
        
        return True

def main():
    """主函数"""
    source_file = "城市售电表（含气温+日期类型+天气）_周天天气分组.xlsx"
    target_file = "/Users/<USER>/Desktop/副本城市售电表（含气温+日期类型+天气）(1).xlsx"
    output_file = "简化版_副本城市售电表_含周天天气汇总.xlsx"
    
    if not os.path.exists(source_file):
        print(f"错误: 源数据文件不存在 - {source_file}")
        return
    
    if not os.path.exists(target_file):
        print(f"错误: 目标文件不存在 - {target_file}")
        return
    
    formatter = SimpleWeekdayFormatter()
    success = formatter.process(source_file, target_file, output_file)
    
    if success:
        print(f"\n🎉 任务完成！")
        print(f"已成功将同一周天相同天气的数据（含具体日期和用电量）添加到各个地区分表中")
        print(f"文件保存位置: {output_file}")
        print(f"\n格式说明:")
        print(f"- 日期: 该天气类型的代表日期")
        print(f"- 日期类型: 星期一到星期日的标题行")
        print(f"- 天气: 该行所有列都显示相同的天气类型")
        print(f"- 用电量: 每个周天该天气类型的总用电量")
        print(f"- 具体日期: 每个周天该天气类型的具体日期")
        print(f"- 具体用电量: 每个周天该天气类型的具体用电量")

if __name__ == "__main__":
    main()
