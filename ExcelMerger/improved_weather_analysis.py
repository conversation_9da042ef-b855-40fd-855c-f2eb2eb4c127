#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的天气与用电量相关性分析
解决数据质量问题，使用更合理的分析方法
"""

import pandas as pd
import numpy as np
from scipy.stats import spearmanr, pearsonr, kruskal
import matplotlib.pyplot as plt
import seaborn as sns
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ImprovedWeatherAnalysis:
    def __init__(self, excel_file):
        """
        初始化改进的天气分析器
        """
        self.excel_file = excel_file
        self.df = None
        self.clean_df = None
        self.load_and_clean_data()
    
    def load_and_clean_data(self):
        """加载并清洗数据"""
        try:
            self.df = pd.read_excel(self.excel_file)
            print(f"原始数据加载成功，共 {len(self.df)} 行数据")
            
            # 数据清洗
            print(f"\n=== 数据清洗过程 ===")
            
            # 1. 移除用电量为0的记录
            zero_power = len(self.df[self.df['总电量(kWh)'] == 0])
            print(f"用电量为0的记录: {zero_power} 条 ({zero_power/len(self.df)*100:.1f}%)")
            
            # 2. 移除用电量为负的记录（如果有）
            negative_power = len(self.df[self.df['总电量(kWh)'] < 0])
            if negative_power > 0:
                print(f"用电量为负的记录: {negative_power} 条")
            
            # 3. 清洗数据
            self.clean_df = self.df[
                (self.df['总电量(kWh)'] > 0) &  # 移除0和负值
                (self.df['总电量(kWh)'] < 100000) &  # 移除异常高值
                (self.df['天气'].notna())  # 移除天气信息缺失的记录
            ].copy()
            
            print(f"清洗后数据: {len(self.clean_df)} 行数据")
            print(f"数据保留率: {len(self.clean_df)/len(self.df)*100:.1f}%")
            
            # 显示清洗后的基本统计
            print(f"\n清洗后用电量统计:")
            print(f"  平均值: {self.clean_df['总电量(kWh)'].mean():.2f} kWh")
            print(f"  中位数: {self.clean_df['总电量(kWh)'].median():.2f} kWh")
            print(f"  标准差: {self.clean_df['总电量(kWh)'].std():.2f} kWh")
            print(f"  最小值: {self.clean_df['总电量(kWh)'].min():.2f} kWh")
            print(f"  最大值: {self.clean_df['总电量(kWh)'].max():.2f} kWh")
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def analyze_weather_distribution(self):
        """分析天气分布和用电量分布"""
        print(f"\n=== 天气状况与用电量分布分析 ===")
        
        # 天气状况统计
        weather_stats = self.clean_df.groupby('天气')['总电量(kWh)'].agg([
            'count', 'mean', 'median', 'std', 'min', 'max'
        ]).round(2)
        
        print(f"\n各天气状况用电量统计:")
        print(weather_stats)
        
        # 计算各天气状况的用电量分位数
        print(f"\n各天气状况用电量分位数分析:")
        for weather in self.clean_df['天气'].unique():
            weather_data = self.clean_df[self.clean_df['天气'] == weather]['总电量(kWh)']
            q25 = weather_data.quantile(0.25)
            q75 = weather_data.quantile(0.75)
            print(f"  {weather}: Q1={q25:.0f}, Q3={q75:.0f}, IQR={q75-q25:.0f}")
        
        return weather_stats
    
    def kruskal_wallis_test(self):
        """
        使用Kruskal-Wallis检验分析天气状况对用电量的影响
        这是一个非参数检验，适合比较多个组的分布差异
        """
        print(f"\n=== Kruskal-Wallis检验 ===")
        print("检验不同天气状况下用电量分布是否存在显著差异")
        
        # 准备各天气状况的用电量数据
        weather_groups = []
        weather_names = []
        
        for weather in self.clean_df['天气'].unique():
            weather_data = self.clean_df[self.clean_df['天气'] == weather]['总电量(kWh)']
            if len(weather_data) >= 10:  # 至少10个样本
                weather_groups.append(weather_data.values)
                weather_names.append(weather)
        
        if len(weather_groups) >= 2:
            # 执行Kruskal-Wallis检验
            statistic, p_value = kruskal(*weather_groups)
            
            print(f"\nKruskal-Wallis检验结果:")
            print(f"  检验统计量: {statistic:.4f}")
            print(f"  p值: {p_value:.6f}")
            print(f"  显著性: {'显著 (p < 0.05)' if p_value < 0.05 else '不显著 (p >= 0.05)'}")
            
            if p_value < 0.05:
                print(f"  结论: 不同天气状况下的用电量分布存在显著差异")
            else:
                print(f"  结论: 不同天气状况下的用电量分布无显著差异")
            
            return statistic, p_value, weather_names
        else:
            print("天气状况类别不足，无法进行检验")
            return None, None, None
    
    def improved_correlation_analysis(self):
        """
        改进的相关性分析
        使用更合理的天气编码和多种相关性方法
        """
        print(f"\n=== 改进的相关性分析 ===")
        
        # 基于物理意义的天气编码
        # 考虑对空调/照明用电的影响
        weather_encoding_v2 = {
            '晴': 3,      # 晴天：高温，空调用电高
            '多云': 2,    # 多云：中等温度，中等用电
            '阴': 1,      # 阴天：较低温度，但需要照明
            '雨': 1,      # 雨天：较低温度，湿度高
        }
        
        # 应用新的编码
        self.clean_df['天气编码_v2'] = self.clean_df['天气'].map(weather_encoding_v2)
        
        print(f"改进的天气编码（基于温度影响）:")
        for weather, code in weather_encoding_v2.items():
            count = len(self.clean_df[self.clean_df['天气'] == weather])
            if count > 0:
                avg_power = self.clean_df[self.clean_df['天气'] == weather]['总电量(kWh)'].mean()
                print(f"  {weather} (编码{code}): {count}条记录, 平均用电{avg_power:.0f}kWh")
        
        # 计算相关性
        clean_data = self.clean_df[['天气编码_v2', '总电量(kWh)']].dropna()
        
        if len(clean_data) > 0:
            # Spearman相关性
            spearman_corr, spearman_p = spearmanr(clean_data['天气编码_v2'], clean_data['总电量(kWh)'])
            
            # Pearson相关性
            pearson_corr, pearson_p = pearsonr(clean_data['天气编码_v2'], clean_data['总电量(kWh)'])
            
            print(f"\n相关性分析结果:")
            print(f"  Spearman相关系数: {spearman_corr:.4f} (p={spearman_p:.6f})")
            print(f"  Pearson相关系数: {pearson_corr:.4f} (p={pearson_p:.6f})")
            
            # 解释相关性
            if abs(spearman_corr) >= 0.3:
                strength = "中等"
            elif abs(spearman_corr) >= 0.1:
                strength = "弱"
            else:
                strength = "极弱"
            
            direction = "正" if spearman_corr > 0 else "负"
            significance = "显著" if spearman_p < 0.05 else "不显著"
            
            print(f"  相关性强度: {strength}{direction}相关")
            print(f"  统计显著性: {significance}")
            
            return {
                'spearman_corr': spearman_corr,
                'spearman_p': spearman_p,
                'pearson_corr': pearson_corr,
                'pearson_p': pearson_p,
                'sample_size': len(clean_data)
            }
        else:
            print("没有有效数据进行相关性分析")
            return None
    
    def regional_analysis(self):
        """按地区分析天气影响"""
        print(f"\n=== 各地区天气影响分析 ===")
        
        regions = self.clean_df['地区'].unique()
        regional_results = {}
        
        for region in regions:
            region_data = self.clean_df[self.clean_df['地区'] == region]
            
            if len(region_data) >= 20:  # 至少20个样本
                # 天气编码
                weather_encoding = {'晴': 3, '多云': 2, '阴': 1, '雨': 1}
                region_data = region_data.copy()
                region_data['天气编码'] = region_data['天气'].map(weather_encoding)
                
                clean_region = region_data[['天气编码', '总电量(kWh)']].dropna()
                
                if len(clean_region) >= 10:
                    corr, p_val = spearmanr(clean_region['天气编码'], clean_region['总电量(kWh)'])
                    
                    regional_results[region] = {
                        'correlation': corr,
                        'p_value': p_val,
                        'sample_size': len(clean_region),
                        'avg_power': region_data['总电量(kWh)'].mean()
                    }
                    
                    significance = "显著" if p_val < 0.05 else "不显著"
                    print(f"  {region}: ρ={corr:.4f}, p={p_val:.4f} ({significance}), 样本={len(clean_region)}, 平均用电={region_data['总电量(kWh)'].mean():.0f}kWh")
        
        return regional_results
    
    def time_analysis(self):
        """时间序列分析"""
        print(f"\n=== 时间序列分析 ===")
        
        # 按日期分组分析
        self.clean_df['日期'] = pd.to_datetime(self.clean_df['时间']).dt.date
        
        daily_stats = self.clean_df.groupby(['日期', '天气']).agg({
            '总电量(kWh)': ['count', 'mean', 'sum']
        }).round(2)
        
        daily_stats.columns = ['记录数', '平均用电量', '总用电量']
        daily_stats = daily_stats.reset_index()
        
        print(f"\n各日期天气与用电量统计:")
        for _, row in daily_stats.iterrows():
            weekday = pd.to_datetime(row['日期']).strftime('%A')
            if row['记录数'] >= 5:  # 只显示有足够样本的数据
                print(f"  {row['日期']} ({weekday}): {row['天气']}, 记录数{row['记录数']}, 平均{row['平均用电量']}kWh")
        
        return daily_stats
    
    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        print(f"\n" + "="*80)
        print(f"改进的天气与用电量相关性分析报告")
        print(f"="*80)
        
        if self.clean_df is None or len(self.clean_df) == 0:
            print("没有有效数据进行分析")
            return
        
        print(f"\n数据概况:")
        print(f"  分析文件: {os.path.basename(self.excel_file)}")
        print(f"  原始数据: {len(self.df)} 条记录")
        print(f"  有效数据: {len(self.clean_df)} 条记录")
        print(f"  数据质量: {len(self.clean_df)/len(self.df)*100:.1f}%")
        
        # 执行各项分析
        weather_stats = self.analyze_weather_distribution()
        kw_stat, kw_p, weather_names = self.kruskal_wallis_test()
        corr_results = self.improved_correlation_analysis()
        regional_results = self.regional_analysis()
        daily_stats = self.time_analysis()
        
        # 综合结论
        print(f"\n" + "="*60)
        print(f"综合分析结论")
        print(f"="*60)
        
        if kw_p is not None and kw_p < 0.05:
            print(f"✅ Kruskal-Wallis检验显示不同天气状况的用电量分布存在显著差异")
        else:
            print(f"❌ Kruskal-Wallis检验显示不同天气状况的用电量分布无显著差异")
        
        if corr_results and corr_results['spearman_p'] < 0.05:
            print(f"✅ Spearman相关性分析显示天气与用电量存在显著相关性")
            print(f"   相关系数: {corr_results['spearman_corr']:.4f}")
        else:
            print(f"❌ 相关性分析未发现显著的天气-用电量关系")
        
        # 数据质量评估
        zero_rate = (len(self.df) - len(self.clean_df)) / len(self.df) * 100
        if zero_rate > 50:
            print(f"⚠️  数据质量问题: {zero_rate:.1f}%的数据被过滤，可能影响分析结果")
        
        return {
            'weather_stats': weather_stats,
            'kruskal_results': (kw_stat, kw_p),
            'correlation_results': corr_results,
            'regional_results': regional_results,
            'daily_stats': daily_stats
        }

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        excel_file = sys.argv[1]
        if "工作日" in excel_file:
            file_type = "工作日"
        elif "非工作日" in excel_file:
            file_type = "非工作日"
        else:
            file_type = "数据"
    else:
        excel_file = "/Users/<USER>/Desktop/合并结果_7月用电量信息含天气(2)_工作日.xlsx"
        file_type = "工作日"
    
    print(f"改进的{file_type}天气与用电量相关性分析")
    print("="*60)
    
    if not os.path.exists(excel_file):
        print(f"错误: 文件不存在 - {excel_file}")
        return
    
    try:
        analyzer = ImprovedWeatherAnalysis(excel_file)
        results = analyzer.generate_comprehensive_report()
        
        print(f"\n✅ 改进分析完成！")
        print(f"📊 已完成{file_type}数据的深度天气影响分析")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
