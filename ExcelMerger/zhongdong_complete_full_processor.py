#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中栋电厂完整数据处理工具 - 完整实现版本
从现货结算表中提取完整48条半小时实时电能数据，生成15分钟出力曲线
支持7月1-14日所有文件的批量处理，完整功能实现
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import glob
import re
import json
from pathlib import Path
import logging
import traceback

class ZhongdongCompleteFullProcessor:
    def __init__(self):
        """
        初始化中栋电厂完整数据处理器
        """
        self.folder_path = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/现货结算表/"
        
        # 数据存储
        self.all_raw_data = []  # 原始48条半小时数据
        self.all_15min_data = []  # 生成的15分钟数据
        self.daily_summaries = []  # 每日统计汇总
        self.processing_log = []  # 处理日志
        self.error_log = []  # 错误日志
        
        # 配置参数
        self.target_days = list(range(1, 15))  # 7月1-14日
        self.expected_half_hour_points = 48  # 每天期望的半小时数据点
        self.expected_15min_points = 96  # 每天期望的15分钟数据点
        
        # 设置日志
        self.setup_logging()
        
        # 15分钟时间点列表
        self.generate_15min_time_points()
        
        print("中栋电厂完整数据处理器初始化完成")
        print(f"目标处理天数: {len(self.target_days)} 天")
        print(f"数据源路径: {self.folder_path}")
    
    def setup_logging(self):
        """
        设置日志系统
        """
        log_file = "/Users/<USER>/RiderProjects/Solution3/zhongdong_processing.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("中栋电厂数据处理器启动")
    
    def generate_15min_time_points(self):
        """
        生成完整的96个15分钟时间点
        """
        self.time_points_15min = []
        
        for hour in range(24):
            self.time_points_15min.extend([
                f"{hour:02d}:15",
                f"{hour:02d}:30",
                f"{hour:02d}:45"
            ])
            if hour < 23:
                self.time_points_15min.append(f"{hour+1:02d}:00")
        
        # 添加24:00作为结束点
        self.time_points_15min.append("24:00")
        
        self.logger.info(f"生成15分钟时间点: {len(self.time_points_15min)} 个")
    
    def find_settlement_files(self):
        """
        查找所有现货结算表文件
        """
        self.logger.info("开始查找现货结算表文件...")
        
        found_files = []
        missing_files = []
        
        for day in self.target_days:
            date_str = f"2025-07-{day:02d}"
            file_name = f"中栋电厂{date_str}-day_sbs_gen_pub_detail_25.xlsx"
            file_path = os.path.join(self.folder_path, file_name)
            
            if os.path.exists(file_path):
                file_info = {
                    'date': date_str,
                    'day': day,
                    'file_path': file_path,
                    'file_name': file_name,
                    'file_size': os.path.getsize(file_path)
                }
                found_files.append(file_info)
                self.logger.info(f"✅ 找到文件: {file_name} ({file_info['file_size']} bytes)")
            else:
                missing_files.append(file_name)
                self.logger.warning(f"❌ 缺失文件: {file_name}")
        
        self.logger.info(f"文件查找完成: 找到 {len(found_files)}/{len(self.target_days)} 个文件")
        
        if missing_files:
            self.logger.warning(f"缺失文件列表: {missing_files}")
        
        return found_files, missing_files
    
    def extract_complete_daily_data(self, file_info):
        """
        从单个文件中提取完整的48条半小时实时电能数据
        """
        file_path = file_info['file_path']
        date_str = file_info['date']
        
        self.logger.info(f"开始处理文件: {file_info['file_name']}")
        
        try:
            # 读取整个Excel文件
            df_full = pd.read_excel(file_path, header=None)
            self.logger.info(f"文件读取成功，形状: {df_full.shape}")
            
            # 查找实时电能起始行
            realtime_start_rows = []
            for i, row in df_full.iterrows():
                row_str = ' '.join([str(cell) for cell in row.values if pd.notna(cell)])
                if '实时电能' in row_str and '中栋电厂' in row_str:
                    realtime_start_rows.append(i)
            
            if not realtime_start_rows:
                self.logger.error(f"未找到实时电能起始行")
                return []
            
            # 使用第一个找到的起始行
            start_row = realtime_start_rows[0]
            self.logger.info(f"找到实时电能起始行: {start_row}")
            
            # 从起始行开始提取数据
            daily_data = []
            current_row = start_row
            max_search_rows = 200  # 限制搜索范围
            
            while current_row < len(df_full) and len(daily_data) < self.expected_half_hour_points and (current_row - start_row) < max_search_rows:
                row = df_full.iloc[current_row]
                row_values = [cell for cell in row.values if pd.notna(cell)]
                
                if len(row_values) < 4:
                    current_row += 1
                    continue
                
                # 查找时间和电量数据
                time_val = None
                energy_val = None
                
                # 查找时间数据
                for cell in row_values:
                    if self.is_time_data(cell):
                        time_val = cell
                        break
                
                # 查找电量数据
                for cell in row_values:
                    if self.is_energy_data(cell):
                        energy_val = cell
                        break
                
                if time_val is not None and energy_val is not None:
                    try:
                        # 处理时间格式
                        if isinstance(time_val, datetime):
                            time_obj = time_val
                        else:
                            time_obj = pd.to_datetime(str(time_val))
                        
                        # 处理电量数据
                        energy_float = float(energy_val)
                        
                        # 计算功率
                        power = energy_float / 0.5  # 半小时功率
                        
                        # 存储数据
                        data_point = {
                            '日期': date_str,
                            '时间': time_obj.strftime('%H:%M:%S'),
                            '计量电量(MWh)': energy_float,
                            '功率(MW)': power,
                            '原始行号': current_row,
                            '数据来源': 'extracted'
                        }
                        
                        daily_data.append(data_point)
                        self.logger.debug(f"提取数据: {time_obj.strftime('%H:%M:%S')} - {energy_float} MWh - {power:.4f} MW")
                        
                    except Exception as e:
                        self.logger.warning(f"数据转换失败 (行{current_row}): {e}")
                
                current_row += 1
            
            # 验证数据完整性
            if len(daily_data) >= 40:  # 至少40条数据才认为成功
                self.logger.info(f"✅ 成功提取 {len(daily_data)} 条半小时数据")
                
                # 按时间排序
                daily_data.sort(key=lambda x: x['时间'])
                
                # 记录处理日志
                self.processing_log.append({
                    'date': date_str,
                    'file': file_info['file_name'],
                    'extracted_points': len(daily_data),
                    'status': 'success'
                })
                
                return daily_data
            else:
                self.logger.error(f"❌ 数据不完整，只提取到 {len(daily_data)} 条数据")
                self.error_log.append({
                    'date': date_str,
                    'file': file_info['file_name'],
                    'error': f'数据不完整，只有{len(daily_data)}条',
                    'extracted_points': len(daily_data)
                })
                return []
                
        except Exception as e:
            self.logger.error(f"❌ 文件处理失败: {e}")
            self.logger.error(traceback.format_exc())
            self.error_log.append({
                'date': date_str,
                'file': file_info['file_name'],
                'error': str(e),
                'extracted_points': 0
            })
            return []
    
    def is_time_data(self, cell):
        """
        判断是否为时间数据
        """
        if isinstance(cell, datetime):
            return True
        
        cell_str = str(cell)
        
        # 检查时间格式
        time_patterns = [
            r'^\d{2}:\d{2}:\d{2}$',  # HH:MM:SS
            r'^\d{1,2}:\d{2}:\d{2}$',  # H:MM:SS
            r'2025.*\d{2}:\d{2}:\d{2}',  # 包含2025和时间的字符串
        ]
        
        for pattern in time_patterns:
            if re.search(pattern, cell_str):
                return True
        
        return False
    
    def is_energy_data(self, cell):
        """
        判断是否为电量数据
        """
        try:
            num_val = float(cell)
            # 合理的电量范围：0.1 - 10 MWh
            return 0.1 <= num_val <= 10.0
        except:
            return False

    def process_all_files(self):
        """
        批量处理所有现货结算表文件
        """
        self.logger.info("="*80)
        self.logger.info("开始批量处理现货结算表文件")
        self.logger.info("="*80)

        # 查找文件
        found_files, missing_files = self.find_settlement_files()

        if not found_files:
            self.logger.error("未找到任何现货结算表文件")
            return False

        # 处理每个文件
        success_count = 0
        total_extracted_points = 0

        for file_info in found_files:
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"处理文件 {success_count + 1}/{len(found_files)}: {file_info['date']}")

            daily_data = self.extract_complete_daily_data(file_info)

            if daily_data:
                self.all_raw_data.extend(daily_data)
                success_count += 1
                total_extracted_points += len(daily_data)
                self.logger.info(f"✅ 文件处理成功: {len(daily_data)} 条数据")
            else:
                self.logger.error(f"❌ 文件处理失败")

        # 处理结果统计
        self.logger.info(f"\n{'='*80}")
        self.logger.info(f"批量处理完成统计:")
        self.logger.info(f"  成功处理文件: {success_count}/{len(found_files)}")
        self.logger.info(f"  总提取数据点: {total_extracted_points}")
        self.logger.info(f"  平均每天数据点: {total_extracted_points/success_count if success_count > 0 else 0:.1f}")
        self.logger.info(f"  缺失文件: {len(missing_files)}")
        self.logger.info(f"{'='*80}")

        if self.all_raw_data:
            # 数据质量分析
            self.analyze_data_quality()
            return True
        else:
            self.logger.error("未提取到任何有效数据")
            return False

    def analyze_data_quality(self):
        """
        分析数据质量
        """
        self.logger.info("\n开始数据质量分析...")

        # 按日期分组分析
        daily_groups = {}
        for data in self.all_raw_data:
            date = data['日期']
            if date not in daily_groups:
                daily_groups[date] = []
            daily_groups[date].append(data)

        # 分析每日数据
        quality_report = []
        for date, daily_data in daily_groups.items():
            energies = [d['计量电量(MWh)'] for d in daily_data]
            powers = [d['功率(MW)'] for d in daily_data]

            quality_info = {
                'date': date,
                'data_points': len(daily_data),
                'completeness': len(daily_data) / self.expected_half_hour_points * 100,
                'energy_min': min(energies),
                'energy_max': max(energies),
                'energy_avg': sum(energies) / len(energies),
                'power_min': min(powers),
                'power_max': max(powers),
                'power_avg': sum(powers) / len(powers),
                'energy_std': np.std(energies),
                'power_std': np.std(powers)
            }

            quality_report.append(quality_info)

            self.logger.info(f"  {date}: {len(daily_data)}点 ({quality_info['completeness']:.1f}%), "
                           f"电量{quality_info['energy_min']:.2f}-{quality_info['energy_max']:.2f}MWh, "
                           f"功率{quality_info['power_min']:.2f}-{quality_info['power_max']:.2f}MW")

        # 整体质量评估
        total_points = len(self.all_raw_data)
        expected_total = len(daily_groups) * self.expected_half_hour_points
        overall_completeness = total_points / expected_total * 100 if expected_total > 0 else 0

        self.logger.info(f"\n数据质量评估:")
        self.logger.info(f"  整体完整性: {overall_completeness:.1f}% ({total_points}/{expected_total})")
        self.logger.info(f"  处理天数: {len(daily_groups)} 天")

        # 保存质量报告
        self.data_quality_report = quality_report

        return quality_report

    def generate_15min_curves_complete(self):
        """
        基于完整48条数据生成15分钟出力曲线
        """
        self.logger.info("\n开始生成15分钟出力曲线...")

        if not self.all_raw_data:
            self.logger.error("没有原始数据可处理")
            return False

        # 按日期分组
        daily_groups = {}
        for data in self.all_raw_data:
            date = data['日期']
            if date not in daily_groups:
                daily_groups[date] = []
            daily_groups[date].append(data)

        # 为每一天生成15分钟数据
        total_15min_points = 0

        for date, daily_data in daily_groups.items():
            self.logger.info(f"  处理日期: {date} ({len(daily_data)}条半小时数据)")

            # 按时间排序
            daily_data.sort(key=lambda x: x['时间'])

            # 为每个半小时数据生成两个15分钟时间点
            daily_15min_count = 0

            for data_point in daily_data:
                time_str = data_point['时间']
                energy = data_point['计量电量(MWh)']
                power = data_point['功率(MW)']

                # 15分钟数据计算
                quarter_energy = energy / 2  # 每15分钟电量
                quarter_power = quarter_energy / 0.25  # 15分钟功率

                # 生成对应的15分钟时间点
                time_points = self.get_15min_time_points_for_half_hour(time_str)

                for time_point in time_points:
                    self.all_15min_data.append({
                        '日期': date,
                        '时间': time_point,
                        '电量(MWh)': quarter_energy,
                        '功率(MW)': quarter_power,
                        '原始时间': time_str,
                        '原始电量': energy,
                        '原始功率': power,
                        '数据来源': 'generated_from_half_hour'
                    })
                    daily_15min_count += 1

            total_15min_points += daily_15min_count
            self.logger.info(f"    生成15分钟数据点: {daily_15min_count} 个")

        self.logger.info(f"✅ 15分钟出力曲线生成完成: {total_15min_points} 个时间点")

        # 数据验证
        self.validate_15min_data()

        return True

    def get_15min_time_points_for_half_hour(self, half_hour_time):
        """
        根据半小时时间点生成对应的15分钟时间点
        """
        hour, minute, second = map(int, half_hour_time.split(':'))

        if minute == 30:
            # 00:30 对应 00:15 和 00:30
            return [f"{hour:02d}:15", f"{hour:02d}:30"]
        elif minute == 0:
            # 01:00 对应 00:45 和 01:00
            if hour == 0:
                # 特殊处理 00:00 (次日)
                return ["23:45", "24:00"]
            else:
                return [f"{hour-1:02d}:45", f"{hour:02d}:00"]
        else:
            # 其他情况，使用默认处理
            self.logger.warning(f"非标准半小时时间点: {half_hour_time}")
            return [f"{hour:02d}:{minute:02d}"]

    def validate_15min_data(self):
        """
        验证15分钟数据的完整性和正确性
        """
        self.logger.info("\n开始验证15分钟数据...")

        # 按日期分组验证
        daily_groups = {}
        for data in self.all_15min_data:
            date = data['日期']
            if date not in daily_groups:
                daily_groups[date] = []
            daily_groups[date].append(data)

        validation_results = []

        for date, daily_data in daily_groups.items():
            # 检查数据点数量
            expected_points = self.expected_15min_points
            actual_points = len(daily_data)
            completeness = actual_points / expected_points * 100

            # 检查时间连续性
            times = [d['时间'] for d in daily_data]
            unique_times = len(set(times))
            has_duplicates = unique_times != actual_points

            # 检查数据合理性
            energies = [d['电量(MWh)'] for d in daily_data]
            powers = [d['功率(MW)'] for d in daily_data]

            validation_result = {
                'date': date,
                'expected_points': expected_points,
                'actual_points': actual_points,
                'completeness': completeness,
                'has_duplicates': has_duplicates,
                'unique_times': unique_times,
                'energy_range': (min(energies), max(energies)),
                'power_range': (min(powers), max(powers)),
                'is_valid': completeness >= 80 and not has_duplicates
            }

            validation_results.append(validation_result)

            status = "✅" if validation_result['is_valid'] else "❌"
            self.logger.info(f"  {status} {date}: {actual_points}/{expected_points}点 ({completeness:.1f}%), "
                           f"重复: {'是' if has_duplicates else '否'}")

        # 整体验证结果
        valid_days = sum(1 for r in validation_results if r['is_valid'])
        total_days = len(validation_results)

        self.logger.info(f"\n15分钟数据验证结果:")
        self.logger.info(f"  有效天数: {valid_days}/{total_days}")
        self.logger.info(f"  总数据点: {len(self.all_15min_data)}")

        self.validation_results = validation_results

        return validation_results

    def generate_daily_summaries_complete(self):
        """
        生成完整的每日统计汇总
        """
        self.logger.info("\n开始生成每日统计汇总...")

        # 按日期分组统计15分钟数据
        daily_groups = {}
        for data in self.all_15min_data:
            date = data['日期']
            if date not in daily_groups:
                daily_groups[date] = []
            daily_groups[date].append(data)

        for date, daily_data in daily_groups.items():
            energies = [d['电量(MWh)'] for d in daily_data]
            powers = [d['功率(MW)'] for d in daily_data]

            # 计算统计指标
            summary = {
                'date': date,
                'data_points_15min': len(daily_data),
                'total_energy_15min': sum(energies),
                'avg_energy_15min': np.mean(energies),
                'max_energy_15min': max(energies),
                'min_energy_15min': min(energies),
                'std_energy_15min': np.std(energies),
                'avg_power': np.mean(powers),
                'max_power': max(powers),
                'min_power': min(powers),
                'std_power': np.std(powers),
                'power_range': max(powers) - min(powers),
                'load_factor': np.mean(powers) / max(powers) if max(powers) > 0 else 0,
                'completeness_15min': len(daily_data) / self.expected_15min_points * 100
            }

            # 添加原始半小时数据统计
            raw_daily_data = [d for d in self.all_raw_data if d['日期'] == date]
            if raw_daily_data:
                raw_energies = [d['计量电量(MWh)'] for d in raw_daily_data]
                raw_powers = [d['功率(MW)'] for d in raw_daily_data]

                summary.update({
                    'data_points_raw': len(raw_daily_data),
                    'total_energy_raw': sum(raw_energies),
                    'avg_energy_raw': np.mean(raw_energies),
                    'max_energy_raw': max(raw_energies),
                    'min_energy_raw': min(raw_energies),
                    'completeness_raw': len(raw_daily_data) / self.expected_half_hour_points * 100
                })

            self.daily_summaries.append(summary)

            self.logger.info(f"  {date}: 15分钟{len(daily_data)}点, 原始{len(raw_daily_data)}点, "
                           f"总电量{summary['total_energy_15min']:.2f}MWh, "
                           f"功率{summary['min_power']:.2f}-{summary['max_power']:.2f}MW")

        self.logger.info(f"✅ 每日统计汇总完成: {len(self.daily_summaries)} 天")

        return True

    def save_complete_results(self, output_file="中栋电厂7月1-14日完整数据处理结果.xlsx"):
        """
        保存完整的处理结果
        """
        self.logger.info(f"\n开始保存完整处理结果...")

        if not self.all_15min_data:
            self.logger.error("没有15分钟数据可保存")
            return False

        try:
            output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"

            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 1. 连续时间出力曲线 - 按您要求的格式
                self.save_continuous_time_format(writer)

                # 2. 15分钟出力曲线汇总
                df_15min = pd.DataFrame(self.all_15min_data)
                df_15min_sorted = df_15min.sort_values(['日期', '时间']).reset_index(drop=True)
                df_15min_sorted.to_excel(writer, sheet_name='15分钟出力曲线汇总', index=False)

                # 3. 原始完整半小时数据
                df_raw = pd.DataFrame(self.all_raw_data)
                df_raw_sorted = df_raw.sort_values(['日期', '时间']).reset_index(drop=True)
                df_raw_sorted.to_excel(writer, sheet_name='原始完整半小时数据', index=False)

                # 4. 每日统计汇总
                if self.daily_summaries:
                    df_summary = pd.DataFrame(self.daily_summaries)
                    df_summary.to_excel(writer, sheet_name='每日统计汇总', index=False)

                # 5. 数据质量报告
                if hasattr(self, 'data_quality_report'):
                    df_quality = pd.DataFrame(self.data_quality_report)
                    df_quality.to_excel(writer, sheet_name='数据质量报告', index=False)

                # 6. 验证结果
                if hasattr(self, 'validation_results'):
                    df_validation = pd.DataFrame(self.validation_results)
                    df_validation.to_excel(writer, sheet_name='数据验证结果', index=False)

                # 7. 处理日志
                if self.processing_log:
                    df_log = pd.DataFrame(self.processing_log)
                    df_log.to_excel(writer, sheet_name='处理日志', index=False)

                # 8. 错误日志
                if self.error_log:
                    df_error = pd.DataFrame(self.error_log)
                    df_error.to_excel(writer, sheet_name='错误日志', index=False)

                # 9. 按日期分表 - 15分钟数据
                self.save_daily_sheets_15min(writer)

                # 10. 按日期分表 - 按要求格式
                self.save_daily_sheets_format(writer)

                # 11. 整体统计分析
                self.save_overall_statistics(writer)

            self.logger.info(f"✅ 完整结果已保存到: {output_path}")

            # 显示保存统计
            self.display_save_statistics()

            return True

        except Exception as e:
            self.logger.error(f"❌ 保存失败: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def save_continuous_time_format(self, writer):
        """
        保存连续时间格式数据
        """
        # 按时间顺序排序所有15分钟数据
        sorted_data = sorted(self.all_15min_data,
                           key=lambda x: (x['日期'], x['时间']))

        # 创建连续时间格式
        continuous_format = []
        for data_point in sorted_data:
            continuous_format.append({
                '日期时间': f"{data_point['日期']} {data_point['时间']}",
                '实时出力_MW': f"{data_point['功率(MW)']:.4f}",
                '总功率=总电量/总时长': ""  # 电量列留空
            })

        df_continuous = pd.DataFrame(continuous_format)
        df_continuous.to_excel(writer, sheet_name='连续时间出力曲线', index=False)

        self.logger.info(f"  连续时间出力曲线: {len(continuous_format)} 条数据")

    def save_daily_sheets_15min(self, writer):
        """
        保存按日期分表的15分钟数据
        """
        # 按日期分组
        daily_groups = {}
        for data in self.all_15min_data:
            date = data['日期']
            if date not in daily_groups:
                daily_groups[date] = []
            daily_groups[date].append(data)

        for date in sorted(daily_groups.keys()):
            daily_data = daily_groups[date]
            date_short = date[5:]  # 07-01格式

            # 标准15分钟数据
            daily_standard_data = []
            for data_point in sorted(daily_data, key=lambda x: x['时间']):
                daily_standard_data.append({
                    '时间': data_point['时间'],
                    '电量(MWh)': data_point['电量(MWh)'],
                    '功率(MW)': data_point['功率(MW)'],
                    '原始时间': data_point['原始时间'],
                    '原始电量': data_point['原始电量'],
                    '原始功率': data_point['原始功率']
                })

            df_daily = pd.DataFrame(daily_standard_data)
            sheet_name = f"15分钟数据_{date_short}"
            df_daily.to_excel(writer, sheet_name=sheet_name, index=False)

        self.logger.info(f"  按日期分表(15分钟): {len(daily_groups)} 个工作表")

    def save_daily_sheets_format(self, writer):
        """
        保存按日期分表的格式化数据
        """
        # 按日期分组
        daily_groups = {}
        for data in self.all_15min_data:
            date = data['日期']
            if date not in daily_groups:
                daily_groups[date] = []
            daily_groups[date].append(data)

        for date in sorted(daily_groups.keys()):
            daily_data = daily_groups[date]
            date_short = date[5:]  # 07-01格式

            # 按要求格式的单日数据
            daily_format_data = []
            for data_point in sorted(daily_data, key=lambda x: x['时间']):
                daily_format_data.append({
                    f'{date_short}实时出力_时间': data_point['时间'],
                    f'{date_short}实时出力_MW': f"{data_point['功率(MW)']:.4f}",
                    '总功率=总电量/总时长': ""  # 电量列留空
                })

            df_daily_format = pd.DataFrame(daily_format_data)
            sheet_name = f"按要求格式_{date_short}"
            df_daily_format.to_excel(writer, sheet_name=sheet_name, index=False)

        self.logger.info(f"  按日期分表(格式化): {len(daily_groups)} 个工作表")

    def save_overall_statistics(self, writer):
        """
        保存整体统计分析
        """
        # 计算整体统计
        all_energies = [d['电量(MWh)'] for d in self.all_15min_data]
        all_powers = [d['功率(MW)'] for d in self.all_15min_data]

        # 按日期分组统计
        daily_groups = {}
        for data in self.all_15min_data:
            date = data['日期']
            if date not in daily_groups:
                daily_groups[date] = []
            daily_groups[date].append(data)

        overall_stats = {
            '项目': [
                '处理日期数', '原始半小时数据点', '生成15分钟数据点',
                '总电量(MWh)', '平均电量(MWh)', '最大电量(MWh)', '最小电量(MWh)',
                '平均功率(MW)', '最大功率(MW)', '最小功率(MW)', '功率标准差(MW)',
                '数据完整性(%)', '平均负荷率(%)', '功率变化范围(MW)'
            ],
            '数值': [
                f"{len(daily_groups)} 天",
                f"{len(self.all_raw_data)} 个",
                f"{len(self.all_15min_data)} 个",
                f"{sum(all_energies):.2f} MWh",
                f"{np.mean(all_energies):.4f} MWh",
                f"{max(all_energies):.4f} MWh",
                f"{min(all_energies):.4f} MWh",
                f"{np.mean(all_powers):.2f} MW",
                f"{max(all_powers):.2f} MW",
                f"{min(all_powers):.2f} MW",
                f"{np.std(all_powers):.2f} MW",
                f"{len(self.all_15min_data)/(len(daily_groups)*self.expected_15min_points)*100:.1f}%",
                f"{np.mean(all_powers)/max(all_powers)*100:.1f}%",
                f"{max(all_powers) - min(all_powers):.2f} MW"
            ]
        }

        df_overall = pd.DataFrame(overall_stats)
        df_overall.to_excel(writer, sheet_name='整体统计分析', index=False)

        self.logger.info(f"  整体统计分析: 完成")

    def display_save_statistics(self):
        """
        显示保存统计信息
        """
        self.logger.info(f"\n📊 保存统计信息:")
        self.logger.info(f"   连续时间出力曲线: {len(self.all_15min_data)} 条数据")
        self.logger.info(f"   15分钟出力曲线汇总: {len(self.all_15min_data)} 个时间点")
        self.logger.info(f"   原始完整半小时数据: {len(self.all_raw_data)} 条")
        self.logger.info(f"   每日统计汇总: {len(self.daily_summaries)} 天")

        if hasattr(self, 'data_quality_report'):
            self.logger.info(f"   数据质量报告: {len(self.data_quality_report)} 天")

        if hasattr(self, 'validation_results'):
            self.logger.info(f"   数据验证结果: {len(self.validation_results)} 天")

        self.logger.info(f"   处理日志: {len(self.processing_log)} 条")
        self.logger.info(f"   错误日志: {len(self.error_log)} 条")

        # 按日期分组统计
        daily_groups = {}
        for data in self.all_15min_data:
            date = data['日期']
            if date not in daily_groups:
                daily_groups[date] = []
            daily_groups[date].append(data)

        self.logger.info(f"   按日期分表: {len(daily_groups)} 天 × 2种格式 = {len(daily_groups)*2} 个工作表")

    def generate_processing_report(self):
        """
        生成处理报告
        """
        self.logger.info("\n" + "="*80)
        self.logger.info("中栋电厂数据处理完成报告")
        self.logger.info("="*80)

        # 数据统计
        daily_groups = {}
        for data in self.all_15min_data:
            date = data['日期']
            if date not in daily_groups:
                daily_groups[date] = []
            daily_groups[date].append(data)

        self.logger.info(f"📊 数据处理统计:")
        self.logger.info(f"   处理天数: {len(daily_groups)} 天")
        self.logger.info(f"   原始半小时数据: {len(self.all_raw_data)} 条")
        self.logger.info(f"   生成15分钟数据: {len(self.all_15min_data)} 个时间点")
        self.logger.info(f"   平均每天半小时数据: {len(self.all_raw_data)/len(daily_groups):.1f} 条")
        self.logger.info(f"   平均每天15分钟数据: {len(self.all_15min_data)/len(daily_groups):.1f} 个")

        # 数据质量
        if hasattr(self, 'validation_results'):
            valid_days = sum(1 for r in self.validation_results if r['is_valid'])
            self.logger.info(f"\n📈 数据质量:")
            self.logger.info(f"   有效天数: {valid_days}/{len(self.validation_results)}")
            self.logger.info(f"   数据完整性: {valid_days/len(self.validation_results)*100:.1f}%")

        # 功率统计
        all_powers = [d['功率(MW)'] for d in self.all_15min_data]
        if all_powers:
            self.logger.info(f"\n⚡ 功率统计:")
            self.logger.info(f"   平均功率: {np.mean(all_powers):.2f} MW")
            self.logger.info(f"   最大功率: {max(all_powers):.2f} MW")
            self.logger.info(f"   最小功率: {min(all_powers):.2f} MW")
            self.logger.info(f"   功率范围: {max(all_powers) - min(all_powers):.2f} MW")
            self.logger.info(f"   负荷率: {np.mean(all_powers)/max(all_powers)*100:.1f}%")

        # 处理结果
        success_count = len(self.processing_log)
        error_count = len(self.error_log)

        self.logger.info(f"\n✅ 处理结果:")
        self.logger.info(f"   成功处理: {success_count} 个文件")
        self.logger.info(f"   处理失败: {error_count} 个文件")
        self.logger.info(f"   成功率: {success_count/(success_count+error_count)*100:.1f}%")

        self.logger.info("="*80)

        return True

def main():
    """
    主函数 - 完整实现版本
    """
    print("中栋电厂完整数据处理工具 - 完整实现版本")
    print("从现货结算表中提取完整48条半小时实时电能数据，生成15分钟出力曲线")
    print("="*80)

    # 创建处理器实例
    processor = ZhongdongCompleteFullProcessor()

    try:
        # 步骤1: 批量处理所有文件，提取完整48条数据
        print("\n步骤1: 批量处理现货结算表文件...")
        if not processor.process_all_files():
            print("❌ 文件处理失败，程序终止")
            return

        # 步骤2: 生成15分钟出力曲线
        print("\n步骤2: 生成15分钟出力曲线...")
        if not processor.generate_15min_curves_complete():
            print("❌ 15分钟曲线生成失败，程序终止")
            return

        # 步骤3: 生成每日统计汇总
        print("\n步骤3: 生成每日统计汇总...")
        if not processor.generate_daily_summaries_complete():
            print("❌ 统计汇总生成失败，程序终止")
            return

        # 步骤4: 保存完整结果
        print("\n步骤4: 保存完整处理结果...")
        if not processor.save_complete_results():
            print("❌ 结果保存失败，程序终止")
            return

        # 步骤5: 生成处理报告
        print("\n步骤5: 生成处理报告...")
        processor.generate_processing_report()

        print(f"\n🎉 中栋电厂数据处理完成！")
        print(f"📊 成功提取 {len(processor.all_raw_data)} 条完整半小时数据")
        print(f"⚡ 生成了 {len(processor.all_15min_data)} 个15分钟时间点")
        print(f"📁 结果已保存为完整格式，包含所有分析和验证")
        print(f"🎯 这是基于真正完整的48条半小时数据的完整实现！")

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        print(traceback.format_exc())
        return

if __name__ == "__main__":
    main()
