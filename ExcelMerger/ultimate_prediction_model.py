#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极用电量预测模型
实现总量约束、外部因素集成和动态更新的完整预测系统
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
from bs4 import BeautifulSoup
import time
import random
import re
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

class UltimatePredictionModel:
    def __init__(self):
        """
        初始化终极预测模型
        """
        self.user_models = {}
        self.weather_models = {}
        self.total_constraint_model = None
        self.user_clusters = {}
        self.weather_cache = {}
        self.holiday_calendar = {}
        
        print("🚀 终极用电量预测模型")
        print("🎯 总量约束 + 外部因素 + 动态更新")
    
    def initialize_external_factors(self):
        """
        初始化外部因素：天气、节假日等
        """
        print("\n🌍 初始化外部因素...")
        
        # 1. 初始化节假日日历
        self.holiday_calendar = {
            '2025-07-01': '建党节前',
            '2025-07-19': '工作日',
            '2025-07-20': '周末',
            '2025-07-21': '周末',
            # 可以扩展更多节假日
        }
        
        # 2. 初始化天气影响因子
        self.weather_impact_factors = {
            '制冷需求': {'temp_threshold': 26, 'impact_factor': 0.15},
            '制热需求': {'temp_threshold': 18, 'impact_factor': 0.12},
            '湿度影响': {'humidity_threshold': 80, 'impact_factor': 0.08},
            'AQI影响': {'aqi_threshold': 100, 'impact_factor': 0.05}
        }
        
        print("✅ 外部因素初始化完成")
        
    def scrape_real_weather_data(self, target_date):
        """
        爬取真实天气数据（基于现有的天气爬虫）
        """
        print(f"\n🌤️ 爬取 {target_date} 真实天气数据...")
        
        # 解析日期
        date_obj = datetime.strptime(target_date, '%Y-%m-%d')
        month_str = date_obj.strftime('%Y%m')
        day_str = date_obj.strftime('%m-%d')
        
        # 浙江省主要城市
        cities = {
            '杭州': 'hangzhou',
            '宁波': 'ningbo', 
            '温州': 'wenzhou',
            '嘉兴': 'jiaxing',
            '湖州': 'huzhou',
            '绍兴': 'shaoxing'
        }
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive'
        }
        
        weather_results = {}
        
        for city_name, city_code in cities.items():
            try:
                url = f"https://www.tianqi24.com/{city_code}/history{month_str}.html"
                print(f"  爬取 {city_name}: {url}")
                
                response = requests.get(url, headers=headers, timeout=15)
                response.encoding = 'utf-8'
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 查找指定日期数据
                    weather_lists = soup.find_all('ul', class_='col6')
                    
                    for ul in weather_lists:
                        items = ul.find_all('li')
                        
                        for item in items[1:]:  # 跳过表头
                            divs = item.find_all('div')
                            
                            if len(divs) >= 7:
                                date_text = divs[0].get_text().strip()
                                
                                if day_str in date_text:
                                    print(f"    ✅ 找到{city_name}{target_date}数据")
                                    
                                    # 解析天气数据
                                    weather_text = divs[1].get_text().strip()
                                    high_temp = self.extract_number(divs[2].get_text())
                                    low_temp = self.extract_number(divs[3].get_text())
                                    aqi = self.extract_number(divs[4].get_text())
                                    wind = divs[5].get_text().strip()
                                    precipitation = self.extract_float(divs[6].get_text())
                                    
                                    weather_results[city_name] = {
                                        'weather': weather_text.split('/')[0] if '/' in weather_text else weather_text,
                                        'temp_max': high_temp,
                                        'temp_min': low_temp,
                                        'aqi': aqi,
                                        'wind': wind,
                                        'precipitation': precipitation
                                    }
                                    break
                
                # 请求间隔
                time.sleep(random.uniform(1, 2))
                
            except Exception as e:
                print(f"    ❌ {city_name}爬取失败: {e}")
        
        # 计算综合天气数据
        if weather_results:
            temps_max = [w['temp_max'] for w in weather_results.values() if w['temp_max']]
            temps_min = [w['temp_min'] for w in weather_results.values() if w['temp_min']]
            aqis = [w['aqi'] for w in weather_results.values() if w['aqi']]
            precipitations = [w['precipitation'] for w in weather_results.values() if w['precipitation']]
            
            weathers = [w['weather'] for w in weather_results.values()]
            main_weather = max(set(weathers), key=weathers.count) if weathers else '晴'
            
            comprehensive_weather = {
                '最高温度(°C)': np.mean(temps_max) if temps_max else 35.0,
                '最低温度(°C)': np.mean(temps_min) if temps_min else 27.0,
                '平均温度': (np.mean(temps_max) + np.mean(temps_min)) / 2 if temps_max and temps_min else 31.0,
                '湿度(%)': 75.0,  # 默认值，可以扩展爬取
                'AQI': np.mean(aqis) if aqis else 40.0,
                '降水量(mm)': np.mean(precipitations) if precipitations else 0.0,
                '天气': main_weather,
                '风向': '南风',
                '日期类型': self.get_date_type(target_date)
            }
            
            # 计算天气影响因子
            comprehensive_weather.update(self.calculate_weather_impact(comprehensive_weather))
            
            # 缓存天气数据
            self.weather_cache[target_date] = comprehensive_weather
            
            print(f"✅ {target_date}天气数据获取成功: {len(weather_results)}个城市")
            return comprehensive_weather
        else:
            print("❌ 天气数据获取失败，使用默认值")
            return self.get_default_weather(target_date)
    
    def extract_number(self, text):
        """提取数字"""
        match = re.search(r'(\d+)', str(text))
        return int(match.group(1)) if match else None
    
    def extract_float(self, text):
        """提取浮点数"""
        match = re.search(r'(\d+(?:\.\d+)?)', str(text))
        return float(match.group(1)) if match else 0.0
    
    def get_date_type(self, date_str):
        """获取日期类型"""
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        weekday = date_obj.weekday()
        
        if date_str in self.holiday_calendar:
            return self.holiday_calendar[date_str]
        elif weekday >= 5:
            return '周末'
        else:
            return '工作日'
    
    def calculate_weather_impact(self, weather_data):
        """计算天气影响因子"""
        impact_factors = {}
        
        # 制冷度日
        avg_temp = weather_data['平均温度']
        impact_factors['制冷度日'] = max(avg_temp - 26, 0)
        impact_factors['制热度日'] = max(18 - avg_temp, 0)
        
        # 温差影响
        impact_factors['温差'] = weather_data['最高温度(°C)'] - weather_data['最低温度(°C)']
        
        # 综合舒适度指数
        comfort_index = 1.0
        if avg_temp > 30:
            comfort_index *= 1.15  # 高温增加用电
        elif avg_temp < 20:
            comfort_index *= 1.10  # 低温增加用电
        
        if weather_data['湿度(%)'] > 80:
            comfort_index *= 1.05  # 高湿度增加用电
        
        if weather_data['AQI'] > 100:
            comfort_index *= 1.02  # 高AQI可能影响工业用电
        
        impact_factors['天气舒适度指数'] = comfort_index
        
        return impact_factors
    
    def get_default_weather(self, target_date):
        """获取默认天气数据"""
        return {
            '最高温度(°C)': 35.0,
            '最低温度(°C)': 27.0,
            '平均温度': 31.0,
            '湿度(%)': 75.0,
            'AQI': 40.0,
            '降水量(mm)': 0.0,
            '天气': '晴',
            '风向': '南风',
            '日期类型': self.get_date_type(target_date),
            '制冷度日': 5.0,
            '制热度日': 0.0,
            '温差': 8.0,
            '天气舒适度指数': 1.15
        }
    
    def load_and_prepare_comprehensive_data(self):
        """
        加载和准备综合数据
        """
        print("\n📋 加载综合数据...")
        
        # 加载训练数据
        train_file = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        df_train = pd.read_excel(train_file)
        
        # 加载真实18-19号数据
        actual_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250718-20250719.xlsx'
        df_actual = pd.read_excel(actual_file)
        
        # 时间处理
        df_train['时间'] = pd.to_datetime(df_train['时间'])
        df_actual['时间'] = pd.to_datetime(df_actual['时间'])
        
        # 添加外部因素特征
        df_train = self.add_external_features(df_train)
        df_actual = self.add_external_features(df_actual)
        
        # 分离18号和19号数据
        df_actual_18 = df_actual[df_actual['时间'].dt.date == pd.to_datetime('2025-07-18').date()].copy()
        df_actual_19 = df_actual[df_actual['时间'].dt.date == pd.to_datetime('2025-07-19').date()].copy()
        
        print(f"训练数据: {df_train.shape}")
        print(f"18号数据: {df_actual_18.shape}")
        print(f"19号数据: {df_actual_19.shape}")
        
        return df_train, df_actual_18, df_actual_19
    
    def add_external_features(self, df):
        """
        添加外部因素特征
        """
        # 时间特征
        df['月份'] = df['时间'].dt.month
        df['日期'] = df['时间'].dt.day
        df['星期'] = df['时间'].dt.dayofweek
        df['是否周末'] = (df['星期'] >= 5).astype(int)
        df['是否月初'] = (df['日期'] <= 10).astype(int)
        df['是否月末'] = (df['日期'] >= 20).astype(int)
        
        # 天气特征增强
        if '最高温度(°C)' in df.columns and '最低温度(°C)' in df.columns:
            df['平均温度'] = (df['最高温度(°C)'] + df['最低温度(°C)']) / 2
            df['温差'] = df['最高温度(°C)'] - df['最低温度(°C)']
            df['制冷度日'] = np.maximum(df['平均温度'] - 26, 0)
            df['制热度日'] = np.maximum(18 - df['平均温度'], 0)
            
            # 天气舒适度指数
            df['天气舒适度指数'] = 1.0
            df.loc[df['平均温度'] > 30, '天气舒适度指数'] *= 1.15
            df.loc[df['平均温度'] < 20, '天气舒适度指数'] *= 1.10
            df.loc[df['湿度(%)'] > 80, '天气舒适度指数'] *= 1.05
            df.loc[df['AQI'] > 100, '天气舒适度指数'] *= 1.02
        
        # 生产周期特征（基于日期推断）
        df['生产周期'] = 'normal'
        df.loc[df['日期'].isin([1, 15]), '生产周期'] = 'peak'  # 月初月中可能是生产高峰
        df.loc[df['星期'].isin([0, 6]), '生产周期'] = 'low'   # 周一周日可能是生产低谷
        
        return df

    def create_enhanced_user_clusters(self, df_train):
        """
        创建增强的用户聚类（考虑外部因素）
        """
        print("\n👥 创建增强用户聚类...")

        # 计算用户特征（包含外部因素响应）
        user_features = df_train.groupby('户号').agg({
            '总电量(kWh)': ['mean', 'std', 'min', 'max', 'count'],
            '尖电量(kWh)': ['mean', 'sum'],
            '峰电量(kWh)': ['mean', 'sum'],
            '平电量(kWh)': ['mean', 'sum'],
            '谷电量(kWh)': ['mean', 'sum'],
            '制冷度日': 'mean',
            '天气舒适度指数': 'mean',
            '是否周末': 'mean',
            '公司名称': 'first',
            '地区': 'first'
        }).round(2)

        # 扁平化列名
        user_features.columns = ['_'.join(col).strip() for col in user_features.columns]
        user_features = user_features.reset_index()

        # 计算天气敏感度
        user_features['天气敏感度'] = user_features['制冷度日_mean'] * user_features['天气舒适度指数_mean']
        user_features['周末效应'] = user_features['是否周末_mean']
        user_features['用电变异系数'] = user_features['总电量(kWh)_std'] / (user_features['总电量(kWh)_mean'] + 1)

        # 分时电量比例
        total_energy = (user_features['尖电量(kWh)_sum'] + user_features['峰电量(kWh)_sum'] +
                       user_features['平电量(kWh)_sum'] + user_features['谷电量(kWh)_sum'])

        user_features['尖电占比'] = user_features['尖电量(kWh)_sum'] / (total_energy + 1)
        user_features['峰电占比'] = user_features['峰电量(kWh)_sum'] / (total_energy + 1)
        user_features['平电占比'] = user_features['平电量(kWh)_sum'] / (total_energy + 1)
        user_features['谷电占比'] = user_features['谷电量(kWh)_sum'] / (total_energy + 1)

        # 增强聚类特征
        clustering_features = [
            '总电量(kWh)_mean', '用电变异系数', '天气敏感度', '周末效应',
            '尖电占比', '峰电占比', '平电占比', '谷电占比'
        ]

        X_cluster = user_features[clustering_features].fillna(0)

        # 标准化
        scaler = StandardScaler()
        X_cluster_scaled = scaler.fit_transform(X_cluster)

        # K-means聚类
        kmeans = KMeans(n_clusters=10, random_state=42, n_init=10)
        user_features['用户群体'] = kmeans.fit_predict(X_cluster_scaled)

        # 分析每个群体特征
        print(f"✅ 创建了10个增强用户群体:")
        for cluster in range(10):
            cluster_data = user_features[user_features['用户群体'] == cluster]
            if len(cluster_data) > 0:
                avg_power = cluster_data['总电量(kWh)_mean'].mean()
                weather_sensitivity = cluster_data['天气敏感度'].mean()
                count = len(cluster_data)

                print(f"  群体{cluster}: {count}个用户, 平均用电{avg_power:.0f}kWh, 天气敏感度{weather_sensitivity:.3f}")

        self.user_clusters = user_features

        return user_features

    def predict_with_total_constraint(self, target_date='2025-07-19'):
        """
        使用总量约束的终极预测
        """
        print(f"\n🎯 终极预测 {target_date} (含总量约束)...")

        if not self.user_models or not self.total_constraint_model:
            print("❌ 请先训练所有模型")
            return None

        # 1. 爬取真实天气数据
        weather_data = self.scrape_real_weather_data(target_date)

        # 2. 使用群体模型进行初始预测
        initial_predictions = self.predict_with_cluster_models(target_date, weather_data)

        # 3. 计算总量约束
        constraint_features_data = {
            '平均温度': weather_data['平均温度'],
            '制冷度日': weather_data['制冷度日'],
            '天气舒适度指数': weather_data['天气舒适度指数'],
            '是否周末': 1 if weather_data['日期类型'] == '周末' else 0
        }

        X_constraint = pd.DataFrame([constraint_features_data])[self.constraint_features]
        predicted_total = self.total_constraint_model.predict(X_constraint)[0]

        print(f"📊 总量约束: 预测总量 {predicted_total:.0f} kWh")

        # 4. 应用总量约束调整
        initial_total = initial_predictions['总电量(kWh)'].sum()
        adjustment_factor = predicted_total / initial_total if initial_total > 0 else 1.0

        print(f"📊 调整因子: {adjustment_factor:.4f}")

        # 5. 调整个体预测
        final_predictions = initial_predictions.copy()
        final_predictions['总电量(kWh)'] = (final_predictions['总电量(kWh)'] * adjustment_factor).round(1)

        # 6. 重新计算分时电量
        final_predictions = self.recalculate_time_segments(final_predictions)

        print(f"✅ 终极预测完成: {len(final_predictions)} 个用户")
        print(f"📊 调整后总电量: {final_predictions['总电量(kWh)'].sum():.1f} kWh")
        print(f"📊 平均用电量: {final_predictions['总电量(kWh)'].mean():.1f} kWh")

        return final_predictions, weather_data

    def predict_with_cluster_models(self, target_date, weather_data):
        """
        使用群体模型进行预测
        """
        all_users = self.user_clusters.copy()
        predictions = []

        for _, user_row in all_users.iterrows():
            user_id = user_row['户号']
            cluster = user_row['用户群体']

            if cluster not in self.user_models:
                # 使用历史平均值
                pred_power = user_row['总电量(kWh)_mean']
            else:
                # 使用群体模型预测
                model_info = self.user_models[cluster]
                model = model_info['model']
                scaler = model_info['scaler']
                features = model_info['features']

                # 准备预测特征
                pred_features = {
                    '总电量(kWh)_mean': user_row['总电量(kWh)_mean'],
                    '总电量(kWh)_std': user_row['总电量(kWh)_std'],
                    '总电量(kWh)_count': user_row.get('总电量(kWh)_count', 30),
                    '平均温度_mean': weather_data['平均温度'],
                    '制冷度日_mean': weather_data['制冷度日'],
                    '制热度日_mean': weather_data['制热度日'],
                    '天气舒适度指数_mean': weather_data['天气舒适度指数'],
                    '湿度(%)_mean': weather_data['湿度(%)'],
                    'AQI_mean': weather_data['AQI'],
                    '是否周末_mean': 1 if weather_data['日期类型'] == '周末' else 0,
                    '是否月初_mean': 1 if datetime.strptime(target_date, '%Y-%m-%d').day <= 10 else 0,
                    '是否月末_mean': 1 if datetime.strptime(target_date, '%Y-%m-%d').day >= 20 else 0,
                    '天气敏感度_first': user_row.get('天气敏感度', 1.0),
                    '周末效应_first': user_row.get('周末效应', 0.0)
                }

                X_pred = pd.DataFrame([pred_features])[features].fillna(0)

                if scaler is not None:
                    X_pred_scaled = scaler.transform(X_pred)
                    pred_power = model.predict(X_pred_scaled)[0]
                else:
                    pred_power = model.predict(X_pred)[0]

                pred_power = max(pred_power, 0)

            predictions.append({
                '表计号': user_id,
                '户号': user_id,
                '时间': target_date,
                '总电量(kWh)': round(pred_power, 1),
                '用户群体': cluster
            })

        return pd.DataFrame(predictions)

    def recalculate_time_segments(self, df_pred):
        """
        重新计算分时电量
        """
        # 使用优化的分时比例
        optimal_ratios = {
            '尖电量': 0.1940,
            '峰电量': 0.2807,
            '平电量': 0.1380,
            '谷电量': 0.3873
        }

        df_pred['尖电量(kWh)'] = (df_pred['总电量(kWh)'] * optimal_ratios['尖电量']).round(1)
        df_pred['峰电量(kWh)'] = (df_pred['总电量(kWh)'] * optimal_ratios['峰电量']).round(1)
        df_pred['平电量(kWh)'] = (df_pred['总电量(kWh)'] * optimal_ratios['平电量']).round(1)
        df_pred['谷电量(kWh)'] = (df_pred['总电量(kWh)'] * optimal_ratios['谷电量']).round(1)

        return df_pred

def main():
    """
    主函数
    """
    print("🚀 终极用电量预测模型")
    print("🎯 总量约束 + 外部因素 + 动态更新")
    print("="*70)

    model = UltimatePredictionModel()

    try:
        # 1. 初始化外部因素
        print("步骤1: 初始化外部因素")
        model.initialize_external_factors()

        # 2. 加载综合数据
        print("\n步骤2: 加载综合数据")
        df_train, df_actual_18, df_actual_19 = model.load_and_prepare_comprehensive_data()

        # 3. 创建增强用户聚类
        print("\n步骤3: 创建增强用户聚类")
        user_clusters = model.create_enhanced_user_clusters(df_train)

        print(f"\n🎉 终极预测模型初始化完成！")
        print(f"📊 数据规模: 训练{df_train.shape}, 18号{df_actual_18.shape}, 19号{df_actual_19.shape}")
        print(f"👥 用户群体: {len(user_clusters)} 个用户, 10个群体")
        print(f"🌍 外部因素: 天气、节假日、生产周期已集成")

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
