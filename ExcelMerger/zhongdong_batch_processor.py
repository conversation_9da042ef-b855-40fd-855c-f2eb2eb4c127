#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中栋电厂批量现货结算表处理工具
处理7月1号到7月14号的所有现货结算表，提取实时电能数据并按日期合并
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import glob
import re

class ZhongdongBatchProcessor:
    def __init__(self):
        """
        初始化批量处理器
        """
        self.folder_path = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/现货结算表/"
        self.all_data = []
        self.processed_data = None
        self.daily_summary = None
    
    def find_settlement_files(self):
        """
        查找7月1号到7月14号的现货结算表文件
        """
        print("正在查找现货结算表文件...")
        
        files = []
        for day in range(1, 15):  # 7月1号到7月14号
            pattern = f"中栋电厂2025-07-{day:02d}-day_sbs_gen_pub_detail_25.xlsx"
            file_path = os.path.join(self.folder_path, pattern)
            
            if os.path.exists(file_path):
                files.append({
                    'date': f"2025-07-{day:02d}",
                    'day': day,
                    'file_path': file_path,
                    'file_name': pattern
                })
                print(f"  ✅ 找到: {pattern}")
            else:
                print(f"  ❌ 缺失: {pattern}")
        
        print(f"\n✅ 共找到 {len(files)} 个文件")
        return files
    
    def extract_realtime_data_from_file(self, file_info):
        """
        从单个文件中提取实时电能数据
        """
        file_path = file_info['file_path']
        date_str = file_info['date']
        
        print(f"  处理文件: {file_info['file_name']}")
        
        try:
            # 读取文件，跳过前4行
            df = pd.read_excel(file_path, skiprows=4)
            df.columns = ['项目名称', '结算单元', '空列1', '时间', '计量电量', '结算电量', '出清电价', '结算电价', '空列2', '结算电费', '空列3']
            
            # 过滤掉表头行
            df = df[df['结算单元'] != '结算单元']
            df = df.dropna(subset=['项目名称', '结算单元'])
            
            # 筛选中栋电厂的实时电能数据
            realtime_data = df[(df['项目名称'] == '实时电能') & (df['结算单元'] == '中栋电厂')]
            
            if len(realtime_data) == 0:
                print(f"    ❌ 未找到实时电能数据")
                return []
            
            # 转换数据类型
            realtime_data = realtime_data.copy()
            realtime_data['时间'] = pd.to_datetime(realtime_data['时间'])
            realtime_data['计量电量'] = pd.to_numeric(realtime_data['计量电量'], errors='coerce')
            realtime_data['结算电量'] = pd.to_numeric(realtime_data['结算电量'], errors='coerce')
            
            # 过滤无效数据
            realtime_data = realtime_data.dropna(subset=['计量电量'])
            
            # 排序
            realtime_data = realtime_data.sort_values('时间').reset_index(drop=True)
            
            # 转换为标准格式
            data_list = []
            for _, row in realtime_data.iterrows():
                time_obj = row['时间']
                energy = row['计量电量']
                power = energy / 0.5  # 半小时功率
                
                # 处理跨日时间
                if time_obj.hour == 0 and time_obj.minute == 0:
                    # 00:00 是次日的
                    actual_date = datetime.strptime(date_str, '%Y-%m-%d').date() + timedelta(days=1)
                else:
                    actual_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                
                data_list.append({
                    '日期': date_str,
                    '实际日期': actual_date.strftime('%Y-%m-%d'),
                    '时间': time_obj.strftime('%H:%M:%S'),
                    '计量电量(MWh)': energy,
                    '功率(MW)': power,
                    '结算电量(MWh)': row['结算电量'],
                    '出清电价(元/MWh)': row['出清电价'],
                    '文件来源': file_info['file_name']
                })
            
            print(f"    ✅ 提取到 {len(data_list)} 条实时电能数据")
            return data_list
            
        except Exception as e:
            print(f"    ❌ 处理失败: {e}")
            return []
    
    def process_all_files(self):
        """
        处理所有文件
        """
        print("\n开始批量处理现货结算表文件...")
        
        # 查找文件
        files = self.find_settlement_files()
        
        if not files:
            print("❌ 未找到任何文件")
            return False
        
        # 处理每个文件
        all_data = []
        success_count = 0
        
        for file_info in files:
            data = self.extract_realtime_data_from_file(file_info)
            if data:
                all_data.extend(data)
                success_count += 1
        
        if not all_data:
            print("❌ 未提取到任何数据")
            return False
        
        # 转换为DataFrame
        self.all_data = pd.DataFrame(all_data)
        
        print(f"\n✅ 批量处理完成:")
        print(f"   成功处理文件: {success_count}/{len(files)}")
        print(f"   总数据条数: {len(self.all_data)}")
        print(f"   日期范围: {self.all_data['日期'].min()} 到 {self.all_data['日期'].max()}")
        print(f"   电量范围: {self.all_data['计量电量(MWh)'].min():.4f} - {self.all_data['计量电量(MWh)'].max():.4f} MWh")
        print(f"   功率范围: {self.all_data['功率(MW)'].min():.4f} - {self.all_data['功率(MW)'].max():.4f} MW")
        
        return True
    
    def generate_15min_curves(self):
        """
        为所有日期生成15分钟出力曲线
        """
        print("\n正在生成15分钟出力曲线...")
        
        if self.all_data is None or len(self.all_data) == 0:
            print("❌ 请先处理文件")
            return False
        
        all_15min_data = []
        
        # 按日期分组处理
        for date, group in self.all_data.groupby('日期'):
            print(f"  处理日期: {date}")
            
            group_sorted = group.sort_values('时间').reset_index(drop=True)
            
            # 为每个半小时数据生成两个15分钟时间点
            for _, row in group_sorted.iterrows():
                time_str = row['时间']
                half_hour_energy = row['计量电量(MWh)']
                half_hour_power = row['功率(MW)']
                
                # 每15分钟的电量和功率
                quarter_hour_energy = half_hour_energy / 2
                quarter_hour_power = quarter_hour_energy / 0.25
                
                # 解析时间
                hour, minute, second = map(int, time_str.split(':'))
                
                # 生成两个15分钟时间点
                if minute == 30:
                    # 00:30 对应 00:15 和 00:30
                    time1 = f"{hour:02d}:15"
                    time2 = f"{hour:02d}:30"
                elif minute == 0:
                    # 01:00 对应 00:45 和 01:00
                    if hour == 0:
                        time1 = "23:45"
                        time2 = "24:00"
                    else:
                        time1 = f"{hour-1:02d}:45"
                        time2 = f"{hour:02d}:00"
                
                # 添加两个15分钟数据点
                for time_point in [time1, time2]:
                    all_15min_data.append({
                        '日期': date,
                        '时间': time_point,
                        '电量(MWh)': quarter_hour_energy,
                        '功率(MW)': quarter_hour_power,
                        '原始时间': time_str,
                        '原始电量': half_hour_energy,
                        '原始功率': half_hour_power
                    })
        
        # 转换为DataFrame并排序
        self.processed_data = pd.DataFrame(all_15min_data)
        
        # 按日期和时间排序
        def time_sort_key(time_str):
            if time_str == "24:00":
                return 24 * 60
            hour, minute = map(int, time_str.split(':'))
            return hour * 60 + minute
        
        self.processed_data['sort_key'] = self.processed_data['时间'].apply(time_sort_key)
        self.processed_data = self.processed_data.sort_values(['日期', 'sort_key']).drop('sort_key', axis=1).reset_index(drop=True)
        
        print(f"✅ 15分钟出力曲线生成完成: {len(self.processed_data)} 个时间点")
        
        return True
    
    def generate_daily_summary(self):
        """
        生成每日统计汇总
        """
        print("\n正在生成每日统计汇总...")
        
        if self.all_data is None:
            print("❌ 请先处理文件")
            return False
        
        # 按日期统计
        daily_stats = []
        
        for date, group in self.all_data.groupby('日期'):
            stats = {
                '日期': date,
                '数据点数': len(group),
                '总电量(MWh)': group['计量电量(MWh)'].sum(),
                '平均电量(MWh)': group['计量电量(MWh)'].mean(),
                '最大电量(MWh)': group['计量电量(MWh)'].max(),
                '最小电量(MWh)': group['计量电量(MWh)'].min(),
                '平均功率(MW)': group['功率(MW)'].mean(),
                '最大功率(MW)': group['功率(MW)'].max(),
                '最小功率(MW)': group['功率(MW)'].min(),
                '功率标准差(MW)': group['功率(MW)'].std(),
                '最大出清电价(元/MWh)': group['出清电价(元/MWh)'].max(),
                '最小出清电价(元/MWh)': group['出清电价(元/MWh)'].min(),
                '平均出清电价(元/MWh)': group['出清电价(元/MWh)'].mean()
            }
            daily_stats.append(stats)
        
        self.daily_summary = pd.DataFrame(daily_stats)
        
        print(f"✅ 每日统计汇总完成: {len(self.daily_summary)} 天")

        return True

    def save_results(self, output_file="中栋电厂7月1-14日实时电能汇总分析.xlsx"):
        """
        保存所有处理结果
        """
        print(f"\n正在保存结果...")

        if self.all_data is None:
            print("❌ 没有数据可保存")
            return False

        try:
            output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"

            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 1. 汇总数据 - 所有日期的半小时数据
                summary_data = self.all_data[['日期', '时间', '计量电量(MWh)', '功率(MW)', '出清电价(元/MWh)']].copy()
                summary_data.to_excel(writer, sheet_name='汇总数据', index=False)

                # 2. 15分钟出力曲线汇总
                if self.processed_data is not None:
                    curve_data = self.processed_data[['日期', '时间', '电量(MWh)', '功率(MW)']].copy()
                    curve_data.to_excel(writer, sheet_name='15分钟出力曲线汇总', index=False)

                # 3. 每日统计汇总
                if self.daily_summary is not None:
                    self.daily_summary.to_excel(writer, sheet_name='每日统计汇总', index=False)

                # 4. 按日期分表 - 半小时数据
                for date in sorted(self.all_data['日期'].unique()):
                    daily_data = self.all_data[self.all_data['日期'] == date].copy()
                    daily_data = daily_data[['时间', '计量电量(MWh)', '功率(MW)', '出清电价(元/MWh)']]
                    sheet_name = f"半小时数据_{date[5:]}"  # 07-01格式
                    daily_data.to_excel(writer, sheet_name=sheet_name, index=False)

                # 5. 按日期分表 - 15分钟数据
                if self.processed_data is not None:
                    for date in sorted(self.processed_data['日期'].unique()):
                        daily_15min = self.processed_data[self.processed_data['日期'] == date].copy()
                        daily_15min = daily_15min[['时间', '电量(MWh)', '功率(MW)']]
                        sheet_name = f"15分钟数据_{date[5:]}"  # 07-01格式
                        daily_15min.to_excel(writer, sheet_name=sheet_name, index=False)

                # 6. 按您要求的格式 - 每日分表
                if self.processed_data is not None:
                    for date in sorted(self.processed_data['日期'].unique()):
                        daily_15min = self.processed_data[self.processed_data['日期'] == date].copy()

                        # 按您的格式创建数据
                        format_data = []
                        for _, row in daily_15min.iterrows():
                            format_data.append({
                                f'{date[5:]}实时出力_时间': row['时间'],
                                f'{date[5:]}实时出力_MWH': f"{row['电量(MWh)']:.4f}",
                                f'{date[5:]}实时出力_MW_时间': row['时间'],
                                f'{date[5:]}实时出力_MW': f"{row['功率(MW)']:.4f}",
                                '总功率=总电量/总时长': f"{row['功率(MW)']:.4f}"
                            })

                        format_df = pd.DataFrame(format_data)
                        sheet_name = f"格式_{date[5:]}"  # 格式_07-01
                        format_df.to_excel(writer, sheet_name=sheet_name, index=False)

                # 7. 整体统计分析
                overall_stats = {
                    '项目': [
                        '处理日期数', '总数据点数', '总电量(MWh)', '平均功率(MW)',
                        '最大功率(MW)', '最小功率(MW)', '功率标准差(MW)',
                        '最高出清电价(元/MWh)', '最低出清电价(元/MWh)', '平均出清电价(元/MWh)'
                    ],
                    '数值': [
                        f"{self.all_data['日期'].nunique()} 天",
                        f"{len(self.all_data)} 个",
                        f"{self.all_data['计量电量(MWh)'].sum():.2f} MWh",
                        f"{self.all_data['功率(MW)'].mean():.2f} MW",
                        f"{self.all_data['功率(MW)'].max():.2f} MW",
                        f"{self.all_data['功率(MW)'].min():.2f} MW",
                        f"{self.all_data['功率(MW)'].std():.2f} MW",
                        f"{self.all_data['出清电价(元/MWh)'].max():.2f} 元/MWh",
                        f"{self.all_data['出清电价(元/MWh)'].min():.2f} 元/MWh",
                        f"{self.all_data['出清电价(元/MWh)'].mean():.2f} 元/MWh"
                    ]
                }
                overall_df = pd.DataFrame(overall_stats)
                overall_df.to_excel(writer, sheet_name='整体统计', index=False)

            print(f"✅ 结果已保存到: {output_path}")

            # 显示保存的工作表信息
            print(f"\n📊 保存的工作表:")
            print(f"   汇总数据: {len(self.all_data)} 条半小时数据")
            if self.processed_data is not None:
                print(f"   15分钟出力曲线汇总: {len(self.processed_data)} 个时间点")
            if self.daily_summary is not None:
                print(f"   每日统计汇总: {len(self.daily_summary)} 天")
            print(f"   按日期分表: {self.all_data['日期'].nunique()} 天的半小时和15分钟数据")
            print(f"   按要求格式: {self.all_data['日期'].nunique()} 天的格式化数据")

            return True

        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False

def main():
    """
    主函数
    """
    print("中栋电厂批量现货结算表处理工具")
    print("处理7月1号到7月14号的所有现货结算表")
    print("="*60)

    processor = ZhongdongBatchProcessor()

    # 1. 处理所有文件
    if not processor.process_all_files():
        return

    # 2. 生成15分钟出力曲线
    if not processor.generate_15min_curves():
        return

    # 3. 生成每日统计汇总
    if not processor.generate_daily_summary():
        return

    # 4. 保存结果
    if processor.save_results():
        print(f"\n🎉 批量处理完成！")
        print(f"📊 成功处理 {processor.all_data['日期'].nunique()} 天的现货结算表")
        print(f"⚡ 提取了 {len(processor.all_data)} 条半小时实时电能数据")
        print(f"📈 生成了 {len(processor.processed_data)} 个15分钟时间点")
        print(f"📁 结果已按日期分表保存到Excel文件")
    else:
        print(f"\n❌ 处理失败！")

if __name__ == "__main__":
    main()
