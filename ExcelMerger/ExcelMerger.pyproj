<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>6c998d3e-11ad-4b4e-881a-be5e9c3493bd</ProjectGuid>
    <ProjectHome>.</ProjectHome>
    <StartupFile>excel_merger.py</StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <Name>ExcelMerger</Name>
    <RootNamespace>ExcelMerger</RootNamespace>
    <InterpreterId>MSBuild|env|$(MSBuildProjectDirectory)\venv</InterpreterId>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="excel_merger.py" />
    <Compile Include="excel_merger_interactive.py" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="add_company_names.py" />
    <Content Include="advanced_company_power_model.py" />
    <Content Include="batch_household_prediction.py" />
    <Content Include="company_power_prediction_model.py" />
    <Content Include="comprehensive_weather_holiday_analysis.py" />
    <Content Include="consecutive_weather_filter.py" />
    <Content Include="corrected_specific_data_processor.py" />
    <Content Include="corrected_weekday_weather_formatter.py" />
    <Content Include="debug_data_processor.py" />
    <Content Include="direct_original_file_processor.py" />
    <Content Include="enhanced_precision_model.py" />
    <Content Include="enhanced_temperature_generator.py" />
    <Content Include="enhanced_weekday_weather_formatter.py" />
    <Content Include="excel_merger_batch.py" />
    <Content Include="final_precision_model.py" />
    <Content Include="final_weekday_weather_formatter.py" />
    <Content Include="historical_weather_enhancer.py" />
    <Content Include="historical_weather_scraper.py" />
    <Content Include="household_weather_merger.py" />
    <Content Include="improved_power_prediction.py" />
    <Content Include="improved_prediction_analysis.py" />
    <Content Include="improved_prediction_model.py" />
    <Content Include="improved_weather_analysis.py" />
    <Content Include="interactive_weather_filter.py" />
    <Content Include="july17_weather_scraper.py" />
    <Content Include="july_detailed_weather_scraper.py" />
    <Content Include="july_prediction_analyzer.py" />
    <Content Include="june_weather_scraper.py" />
    <Content Include="linear_analysis.py" />
    <Content Include="merge_customer_region_0714.py" />
    <Content Include="merge_july_weather.py" />
    <Content Include="merge_weather_final.py" />
    <Content Include="merge_weather_to_target.py" />
    <Content Include="NewFile1.py" />
    <Content Include="power_curve_final.py" />
    <Content Include="power_curve_processor.py" />
    <Content Include="power_curve_processor_fixed.py" />
    <Content Include="power_sales_prediction_analysis.py" />
    <Content Include="precision_prediction_model.py" />
    <Content Include="predict_by_date_structure.py" />
    <Content Include="predict_july17_complete.py" />
    <Content Include="predict_july18_structure.py" />
    <Content Include="predict_with_real_weather.py" />
    <Content Include="realistic_weather_enhancer.py" />
    <Content Include="real_tianqi24_scraper.py" />
    <Content Include="real_weather_scraper.py" />
    <Content Include="remove_temperature_columns.py" />
    <Content Include="requirements.txt" />
    <Content Include="README.md" />
    <Content Include="run_ultimate_prediction.py" />
    <Content Include="shandong_temperature_accumulation_analyzer.py" />
    <Content Include="simple_improved_prediction.py" />
    <Content Include="simple_weekday_formatter.py" />
    <Content Include="single_household_prediction.py" />
    <Content Include="smart_error_correction.py" />
    <Content Include="spearman_weather_analysis.py" />
    <Content Include="temperature_data_scraper.py" />
    <Content Include="ultimate_prediction_model.py" />
    <Content Include="ultra_precision_model.py" />
    <Content Include="ventusky_anti_crawler_breaker.py" />
    <Content Include="ventusky_comprehensive_scraper.py" />
    <Content Include="ventusky_detailed_scraper.py" />
    <Content Include="ventusky_simulator.py" />
    <Content Include="ventusky_ultimate_breaker.py" />
    <Content Include="ventusky_weather_scraper.py" />
    <Content Include="weather_analysis.py" />
    <Content Include="weather_enhancer.py" />
    <Content Include="weather_enhancer_alternative.py" />
    <Content Include="weather_enhancer_realistic.py" />
    <Content Include="weather_enhancer_with_api.py" />
    <Content Include="weather_holiday_analysis.py" />
    <Content Include="weather_merger.py" />
    <Content Include="weather_replacer.py" />
    <Content Include="weekday_weather_formatter.py" />
    <Content Include="weekday_weather_grouper.py" />
    <Content Include="workday_splitter.py" />
    <Content Include="zhejiang_july22_scraper.py" />
    <Content Include="zhongdong_batch_processor.py" />
    <Content Include="zhongdong_complete_48_data_processor.py" />
    <Content Include="zhongdong_complete_data_processor.py" />
    <Content Include="zhongdong_complete_full_processor.py" />
    <Content Include="zhongdong_realtime_power.py" />
    <Content Include="zhongdong_real_data_processor.py" />
    <Content Include="zhongdong_simple_save.py" />
    <Content Include="天气API使用说明.md" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
  <!-- Uncomment the CoreCompile target to enable the Build command in
       Visual Studio and specify your pre- and post-build commands in
       the BeforeBuild and AfterBuild targets below. -->
  <!--<Target Name="CoreCompile" />-->
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
</Project>
