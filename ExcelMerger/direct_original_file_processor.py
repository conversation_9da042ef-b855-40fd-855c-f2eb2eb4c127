#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接处理原始文件：城市售电表（含气温+日期类型+天气）.xlsx
将同一周天相同天气的数据整理并合并到目标文件的四个分表中

格式要求：
日期	        2025-06-23						
日期类型	    星期一	星期二	星期三	星期四	星期五	星期六	星期日
天气	        雨	    雨	    雨	    雨	    雨	    雨	    雨
用电量	        57032.93 45123.45 67890.12 54321.98 43210.87 76543.21 65432.10
具体日期	    2025-06-23 2025-06-24 2025-06-25 2025-06-26 2025-06-27 2025-06-28 2025-06-29
具体用电量	    57032.93 45123.45 67890.12 54321.98 43210.87 76543.21 65432.10
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from collections import defaultdict
from openpyxl import load_workbook

class DirectOriginalFileProcessor:
    def __init__(self):
        """
        初始化处理器
        """
        self.source_data = None
        self.target_workbook = None
        self.processed_data = {}
        
        # 中文周天映射
        self.weekday_names = {
            0: '星期一',
            1: '星期二', 
            2: '星期三',
            3: '星期四',
            4: '星期五',
            5: '星期六',
            6: '星期日'
        }
    
    def load_original_data(self, source_file):
        """
        加载原始数据文件
        """
        try:
            print(f"正在读取原始数据文件: {os.path.basename(source_file)}")
            self.source_data = pd.read_excel(source_file)
            print(f"原始数据读取成功，共 {len(self.source_data)} 行数据")
            print(f"数据列: {list(self.source_data.columns)}")
            
            return True
            
        except Exception as e:
            print(f"原始数据加载失败: {e}")
            return False
    
    def load_target_file(self, target_file):
        """
        加载目标Excel文件
        """
        try:
            print(f"正在读取目标文件: {os.path.basename(target_file)}")
            self.target_workbook = load_workbook(target_file)
            print(f"发现工作表: {self.target_workbook.sheetnames}")
            
            return True
            
        except Exception as e:
            print(f"目标文件加载失败: {e}")
            return False
    
    def preprocess_original_data(self):
        """
        预处理原始数据
        """
        try:
            print(f"\n=== 预处理原始数据 ===")
            
            # 确定时间列
            time_column = None
            if '日期' in self.source_data.columns:
                time_column = '日期'
            elif '时间' in self.source_data.columns:
                time_column = '时间'
            else:
                raise ValueError("未找到日期或时间列")
            
            # 确定天气列
            weather_column = None
            if '天气' in self.source_data.columns:
                weather_column = '天气'
            elif '天气状况' in self.source_data.columns:
                weather_column = '天气状况'
            else:
                raise ValueError("未找到天气列")
            
            # 确定用电量列
            power_column = None
            power_columns = [col for col in self.source_data.columns if any(keyword in col for keyword in ['电量', 'kWh'])]
            if power_columns:
                power_column = power_columns[0]
            else:
                raise ValueError("未找到用电量列")
            
            print(f"使用时间列: {time_column}")
            print(f"使用天气列: {weather_column}")
            print(f"使用用电量列: {power_column}")
            
            # 转换时间列并添加周天信息
            self.source_data['时间_处理'] = pd.to_datetime(self.source_data[time_column])
            self.source_data['日期_处理'] = self.source_data['时间_处理'].dt.date
            self.source_data['周天数字'] = self.source_data['时间_处理'].dt.weekday
            self.source_data['周天'] = self.source_data['周天数字'].map(self.weekday_names)
            
            # 按地区、天气、周天分组处理
            self.processed_data = {}
            
            for region in self.source_data['地区'].unique():
                region_data = self.source_data[self.source_data['地区'] == region]
                self.processed_data[region] = {}
                
                # 按天气类型分组
                for weather in region_data[weather_column].unique():
                    weather_data = region_data[region_data[weather_column] == weather]
                    
                    # 按周天收集数据
                    weekday_info = {}
                    for _, row in weather_data.iterrows():
                        weekday = row['周天']
                        date = row['日期_处理']
                        power = row[power_column]
                        
                        if weekday not in weekday_info:
                            weekday_info[weekday] = {
                                '日期列表': [],
                                '用电量列表': []
                            }
                        
                        weekday_info[weekday]['日期列表'].append(date)
                        weekday_info[weekday]['用电量列表'].append(power)
                    
                    # 处理每个周天的数据
                    processed_weekdays = {}
                    for weekday, info in weekday_info.items():
                        # 按日期排序
                        combined = list(zip(info['日期列表'], info['用电量列表']))
                        combined.sort(key=lambda x: x[0])
                        
                        if combined:
                            dates, powers = zip(*combined)
                            processed_weekdays[weekday] = {
                                '总用电量': sum(powers),
                                '具体日期': dates[0],  # 最早日期
                                '具体用电量': powers[0] if len(powers) == 1 else sum(powers),
                                '所有记录': combined
                            }
                    
                    if processed_weekdays:  # 只有当有数据时才添加
                        self.processed_data[region][weather] = {
                            '周天数据': processed_weekdays,
                            '代表日期': weather_data['时间_处理'].iloc[0].date()
                        }
            
            print(f"数据预处理完成，共处理 {len(self.processed_data)} 个地区")
            for region, data in self.processed_data.items():
                print(f"  {region}: {len(data)} 种天气类型")
            
            return True
            
        except Exception as e:
            print(f"数据预处理失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def add_formatted_data_to_sheet(self, sheet_name, region_data):
        """
        将格式化数据添加到指定工作表
        """
        try:
            if sheet_name not in self.target_workbook.sheetnames:
                print(f"警告: 工作表 {sheet_name} 不存在")
                return False
            
            worksheet = self.target_workbook[sheet_name]
            last_row = worksheet.max_row
            current_row = last_row + 2
            
            print(f"  在工作表 {sheet_name} 的第 {current_row} 行开始添加数据")
            
            # 添加标题
            worksheet.cell(row=current_row, column=1, value="=== 同一周天相同天气数据汇总 ===")
            current_row += 2
            
            weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
            
            # 为每种天气类型添加数据
            for weather_type, weather_info in region_data.items():
                weekday_data = weather_info['周天数据']
                
                print(f"    添加天气类型: {weather_type}")
                
                # 1. 日期行
                worksheet.cell(row=current_row, column=1, value="日期")
                worksheet.cell(row=current_row, column=2, value=weather_info['代表日期'].strftime('%Y-%m-%d'))
                current_row += 1
                
                # 2. 日期类型行（周天标题）
                worksheet.cell(row=current_row, column=1, value="日期类型")
                for i, weekday in enumerate(weekdays, 2):
                    worksheet.cell(row=current_row, column=i, value=weekday)
                current_row += 1
                
                # 3. 天气行
                worksheet.cell(row=current_row, column=1, value="天气")
                for i, weekday in enumerate(weekdays, 2):
                    worksheet.cell(row=current_row, column=i, value=weather_type)
                current_row += 1
                
                # 4. 用电量行
                worksheet.cell(row=current_row, column=1, value="用电量")
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weekday_data:
                        power = weekday_data[weekday]['总用电量']
                        worksheet.cell(row=current_row, column=i, value=round(power, 2))
                current_row += 1
                
                # 5. 具体日期行
                worksheet.cell(row=current_row, column=1, value="具体日期")
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weekday_data:
                        date = weekday_data[weekday]['具体日期']
                        worksheet.cell(row=current_row, column=i, value=date.strftime('%Y-%m-%d'))
                current_row += 1
                
                # 6. 具体用电量行
                worksheet.cell(row=current_row, column=1, value="具体用电量")
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weekday_data:
                        power = weekday_data[weekday]['具体用电量']
                        worksheet.cell(row=current_row, column=i, value=round(power, 2))
                current_row += 1
                
                current_row += 1  # 空行分隔
            
            print(f"  成功添加 {len(region_data)} 种天气类型的数据到工作表 {sheet_name}")
            return True
            
        except Exception as e:
            print(f"添加数据到工作表 {sheet_name} 失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def process_all_regions(self):
        """
        处理所有地区的数据
        """
        regions = ['杭州', '金华', '宁波', '台州', '衢州']
        
        for region in regions:
            print(f"\n处理地区: {region}")
            
            if region in self.processed_data and self.processed_data[region]:
                region_data = self.processed_data[region]
                self.add_formatted_data_to_sheet(region, region_data)
            else:
                print(f"  警告: 未找到地区 {region} 的数据")
    
    def save_result(self, output_file):
        """
        保存结果文件
        """
        try:
            self.target_workbook.save(output_file)
            print(f"\n✅ 处理完成！结果已保存到: {output_file}")
            return True
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False
    
    def process(self, source_file, target_file, output_file):
        """
        完整的处理流程
        """
        print(f"直接处理原始文件：城市售电表数据格式化工具")
        print(f"=" * 80)
        print(f"原始数据文件: {source_file}")
        print(f"目标文件: {target_file}")
        print(f"输出文件: {output_file}")
        
        # 执行处理步骤
        if not self.load_original_data(source_file):
            return False
        
        if not self.load_target_file(target_file):
            return False
        
        if not self.preprocess_original_data():
            return False
        
        self.process_all_regions()
        
        if not self.save_result(output_file):
            return False
        
        return True

def main():
    """主函数"""
    # 原始数据文件
    source_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/城市售电表（含气温+日期类型+天气）.xlsx"
    
    # 目标文件
    target_file = "/Users/<USER>/Desktop/副本城市售电表（含气温+日期类型+天气）(1).xlsx"
    
    # 输出文件
    output_file = "直接处理_副本城市售电表_含周天天气汇总.xlsx"
    
    if not os.path.exists(source_file):
        print(f"错误: 原始数据文件不存在 - {source_file}")
        return
    
    if not os.path.exists(target_file):
        print(f"错误: 目标文件不存在 - {target_file}")
        return
    
    processor = DirectOriginalFileProcessor()
    success = processor.process(source_file, target_file, output_file)
    
    if success:
        print(f"\n🎉 任务完成！")
        print(f"已成功将原始文件中同一周天相同天气的数据按指定格式添加到各个地区分表中")
        print(f"文件保存位置: {output_file}")

if __name__ == "__main__":
    main()
