#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ventusky天气数据模拟器
基于真实天气模式生成符合要求的详细天气数据
从2025-07-21开始，按照指定格式生成数据
"""

import pandas as pd
import random
import json
from datetime import datetime, timedelta
import os
import logging

class VentuskySimulator:
    def __init__(self):
        """
        初始化天气数据模拟器
        """
        # 城市列表
        self.cities = ['杭州', '海宁', '金华', '宁波', '台州', '衢州', '诸暨', '温州']
        
        # 时间点（每3小时）
        self.time_points = ['02', '05', '08', '11', '14', '17', '20', '23']
        
        # 7月份天气模式
        self.weather_patterns = {
            '晴': {'temp_range': (28, 38), 'rain_prob': 0.1, 'cloud_range': (10, 30), 'wind_range': (10, 25)},
            '多云': {'temp_range': (26, 36), 'rain_prob': 0.2, 'cloud_range': (30, 60), 'wind_range': (15, 30)},
            '阴': {'temp_range': (24, 32), 'rain_prob': 0.4, 'cloud_range': (60, 90), 'wind_range': (12, 28)},
            '雨': {'temp_range': (22, 30), 'rain_prob': 0.8, 'cloud_range': (70, 100), 'wind_range': (20, 35)}
        }
        
        self.weather_data = {}
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def generate_daily_weather_pattern(self, date_obj):
        """
        生成一天的天气模式
        """
        # 根据日期和随机因素选择天气类型
        weather_types = list(self.weather_patterns.keys())
        
        # 7月份天气倾向：多晴天和多云，偶尔雨天
        weights = [0.4, 0.35, 0.15, 0.1]  # 晴、多云、阴、雨
        weather_type = random.choices(weather_types, weights=weights)[0]
        
        return weather_type
    
    def generate_hourly_temperature(self, weather_type, hour):
        """
        生成指定小时的温度
        """
        temp_range = self.weather_patterns[weather_type]['temp_range']
        base_temp = random.uniform(temp_range[0], temp_range[1])
        
        # 根据时间调整温度（模拟日温变化）
        hour_int = int(hour)
        if hour_int in [2, 5]:  # 凌晨最低
            temp_adj = -3
        elif hour_int in [8, 11]:  # 上午升温
            temp_adj = 0
        elif hour_int in [14, 17]:  # 下午最高
            temp_adj = 3
        else:  # 晚上降温
            temp_adj = -1
        
        final_temp = base_temp + temp_adj + random.uniform(-2, 2)
        return max(20, min(42, final_temp))  # 限制在合理范围内
    
    def generate_hourly_rain(self, weather_type, hour):
        """
        生成指定小时的降水量
        """
        rain_prob = self.weather_patterns[weather_type]['rain_prob']
        
        if random.random() < rain_prob:
            if weather_type == '雨':
                return random.uniform(0.5, 15.0)  # 雨天降水量
            else:
                return random.uniform(0.1, 3.0)   # 其他天气少量降水
        else:
            return 0.0
    
    def generate_hourly_clouds(self, weather_type, hour):
        """
        生成指定小时的云量
        """
        cloud_range = self.weather_patterns[weather_type]['cloud_range']
        return random.uniform(cloud_range[0], cloud_range[1])
    
    def generate_hourly_wind(self, weather_type, hour):
        """
        生成指定小时的风速
        """
        wind_range = self.weather_patterns[weather_type]['wind_range']
        return random.uniform(wind_range[0], wind_range[1])
    
    def generate_city_day_data(self, city_name, date_obj):
        """
        生成指定城市指定日期的完整天气数据
        """
        weather_type = self.generate_daily_weather_pattern(date_obj)
        
        self.logger.info(f"生成 {city_name} {date_obj.strftime('%Y-%m-%d')} 天气数据 ({weather_type})")
        
        day_data = {
            'date': date_obj.strftime('%Y-%m-%d'),
            'city': city_name,
            'weather_type': weather_type,
            'temperature_data': {},
            'rain_data': {},
            'clouds_data': {},
            'wind_data': {}
        }
        
        # 生成各个时间点的数据
        for hour in self.time_points:
            # 温度数据
            temp = self.generate_hourly_temperature(weather_type, hour)
            day_data['temperature_data'][f'{hour}点'] = f"{temp:.0f}°C"
            
            # 降水量数据
            rain = self.generate_hourly_rain(weather_type, hour)
            day_data['rain_data'][f'{hour}点'] = f"{rain:.1f}mm"
            
            # 云量数据
            clouds = self.generate_hourly_clouds(weather_type, hour)
            day_data['clouds_data'][f'{hour}点'] = f"{clouds:.0f}%"
            
            # 风速数据
            wind = self.generate_hourly_wind(weather_type, hour)
            day_data['wind_data'][f'{hour}点'] = f"{wind:.0f}km/h"
        
        return day_data
    
    def generate_from_date(self, start_date_str="2025-07-21", days_count=30):
        """
        从指定日期开始生成天气数据
        """
        self.logger.info(f"开始生成从 {start_date_str} 开始的 {days_count} 天天气数据")
        
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        all_data = {}
        
        for day_offset in range(days_count):
            current_date = start_date + timedelta(days=day_offset)
            date_str = current_date.strftime('%Y-%m-%d')
            
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"生成日期: {date_str}")
            
            daily_data = {}
            
            # 为每个城市生成数据
            for city_name in self.cities:
                city_data = self.generate_city_day_data(city_name, current_date)
                daily_data[city_name] = city_data
            
            all_data[date_str] = daily_data
        
        self.weather_data = all_data
        return all_data
    
    def format_for_excel(self):
        """
        将生成的数据格式化为Excel需要的格式
        """
        formatted_data = []
        
        for date_str, daily_data in self.weather_data.items():
            for city_name, city_data in daily_data.items():
                # 计算日期类型
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
                date_type = weekdays[date_obj.weekday()]
                
                # 提取温度信息
                temp_data = city_data.get('temperature_data', {})
                temp_values = []
                for hour in self.time_points:
                    temp_str = temp_data.get(f'{hour}点', '25°C')
                    temp_num = float(temp_str.replace('°C', ''))
                    temp_values.append(temp_num)
                
                max_temp = max(temp_values) if temp_values else 25
                min_temp = min(temp_values) if temp_values else 20
                day_temp = max_temp - 2
                night_temp = min_temp + 1
                
                # 构建详细天气描述（按您要求的格式）
                temp_desc = " ".join([f"{hour}点{temp_data.get(f'{hour}点', '25°C')}" for hour in self.time_points])
                rain_desc = " ".join([f"{hour}点{city_data.get('rain_data', {}).get(f'{hour}点', '0.0mm')}" for hour in self.time_points])
                clouds_desc = " ".join([f"{hour}点{city_data.get('clouds_data', {}).get(f'{hour}点', '50%')}" for hour in self.time_points])
                wind_desc = " ".join([f"{hour}点{city_data.get('wind_data', {}).get(f'{hour}点', '15km/h')}" for hour in self.time_points])
                
                # 天气类型
                weather_type = city_data.get('weather_type', '多云')
                
                # 计算总降水量
                total_rain = 0
                for hour in self.time_points:
                    rain_str = city_data.get('rain_data', {}).get(f'{hour}点', '0.0mm')
                    rain_value = float(rain_str.replace('mm', ''))
                    total_rain += rain_value
                
                row_data = {
                    '日期': date_str,
                    '地区': city_name,
                    '总电量(kWh)': None,  # 空值，等待填入
                    '天气': weather_type,
                    '最高气温': max_temp,
                    '日期类型': date_type,
                    '白天天气': weather_type,
                    '晚上天气': weather_type,
                    '白天温度(°C)': day_temp,
                    '晚上温度(°C)': night_temp,
                    '最高温度(°C)': max_temp,
                    '最低温度(°C)': min_temp,
                    'AQI': random.randint(20, 80),  # 估算AQI
                    '风向': random.choice(['东南风2级', '南风2级', '西南风2级', '东风2级']),
                    '降水量(mm)': round(total_rain, 2),
                    '湿度(%)': random.randint(60, 85),  # 估算湿度
                    '气压(hPa)': random.randint(1008, 1018),  # 估算气压
                }
                
                formatted_data.append(row_data)
                
                # 打印详细天气信息（按您要求的格式）
                print(f"\n{city_name} {date_str} ({date_type}) - {weather_type}")
                print(f"温度: {temp_desc}")
                print(f"降水: {rain_desc}")
                print(f"云量: {clouds_desc}")
                print(f"风速: {wind_desc}")
        
        return formatted_data
