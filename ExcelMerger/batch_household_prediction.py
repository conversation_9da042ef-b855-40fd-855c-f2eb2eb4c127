#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量户号用电量预测工具
读取110kV以下用户历史用电量信息，爬取天气数据，预测各户号用电量
"""

import pandas as pd
import numpy as np
import requests
from bs4 import BeautifulSoup
import re
import time
import random
from datetime import datetime
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.linear_model import Ridge
import os

class BatchHouseholdPredictor:
    def __init__(self):
        """
        初始化批量户号预测器
        """
        self.new_data = None
        self.training_data = None
        self.model = None
        self.scaler = None
        self.label_encoders = {}
        self.feature_columns = []
        self.weather_data = {}
        
        # 城市URL映射
        self.city_url_map = {
            '衢州': 'quzhou', '诸暨': 'zhuji', '温州': 'wenzhou', '杭州': 'hangzhou',
            '宁波': 'ningbo', '嘉兴': 'jiaxing', '湖州': 'huzhou', '绍兴': 'shaoxing',
            '金华': 'jinhua', '台州': 'taizhou', '丽水': 'lishui', '海宁': 'haining'
        }
        
        # 户号到地区的映射（基于历史数据）
        self.household_to_region = {}
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://www.tianqi24.com/',
        }
    
    def load_new_data(self, file_path):
        """
        加载新的用电量数据文件
        """
        print("正在加载新的用电量数据...")
        
        try:
            self.new_data = pd.read_excel(file_path)
            print(f"✅ 成功加载新数据: {len(self.new_data)} 条记录")
            
            # 数据预处理
            self.new_data['时间'] = pd.to_datetime(self.new_data['时间'])
            self.new_data['户号'] = self.new_data['户号'].astype(str)
            
            print(f"   户号数量: {self.new_data['户号'].nunique()}")
            print(f"   日期: {self.new_data['时间'].dt.date.unique()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载新数据失败: {e}")
            return False
    
    def load_training_data(self):
        """
        加载训练数据
        """
        print("正在加载训练数据...")
        
        training_file = "/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx"
        if not os.path.exists(training_file):
            print(f"❌ 训练数据文件不存在: {training_file}")
            return False
        
        try:
            self.training_data = pd.read_excel(training_file, sheet_name='完整数据')
            print(f"✅ 成功加载训练数据: {len(self.training_data)} 条记录")
            
            # 建立户号到地区的映射
            household_region_map = self.training_data.groupby('户号')['地区'].first().to_dict()
            self.household_to_region = {str(int(k)): v for k, v in household_region_map.items()}
            
            print(f"   建立户号-地区映射: {len(self.household_to_region)} 个")
            
            # 数据预处理
            self.training_data['时间'] = pd.to_datetime(self.training_data['时间'])
            self.training_data['年'] = self.training_data['时间'].dt.year
            self.training_data['月'] = self.training_data['时间'].dt.month
            self.training_data['日'] = self.training_data['时间'].dt.day
            self.training_data['星期'] = self.training_data['时间'].dt.dayofweek
            self.training_data['是否周末'] = (self.training_data['星期'] >= 5).astype(int)
            
            return True
            
        except Exception as e:
            print(f"❌ 加载训练数据失败: {e}")
            return False
    
    def get_region_for_household(self, household_id):
        """
        获取户号对应的地区
        """
        household_str = str(int(float(household_id)))
        return self.household_to_region.get(household_str, '未知')
    
    def scrape_weather_data(self, target_date="2025-07-17"):
        """
        爬取所有需要的城市天气数据
        """
        print(f"\n正在爬取 {target_date} 的天气数据...")
        
        # 获取需要的地区列表
        regions_needed = set()
        for household_id in self.new_data['户号'].unique():
            region = self.get_region_for_household(household_id)
            if region != '未知':
                regions_needed.add(region)
        
        print(f"需要爬取的地区: {list(regions_needed)}")
        
        success_count = 0
        for region in regions_needed:
            if region in self.city_url_map:
                print(f"\n正在爬取 {region} 的天气数据...")
                weather_info = self.scrape_city_weather(region, target_date)
                
                if weather_info:
                    self.weather_data[region] = weather_info
                    success_count += 1
                    print(f"  ✅ {region}: 成功获取天气数据")
                    
                    # 显示天气信息
                    print(f"     温度: {weather_info['temp_min']}°C - {weather_info['temp_max']}°C")
                    print(f"     天气: {weather_info['weather']}")
                    print(f"     AQI: {weather_info['aqi']}")
                    print(f"     降水: {weather_info['precipitation']} mm")
                else:
                    print(f"  ❌ {region}: 获取失败")
                
                # 请求间隔
                time.sleep(random.uniform(2, 4))
        
        print(f"\n✅ 天气数据爬取完成: {success_count}/{len(regions_needed)} 个地区")
        return success_count > 0
    
    def scrape_city_weather(self, city, target_date):
        """
        爬取指定城市的天气数据
        """
        if city not in self.city_url_map:
            return None
        
        # 解析日期
        date_obj = datetime.strptime(target_date, '%Y-%m-%d')
        year = date_obj.year
        month = date_obj.month
        day = date_obj.day
        
        city_url = self.city_url_map[city]
        url = f"https://www.tianqi24.com/{city_url}/history{year}{month:02d}.html"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=15)
            response.encoding = 'utf-8'
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                return self.parse_weather_data(soup, year, month, day)
            else:
                return None
                
        except Exception as e:
            print(f"    爬取失败: {e}")
            return None
    
    def parse_weather_data(self, soup, year, month, day):
        """
        解析天气数据
        """
        weather_lists = soup.find_all('ul', class_='col6')
        
        for ul in weather_lists:
            items = ul.find_all('li')
            for item in items[1:]:  # 跳过表头
                divs = item.find_all('div')
                if len(divs) >= 7:
                    try:
                        date_text = divs[0].get_text().strip()
                        date_match = re.search(r'(\d{2})-(\d{2})', date_text)
                        
                        if date_match:
                            month_part = int(date_match.group(1))
                            day_part = int(date_match.group(2))
                            
                            if month_part == month and day_part == day:
                                # 解析天气信息
                                weather_text = divs[1].get_text().strip()
                                high_temp = self.extract_temperature(divs[2].get_text().strip())
                                low_temp = self.extract_temperature(divs[3].get_text().strip())
                                aqi = self.extract_number(divs[4].get_text().strip())
                                wind_text = divs[5].get_text().strip()
                                precipitation = self.extract_precipitation(divs[6].get_text().strip())
                                
                                return {
                                    'temp_max': high_temp,
                                    'temp_min': low_temp,
                                    'weather': weather_text.replace('\n', '').replace('/', '/'),
                                    'aqi': aqi,
                                    'wind_direction': wind_text,
                                    'precipitation': precipitation
                                }
                    except:
                        continue
        
        return None
    
    def extract_temperature(self, text):
        """提取温度"""
        match = re.search(r'(\d+)', text)
        return int(match.group(1)) if match else None
    
    def extract_number(self, text):
        """提取数字"""
        match = re.search(r'(\d+)', text)
        return int(match.group(1)) if match else None
    
    def extract_precipitation(self, text):
        """提取降水量"""
        match = re.search(r'(\d+(?:\.\d+)?)', text)
        return float(match.group(1)) if match else 0.0
    
    def prepare_features(self):
        """
        准备特征工程
        """
        print("\n正在进行特征工程...")
        
        # 基础特征
        feature_columns = [
            '年', '月', '日', '星期', '是否周末',
            '最高温度(°C)', '最低温度(°C)', 'AQI', '降水量(mm)', '湿度(%)', '气压(hPa)'
        ]
        
        # 处理分类特征
        categorical_features = ['地区', '公司名称', '白天天气', '晚上天气', '风向', '合同状态', '居间/销售']
        
        for col in categorical_features:
            if col in self.training_data.columns:
                le = LabelEncoder()
                self.training_data[f'{col}_编码'] = le.fit_transform(self.training_data[col].fillna('未知'))
                self.label_encoders[col] = le
                feature_columns.append(f'{col}_编码')
        
        # 创建温度相关特征
        self.training_data['温差'] = self.training_data['最高温度(°C)'] - self.training_data['最低温度(°C)']
        self.training_data['平均温度'] = (self.training_data['最高温度(°C)'] + self.training_data['最低温度(°C)']) / 2
        self.training_data['高温指标'] = (self.training_data['最高温度(°C)'] >= 30).astype(int)
        self.training_data['低温指标'] = (self.training_data['最低温度(°C)'] <= 10).astype(int)
        
        feature_columns.extend(['温差', '平均温度', '高温指标', '低温指标'])
        
        # 创建时间特征
        self.training_data['月份sin'] = np.sin(2 * np.pi * self.training_data['月'] / 12)
        self.training_data['月份cos'] = np.cos(2 * np.pi * self.training_data['月'] / 12)
        self.training_data['日期sin'] = np.sin(2 * np.pi * self.training_data['日'] / 31)
        self.training_data['日期cos'] = np.cos(2 * np.pi * self.training_data['日'] / 31)
        
        feature_columns.extend(['月份sin', '月份cos', '日期sin', '日期cos'])
        
        # 创建滞后特征
        self.training_data = self.training_data.sort_values(['户号', '时间'])
        self.training_data['前一天用电量'] = self.training_data.groupby('户号')['总电量(kWh)'].shift(1)
        self.training_data['前三天平均用电量'] = self.training_data.groupby('户号')['总电量(kWh)'].rolling(window=3).mean().reset_index(0, drop=True)
        
        feature_columns.extend(['前一天用电量', '前三天平均用电量'])
        
        # 移除缺失值
        self.training_data = self.training_data.dropna(subset=feature_columns + ['总电量(kWh)'])
        
        self.feature_columns = feature_columns
        print(f"✅ 特征工程完成: {len(feature_columns)} 个特征")
        return True
    
    def train_model(self):
        """
        训练预测模型
        """
        print("正在训练预测模型...")
        
        X = self.training_data[self.feature_columns]
        y = self.training_data['总电量(kWh)']
        
        # 数据标准化
        self.scaler = StandardScaler()
        X_scaled = self.scaler.fit_transform(X)
        
        # 训练岭回归模型
        self.model = Ridge(alpha=1.0)
        self.model.fit(X_scaled, y)
        
        # 评估模型
        score = self.model.score(X_scaled, y)
        print(f"✅ 模型训练完成，R² = {score:.3f}")
        return True

    def predict_all_households(self, target_date="2025-07-17"):
        """
        预测所有户号的用电量
        """
        print(f"\n正在预测所有户号 {target_date} 的用电量...")

        predictions = []
        success_count = 0

        for idx, row in self.new_data.iterrows():
            household_id = str(int(float(row['户号'])))
            region = self.get_region_for_household(household_id)

            if region == '未知':
                print(f"  ⚠️ 户号 {household_id}: 未找到地区信息，跳过")
                continue

            if region not in self.weather_data:
                print(f"  ⚠️ 户号 {household_id}: 地区 {region} 无天气数据，跳过")
                continue

            # 预测用电量
            predicted_consumption = self.predict_single_household(household_id, region, target_date)

            if predicted_consumption is not None:
                # 按比例分配到各时段
                total_predicted = predicted_consumption

                # 基于历史比例分配（如果有历史数据）
                household_history = self.training_data[self.training_data['户号'] == int(float(household_id))]

                if not household_history.empty:
                    # 使用历史平均比例
                    total_avg = household_history['总电量(kWh)'].mean()
                    jian_ratio = household_history['尖电量(kWh)'].mean() / total_avg if total_avg > 0 else 0.15
                    feng_ratio = household_history['峰电量(kWh)'].mean() / total_avg if total_avg > 0 else 0.35
                    ping_ratio = household_history['平电量(kWh)'].mean() / total_avg if total_avg > 0 else 0.25
                    gu_ratio = household_history['谷电量(kWh)'].mean() / total_avg if total_avg > 0 else 0.25
                else:
                    # 使用默认比例
                    jian_ratio, feng_ratio, ping_ratio, gu_ratio = 0.15, 0.35, 0.25, 0.25

                # 确保比例和为1
                total_ratio = jian_ratio + feng_ratio + ping_ratio + gu_ratio
                if total_ratio > 0:
                    jian_ratio /= total_ratio
                    feng_ratio /= total_ratio
                    ping_ratio /= total_ratio
                    gu_ratio /= total_ratio

                prediction = {
                    '表计号': row['表计号'],
                    '户号': int(float(row['户号'])),
                    '时间': target_date,
                    '总电量(kWh)': round(total_predicted, 1),
                    '尖电量(kWh)': round(total_predicted * jian_ratio, 1),
                    '峰电量(kWh)': round(total_predicted * feng_ratio, 1),
                    '平电量(kWh)': round(total_predicted * ping_ratio, 1),
                    '谷电量(kWh)': round(total_predicted * gu_ratio, 1),
                    '地区': region,
                    '天气': self.weather_data[region]['weather'],
                    '温度': f"{self.weather_data[region]['temp_min']}-{self.weather_data[region]['temp_max']}°C"
                }

                predictions.append(prediction)
                success_count += 1

                if success_count % 50 == 0:
                    print(f"  已完成 {success_count} 个户号预测...")

        print(f"✅ 预测完成: {success_count}/{len(self.new_data)} 个户号")
        return predictions

    def predict_single_household(self, household_id, region, target_date):
        """
        预测单个户号的用电量
        """
        try:
            # 获取户号历史数据
            household_history = self.training_data[self.training_data['户号'] == int(float(household_id))]

            if household_history.empty:
                # 如果没有历史数据，使用同地区平均值
                region_data = self.training_data[self.training_data['地区'] == region]
                if not region_data.empty:
                    return region_data['总电量(kWh)'].mean()
                else:
                    return 1000.0  # 默认值

            # 获取天气数据
            weather_info = self.weather_data[region]

            # 构建预测特征
            target_date_obj = datetime.strptime(target_date, '%Y-%m-%d')

            # 获取最新记录作为基础
            latest_record = household_history.iloc[-1]

            # 基础特征
            features = {
                '年': target_date_obj.year,
                '月': target_date_obj.month,
                '日': target_date_obj.day,
                '星期': target_date_obj.weekday(),
                '是否周末': 1 if target_date_obj.weekday() >= 5 else 0,
                '最高温度(°C)': weather_info['temp_max'],
                '最低温度(°C)': weather_info['temp_min'],
                'AQI': weather_info['aqi'],
                '降水量(mm)': weather_info['precipitation'],
                '湿度(%)': 75,  # 估算
                '气压(hPa)': 1013  # 估算
            }

            # 分类特征编码
            categorical_features = ['地区', '公司名称', '白天天气', '晚上天气', '风向', '合同状态', '居间/销售']
            categorical_values = {
                '地区': region,
                '公司名称': latest_record.get('公司名称', '未知'),
                '白天天气': weather_info['weather'].split('/')[0] if '/' in weather_info['weather'] else weather_info['weather'],
                '晚上天气': weather_info['weather'].split('/')[-1] if '/' in weather_info['weather'] else weather_info['weather'],
                '风向': weather_info['wind_direction'],
                '合同状态': latest_record.get('合同状态', '未知'),
                '居间/销售': latest_record.get('居间/销售', '未知')
            }

            for col in categorical_features:
                if col in self.label_encoders:
                    value = categorical_values[col]
                    try:
                        encoded_value = self.label_encoders[col].transform([str(value)])[0]
                    except:
                        encoded_value = 0
                    features[f'{col}_编码'] = encoded_value

            # 温度相关特征
            features['温差'] = features['最高温度(°C)'] - features['最低温度(°C)']
            features['平均温度'] = (features['最高温度(°C)'] + features['最低温度(°C)']) / 2
            features['高温指标'] = 1 if features['最高温度(°C)'] >= 30 else 0
            features['低温指标'] = 1 if features['最低温度(°C)'] <= 10 else 0

            # 时间特征
            features['月份sin'] = np.sin(2 * np.pi * target_date_obj.month / 12)
            features['月份cos'] = np.cos(2 * np.pi * target_date_obj.month / 12)
            features['日期sin'] = np.sin(2 * np.pi * target_date_obj.day / 31)
            features['日期cos'] = np.cos(2 * np.pi * target_date_obj.day / 31)

            # 滞后特征
            recent_consumption = household_history.tail(3)['总电量(kWh)']
            features['前一天用电量'] = recent_consumption.iloc[-1] if len(recent_consumption) > 0 else household_history['总电量(kWh)'].mean()
            features['前三天平均用电量'] = recent_consumption.mean() if len(recent_consumption) >= 3 else household_history['总电量(kWh)'].mean()

            # 构建特征向量
            X_pred = np.array([features[col] for col in self.feature_columns]).reshape(1, -1)
            X_pred_scaled = self.scaler.transform(X_pred)

            # 预测
            predicted_consumption = self.model.predict(X_pred_scaled)[0]
            return max(0, predicted_consumption)  # 确保非负

        except Exception as e:
            print(f"    预测户号 {household_id} 失败: {e}")
            return None

    def save_predictions(self, predictions, output_file="110kV以下用户用电量预测结果_2025-07-17.xlsx"):
        """
        保存预测结果
        """
        print(f"\n正在保存预测结果...")

        try:
            output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"

            # 转换为DataFrame
            predictions_df = pd.DataFrame(predictions)

            # 重新排序列，与原表格式一致
            column_order = ['表计号', '户号', '时间', '总电量(kWh)', '尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']
            main_df = predictions_df[column_order]

            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 主预测结果（与原表格式一致）
                main_df.to_excel(writer, sheet_name='预测结果', index=False)

                # 详细预测结果（包含地区和天气信息）
                predictions_df.to_excel(writer, sheet_name='详细预测结果', index=False)

                # 预测统计
                stats_df = predictions_df.groupby('地区').agg({
                    '户号': 'count',
                    '总电量(kWh)': ['sum', 'mean', 'min', 'max'],
                    '温度': 'first',
                    '天气': 'first'
                }).round(2)
                stats_df.columns = ['户号数量', '总用电量', '平均用电量', '最小用电量', '最大用电量', '温度', '天气']
                stats_df = stats_df.reset_index()
                stats_df.to_excel(writer, sheet_name='地区统计', index=False)

                # 天气信息汇总
                weather_summary = []
                for region, weather_info in self.weather_data.items():
                    weather_summary.append({
                        '地区': region,
                        '最高温度': weather_info['temp_max'],
                        '最低温度': weather_info['temp_min'],
                        '天气': weather_info['weather'],
                        'AQI': weather_info['aqi'],
                        '风向': weather_info['wind_direction'],
                        '降水量': weather_info['precipitation']
                    })

                weather_df = pd.DataFrame(weather_summary)
                weather_df.to_excel(writer, sheet_name='天气信息', index=False)

            print(f"✅ 预测结果已保存到: {output_path}")

            # 显示统计信息
            print(f"\n📊 预测统计:")
            print(f"   预测户号数: {len(predictions)}")
            print(f"   总预测用电量: {predictions_df['总电量(kWh)'].sum():,.1f} kWh")
            print(f"   平均预测用电量: {predictions_df['总电量(kWh)'].mean():.1f} kWh")
            print(f"   涉及地区: {predictions_df['地区'].nunique()} 个")

            return True

        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False

def main():
    """
    主函数
    """
    print("批量户号用电量预测工具")
    print("="*80)

    # 文件路径
    new_data_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250717-20250717 (1).xlsx"

    if not os.path.exists(new_data_file):
        print(f"❌ 新数据文件不存在: {new_data_file}")
        return

    predictor = BatchHouseholdPredictor()

    # 1. 加载数据
    if not predictor.load_new_data(new_data_file):
        return

    if not predictor.load_training_data():
        return

    # 2. 爬取天气数据
    if not predictor.scrape_weather_data("2025-07-18"):
        print("❌ 天气数据爬取失败")
        return

    # 3. 准备特征和训练模型
    if not predictor.prepare_features():
        return

    if not predictor.train_model():
        return

    # 4. 预测所有户号
    predictions = predictor.predict_all_households("2025-07-18")

    if predictions:
        # 5. 保存结果
        predictor.save_predictions(predictions, "110kV以下用户用电量预测结果_2025-07-18.xlsx")

        print(f"\n🎉 批量预测完成！")
        print(f"📊 成功预测 {len(predictions)} 个户号的用电量")
        print(f"🌡️ 爬取了 {len(predictor.weather_data)} 个地区的天气数据")
        print(f"📁 结果已保存到Excel文件")
    else:
        print(f"\n❌ 预测失败！")

if __name__ == "__main__":
    main()
