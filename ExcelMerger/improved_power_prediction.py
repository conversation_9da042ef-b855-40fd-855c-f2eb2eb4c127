#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的用电量预测模型
分析预测偏差并优化模型以减小误差
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class ImprovedPowerPredictor:
    def __init__(self):
        """
        初始化改进的预测器
        """
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.feature_importance = {}
        
        print("改进的用电量预测模型初始化完成")
    
    def analyze_prediction_errors(self):
        """
        分析现有预测的误差模式
        """
        print("\n📊 分析预测误差模式...")
        
        # 读取预测结果和实际数据
        pred_file = '/Users/<USER>/RiderProjects/Solution3/110kV以下用户用电量预测结果_2025-07-17.xlsx'
        actual_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250717-20250717 (1).xlsx'
        
        df_pred = pd.read_excel(pred_file)
        df_actual = pd.read_excel(actual_file)
        
        # 合并数据
        df_compare = pd.merge(df_pred, df_actual, on=['表计号', '户号'], suffixes=('_pred', '_actual'))
        
        print(f"匹配用户数: {len(df_compare)}")
        
        # 分析主要问题
        problems = []
        
        # 1. 尖电量预测问题
        actual_peak_avg = df_compare['尖电量(kWh)_actual'].mean()
        pred_peak_avg = df_compare['尖电量(kWh)_pred'].mean()
        if pred_peak_avg < actual_peak_avg * 0.1:
            problems.append("尖电量预测严重偏低")
        
        # 2. 平电量预测问题
        actual_flat_avg = df_compare['平电量(kWh)_actual'].mean()
        pred_flat_avg = df_compare['平电量(kWh)_pred'].mean()
        if pred_flat_avg > actual_flat_avg * 1.5:
            problems.append("平电量预测偏高")
        
        # 3. 零值问题
        zero_actual = len(df_compare[df_compare['总电量(kWh)_actual'] == 0])
        if zero_actual > 0:
            problems.append(f"存在{zero_actual}个实际用电量为0的异常数据")
        
        print(f"\n🔍 发现的主要问题:")
        for i, problem in enumerate(problems, 1):
            print(f"  {i}. {problem}")
        
        return df_compare, problems
    
    def load_and_prepare_training_data(self):
        """
        加载和准备训练数据
        """
        print("\n📋 加载训练数据...")
        
        train_file = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        df_train = pd.read_excel(train_file)
        
        print(f"原始训练数据: {df_train.shape}")
        
        # 数据清洗
        # 1. 移除异常值
        df_clean = df_train.copy()
        
        # 移除总电量为0或异常大的数据
        df_clean = df_clean[df_clean['总电量(kWh)'] > 0]
        df_clean = df_clean[df_clean['总电量(kWh)'] < df_clean['总电量(kWh)'].quantile(0.99)]
        
        # 2. 处理缺失值
        numeric_columns = ['最高温度(°C)', '最低温度(°C)', 'AQI', '降水量(mm)', '湿度(%)', '气压(hPa)']
        for col in numeric_columns:
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].fillna(df_clean[col].median())
        
        # 3. 特征工程
        df_clean = self.feature_engineering(df_clean)
        
        print(f"清洗后训练数据: {df_clean.shape}")
        
        return df_clean
    
    def feature_engineering(self, df):
        """
        特征工程
        """
        df_fe = df.copy()
        
        # 1. 时间特征
        df_fe['时间'] = pd.to_datetime(df_fe['时间'])
        df_fe['月份'] = df_fe['时间'].dt.month
        df_fe['日期'] = df_fe['时间'].dt.day
        df_fe['星期'] = df_fe['时间'].dt.dayofweek
        
        # 2. 温度特征
        if '最高温度(°C)' in df_fe.columns and '最低温度(°C)' in df_fe.columns:
            df_fe['温差'] = df_fe['最高温度(°C)'] - df_fe['最低温度(°C)']
            df_fe['平均温度'] = (df_fe['最高温度(°C)'] + df_fe['最低温度(°C)']) / 2
            
            # 温度区间
            df_fe['温度区间'] = pd.cut(df_fe['平均温度'], 
                                   bins=[-np.inf, 15, 25, 35, np.inf], 
                                   labels=['低温', '适温', '高温', '酷热'])
        
        # 3. 用电模式特征
        df_fe['峰谷比'] = df_fe['峰电量(kWh)'] / (df_fe['谷电量(kWh)'] + 1)
        df_fe['尖峰比'] = df_fe['尖电量(kWh)'] / (df_fe['峰电量(kWh)'] + 1)
        
        # 4. 历史用电特征（按用户分组）
        df_fe = df_fe.sort_values(['户号', '时间'])
        df_fe['历史平均用电'] = df_fe.groupby('户号')['总电量(kWh)'].transform(lambda x: x.expanding().mean().shift(1))
        df_fe['历史用电标准差'] = df_fe.groupby('户号')['总电量(kWh)'].transform(lambda x: x.expanding().std().shift(1))
        
        # 5. 公司规模特征
        company_avg = df_fe.groupby('公司名称')['总电量(kWh)'].mean()
        df_fe['公司平均用电'] = df_fe['公司名称'].map(company_avg)
        
        # 6. 地区特征
        region_avg = df_fe.groupby('地区')['总电量(kWh)'].mean()
        df_fe['地区平均用电'] = df_fe['地区'].map(region_avg)
        
        return df_fe
    
    def prepare_features(self, df):
        """
        准备模型特征
        """
        # 选择特征
        feature_columns = [
            '月份', '日期', '星期',
            '最高温度(°C)', '最低温度(°C)', '温差', '平均温度',
            'AQI', '降水量(mm)', '湿度(%)', '气压(hPa)',
            '峰谷比', '尖峰比',
            '历史平均用电', '历史用电标准差',
            '公司平均用电', '地区平均用电'
        ]
        
        # 分类特征编码
        categorical_features = ['地区', '天气', '日期类型', '风向', '温度区间']
        
        df_features = df.copy()
        
        for cat_feature in categorical_features:
            if cat_feature in df_features.columns:
                if cat_feature not in self.encoders:
                    self.encoders[cat_feature] = LabelEncoder()
                    df_features[f'{cat_feature}_encoded'] = self.encoders[cat_feature].fit_transform(df_features[cat_feature].astype(str))
                else:
                    df_features[f'{cat_feature}_encoded'] = self.encoders[cat_feature].transform(df_features[cat_feature].astype(str))
                feature_columns.append(f'{cat_feature}_encoded')
        
        # 选择存在的特征
        available_features = [col for col in feature_columns if col in df_features.columns]
        
        X = df_features[available_features].fillna(0)
        
        return X, available_features
    
    def train_improved_models(self, df_train):
        """
        训练改进的模型
        """
        print("\n🤖 训练改进的预测模型...")
        
        # 准备特征
        X, feature_names = self.prepare_features(df_train)
        
        # 目标变量
        target_columns = ['总电量(kWh)', '尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']
        
        results = {}
        
        for target in target_columns:
            print(f"\n训练 {target} 预测模型...")
            
            y = df_train[target]
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # 特征标准化
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # 训练多个模型
            models = {
                'RandomForest': RandomForestRegressor(n_estimators=100, random_state=42),
                'GradientBoosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
                'LinearRegression': LinearRegression()
            }
            
            best_model = None
            best_score = -np.inf
            best_model_name = None
            
            for model_name, model in models.items():
                if model_name == 'LinearRegression':
                    model.fit(X_train_scaled, y_train)
                    y_pred = model.predict(X_test_scaled)
                else:
                    model.fit(X_train, y_train)
                    y_pred = model.predict(X_test)
                
                # 评估模型
                mae = mean_absolute_error(y_test, y_pred)
                rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                r2 = r2_score(y_test, y_pred)
                
                print(f"  {model_name}: MAE={mae:.2f}, RMSE={rmse:.2f}, R²={r2:.4f}")
                
                if r2 > best_score:
                    best_score = r2
                    best_model = model
                    best_model_name = model_name
            
            # 保存最佳模型
            self.models[target] = best_model
            self.scalers[target] = scaler
            
            # 特征重要性（如果是树模型）
            if hasattr(best_model, 'feature_importances_'):
                importance = pd.DataFrame({
                    'feature': feature_names,
                    'importance': best_model.feature_importances_
                }).sort_values('importance', ascending=False)
                self.feature_importance[target] = importance
                
                print(f"  最佳模型: {best_model_name} (R²={best_score:.4f})")
                print(f"  前5个重要特征: {list(importance.head()['feature'])}")
            
            results[target] = {
                'model': best_model,
                'model_name': best_model_name,
                'r2_score': best_score,
                'scaler': scaler
            }
        
        return results
    
    def predict_improved(self, target_date='2025-07-17'):
        """
        使用改进模型进行预测
        """
        print(f"\n🔮 使用改进模型预测 {target_date}...")

        # 读取实际数据作为预测目标
        actual_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250717-20250717 (1).xlsx'
        df_target = pd.read_excel(actual_file)

        # 准备预测数据（需要添加特征）
        df_pred_data = df_target[['表计号', '户号', '时间']].copy()

        # 添加天气和其他特征（这里使用历史平均值或假设值）
        df_pred_data['地区'] = '杭州'  # 默认地区
        df_pred_data['天气'] = '晴'
        df_pred_data['日期类型'] = '工作日'
        df_pred_data['最高温度(°C)'] = 32.0  # 7月17日的典型温度
        df_pred_data['最低温度(°C)'] = 26.0
        df_pred_data['AQI'] = 50.0
        df_pred_data['降水量(mm)'] = 0.0
        df_pred_data['湿度(%)'] = 70.0
        df_pred_data['气压(hPa)'] = 1013.0
        df_pred_data['风向'] = '南风'
        df_pred_data['公司名称'] = '默认公司'

        # 添加初始的电量数据（用于特征工程）
        df_pred_data['总电量(kWh)'] = 0
        df_pred_data['尖电量(kWh)'] = 0
        df_pred_data['峰电量(kWh)'] = 0
        df_pred_data['平电量(kWh)'] = 0
        df_pred_data['谷电量(kWh)'] = 0

        # 特征工程
        df_pred_data = self.feature_engineering(df_pred_data)

        # 准备特征
        X_pred, _ = self.prepare_features(df_pred_data)

        # 预测
        predictions = {}
        target_columns = ['总电量(kWh)', '尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']

        for target in target_columns:
            if target in self.models:
                model = self.models[target]
                scaler = self.scalers[target]

                if isinstance(model, LinearRegression):
                    X_pred_scaled = scaler.transform(X_pred)
                    pred = model.predict(X_pred_scaled)
                else:
                    pred = model.predict(X_pred)

                # 确保预测值为正数
                pred = np.maximum(pred, 0)
                predictions[target] = pred

        # 创建预测结果DataFrame
        df_result = df_target[['表计号', '户号', '时间']].copy()
        for target in target_columns:
            if target in predictions:
                df_result[target] = predictions[target]

        return df_result

    def save_improved_predictions(self, df_predictions, df_actual, output_file="改进的用电量预测结果_2025-07-17.xlsx"):
        """
        保存改进的预测结果并进行对比分析
        """
        print(f"\n💾 保存改进的预测结果...")

        output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"

        # 合并预测和实际数据进行对比
        df_compare = pd.merge(df_predictions, df_actual, on=['表计号', '户号'], suffixes=('_improved_pred', '_actual'))

        # 读取原始预测结果进行三方对比
        try:
            original_pred_file = '/Users/<USER>/RiderProjects/Solution3/110kV以下用户用电量预测结果_2025-07-17.xlsx'
            df_original_pred = pd.read_excel(original_pred_file)
            df_compare = pd.merge(df_compare, df_original_pred, on=['表计号', '户号'], suffixes=('', '_original_pred'))
        except:
            print("无法读取原始预测结果，跳过三方对比")

        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 1. 改进的预测结果
            df_predictions.to_excel(writer, sheet_name='改进预测结果', index=False)

            # 2. 预测对比分析
            df_compare.to_excel(writer, sheet_name='预测对比分析', index=False)

            # 3. 误差分析
            error_analysis = self.calculate_error_metrics(df_compare)
            error_analysis.to_excel(writer, sheet_name='误差分析', index=False)

            # 4. 特征重要性
            if self.feature_importance:
                for target, importance_df in self.feature_importance.items():
                    sheet_name = f'特征重要性_{target[:4]}'
                    importance_df.to_excel(writer, sheet_name=sheet_name, index=False)

        print(f"✅ 改进预测结果已保存到: {output_path}")

        return df_compare

    def calculate_error_metrics(self, df_compare):
        """
        计算误差指标
        """
        target_columns = ['总电量(kWh)', '尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']

        metrics = []

        for target in target_columns:
            improved_col = f'{target}_improved_pred'
            actual_col = f'{target}_actual'
            original_col = f'{target}_original_pred'

            if improved_col in df_compare.columns and actual_col in df_compare.columns:
                # 改进模型误差
                improved_mae = mean_absolute_error(df_compare[actual_col], df_compare[improved_col])
                improved_rmse = np.sqrt(mean_squared_error(df_compare[actual_col], df_compare[improved_col]))
                improved_r2 = r2_score(df_compare[actual_col], df_compare[improved_col])

                metric_row = {
                    '指标': target,
                    '改进模型_MAE': improved_mae,
                    '改进模型_RMSE': improved_rmse,
                    '改进模型_R²': improved_r2
                }

                # 原始模型误差（如果存在）
                if original_col in df_compare.columns:
                    original_mae = mean_absolute_error(df_compare[actual_col], df_compare[original_col])
                    original_rmse = np.sqrt(mean_squared_error(df_compare[actual_col], df_compare[original_col]))
                    original_r2 = r2_score(df_compare[actual_col], df_compare[original_col])

                    metric_row.update({
                        '原始模型_MAE': original_mae,
                        '原始模型_RMSE': original_rmse,
                        '原始模型_R²': original_r2,
                        'MAE改进率(%)': (original_mae - improved_mae) / original_mae * 100,
                        'RMSE改进率(%)': (original_rmse - improved_rmse) / original_rmse * 100
                    })

                metrics.append(metric_row)

        return pd.DataFrame(metrics)

    def generate_improvement_report(self, df_compare):
        """
        生成改进报告
        """
        print("\n" + "="*80)
        print("用电量预测模型改进报告")
        print("="*80)

        target_columns = ['总电量(kWh)', '尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']

        for target in target_columns:
            improved_col = f'{target}_improved_pred'
            actual_col = f'{target}_actual'
            original_col = f'{target}_original_pred'

            if improved_col in df_compare.columns and actual_col in df_compare.columns:
                print(f"\n📊 {target} 预测改进分析:")

                # 改进模型性能
                improved_mae = mean_absolute_error(df_compare[actual_col], df_compare[improved_col])
                improved_r2 = r2_score(df_compare[actual_col], df_compare[improved_col])

                print(f"  改进模型 - MAE: {improved_mae:.2f}, R²: {improved_r2:.4f}")

                # 与原始模型对比
                if original_col in df_compare.columns:
                    original_mae = mean_absolute_error(df_compare[actual_col], df_compare[original_col])
                    original_r2 = r2_score(df_compare[actual_col], df_compare[original_col])

                    mae_improvement = (original_mae - improved_mae) / original_mae * 100
                    r2_improvement = improved_r2 - original_r2

                    print(f"  原始模型 - MAE: {original_mae:.2f}, R²: {original_r2:.4f}")
                    print(f"  改进效果 - MAE改进: {mae_improvement:.1f}%, R²提升: {r2_improvement:.4f}")

                # 特征重要性
                if target in self.feature_importance:
                    top_features = self.feature_importance[target].head(3)['feature'].tolist()
                    print(f"  重要特征: {', '.join(top_features)}")

        print("\n" + "="*80)

def main():
    """
    主函数
    """
    print("改进的用电量预测模型")
    print("分析预测偏差并优化模型以减小误差")
    print("="*60)

    predictor = ImprovedPowerPredictor()

    try:
        # 1. 分析现有预测误差
        df_compare, problems = predictor.analyze_prediction_errors()

        # 2. 加载和准备训练数据
        df_train = predictor.load_and_prepare_training_data()

        # 3. 训练改进的模型
        model_results = predictor.train_improved_models(df_train)

        # 4. 使用改进模型进行预测
        df_improved_pred = predictor.predict_improved()

        # 5. 读取实际数据
        actual_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250717-20250717 (1).xlsx'
        df_actual = pd.read_excel(actual_file)

        # 6. 保存改进的预测结果
        df_final_compare = predictor.save_improved_predictions(df_improved_pred, df_actual)

        # 7. 生成改进报告
        predictor.generate_improvement_report(df_final_compare)

        print(f"\n🎉 预测模型改进完成！")
        print(f"📊 发现的主要问题: {len(problems)} 个")
        print(f"🤖 训练的模型数量: {len(model_results)} 个")
        print(f"📁 改进结果已保存")

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
