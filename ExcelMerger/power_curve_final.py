#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中栋电厂出力曲线处理工具 - 最终版
正确处理半小时数据转换为15分钟间隔
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

class PowerCurveFinalProcessor:
    def __init__(self):
        """
        初始化出力曲线处理器
        """
        self.settlement_data = None
        self.processed_data = None
    
    def load_settlement_data(self, file_path):
        """
        加载现货结算表数据
        """
        print("正在加载中栋电厂现货结算表...")
        
        try:
            # 跳过前3行，从第4行开始读取
            df = pd.read_excel(file_path, skiprows=3)
            
            # 重新命名列
            df.columns = ['项目名称', '结算单元', '空列1', '时间', '计量电量', '结算电量', 
                         '出清电价', '结算电价', '空列2', '结算电费', '空列3']
            
            # 过滤掉表头行和空行
            df = df[df['时间'].notna()]
            df = df[df['时间'] != '时间']
            
            # 转换时间格式
            df['时间'] = pd.to_datetime(df['时间'])
            
            # 转换数值列
            df['结算电量'] = pd.to_numeric(df['结算电量'], errors='coerce')
            
            # 过滤掉无效数据
            df = df.dropna(subset=['时间', '结算电量'])
            
            # 排序
            df = df.sort_values('时间').reset_index(drop=True)
            
            self.settlement_data = df
            
            print(f"✅ 成功加载现货结算数据: {len(df)} 条记录")
            print(f"   时间范围: {df['时间'].min()} 到 {df['时间'].max()}")
            print(f"   结算电量范围: {df['结算电量'].min():.4f} - {df['结算电量'].max():.4f} MWh")
            
            # 显示前几条数据用于验证
            print(f"\n📊 原始半小时数据示例:")
            for i, row in df.head(6).iterrows():
                print(f"   {row['时间'].strftime('%H:%M:%S')}: {row['结算电量']:.4f} MWh")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载现货结算数据失败: {e}")
            return False
    
    def convert_to_15min_format(self):
        """
        将半小时数据转换为15分钟格式
        逻辑：
        1. 半小时电量数据表示该半小时内的总发电量
        2. 转换为15分钟时，每15分钟的电量 = 半小时电量 / 2
        3. 功率 = 15分钟电量 / 0.25小时
        """
        print("\n正在转换为15分钟格式...")
        
        if self.settlement_data is None:
            print("❌ 请先加载现货结算数据")
            return False
        
        results = []
        
        # 处理每一天的数据
        for date, group in self.settlement_data.groupby(self.settlement_data['时间'].dt.date):
            print(f"  处理日期: {date}")
            
            group_sorted = group.sort_values('时间').reset_index(drop=True)
            
            # 为每一天生成96个15分钟时间点
            for hour in range(24):
                for minute in [15, 30, 45]:
                    time_str = f"{hour:02d}:{minute:02d}"
                    
                    # 找到对应的半小时数据
                    if minute == 15:
                        # 00:15 对应 00:30 的数据
                        target_time = datetime.combine(date, datetime.strptime(f"{hour:02d}:30", "%H:%M").time())
                    elif minute == 30:
                        # 00:30 对应 00:30 的数据
                        target_time = datetime.combine(date, datetime.strptime(f"{hour:02d}:30", "%H:%M").time())
                    else:  # minute == 45
                        # 00:45 对应 01:00 的数据
                        if hour == 23:
                            target_time = datetime.combine(date + timedelta(days=1), datetime.strptime("00:00", "%H:%M").time())
                        else:
                            target_time = datetime.combine(date, datetime.strptime(f"{hour+1:02d}:00", "%H:%M").time())
                    
                    # 查找对应的半小时数据
                    matching_data = group_sorted[group_sorted['时间'] == target_time]
                    
                    if len(matching_data) > 0:
                        half_hour_energy = matching_data.iloc[0]['结算电量']  # 半小时电量 MWh
                        quarter_hour_energy = half_hour_energy / 2  # 15分钟电量 MWh
                        power = quarter_hour_energy / 0.25  # 功率 MW
                        
                        results.append({
                            '时间': time_str,
                            '电量(MWh)': quarter_hour_energy,
                            '功率(MW)': power,
                            '日期': date.strftime('%Y-%m-%d'),
                            '对应半小时时间': target_time.strftime('%H:%M'),
                            '半小时电量': half_hour_energy
                        })
                    else:
                        # 没有找到对应数据，使用0
                        results.append({
                            '时间': time_str,
                            '电量(MWh)': 0.0,
                            '功率(MW)': 0.0,
                            '日期': date.strftime('%Y-%m-%d'),
                            '对应半小时时间': 'N/A',
                            '半小时电量': 0.0
                        })
                
                # 添加整点时间 (除了24:00)
                if hour < 23:
                    time_str = f"{hour+1:02d}:00"
                    target_time = datetime.combine(date, datetime.strptime(f"{hour+1:02d}:00", "%H:%M").time())
                    
                    matching_data = group_sorted[group_sorted['时间'] == target_time]
                    
                    if len(matching_data) > 0:
                        half_hour_energy = matching_data.iloc[0]['结算电量']
                        quarter_hour_energy = half_hour_energy / 2
                        power = quarter_hour_energy / 0.25
                        
                        results.append({
                            '时间': time_str,
                            '电量(MWh)': quarter_hour_energy,
                            '功率(MW)': power,
                            '日期': date.strftime('%Y-%m-%d'),
                            '对应半小时时间': target_time.strftime('%H:%M'),
                            '半小时电量': half_hour_energy
                        })
                    else:
                        results.append({
                            '时间': time_str,
                            '电量(MWh)': 0.0,
                            '功率(MW)': 0.0,
                            '日期': date.strftime('%Y-%m-%d'),
                            '对应半小时时间': 'N/A',
                            '半小时电量': 0.0
                        })
            
            # 添加24:00
            results.append({
                '时间': '24:00',
                '电量(MWh)': 0.0,
                '功率(MW)': 0.0,
                '日期': date.strftime('%Y-%m-%d'),
                '对应半小时时间': 'N/A',
                '半小时电量': 0.0
            })
        
        # 转换为DataFrame
        self.processed_data = pd.DataFrame(results)
        
        print(f"✅ 15分钟格式转换完成: {len(self.processed_data)} 条记录")
        
        # 显示统计信息
        total_energy = self.processed_data['电量(MWh)'].sum()
        avg_power = self.processed_data['功率(MW)'].mean()
        print(f"   总电量: {total_energy:.2f} MWh")
        print(f"   平均功率: {avg_power:.2f} MW")
        print(f"   功率范围: {self.processed_data['功率(MW)'].min():.2f} - {self.processed_data['功率(MW)'].max():.2f} MW")
        
        return True
    
    def save_results(self, output_file="中栋电厂15分钟出力曲线_最终版.xlsx"):
        """
        保存处理结果
        """
        print(f"\n正在保存结果...")
        
        if self.processed_data is None:
            print("❌ 没有数据可保存")
            return False
        
        try:
            output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 1. 完整15分钟数据
                output_data = self.processed_data[['时间', '电量(MWh)', '功率(MW)', '日期']].copy()
                output_data.to_excel(writer, sheet_name='15分钟出力曲线', index=False)
                
                # 2. 按日期分表
                for date in output_data['日期'].unique():
                    daily_data = output_data[output_data['日期'] == date].copy()
                    daily_data = daily_data[['时间', '电量(MWh)', '功率(MW)']]  # 移除日期列
                    sheet_name = f"出力曲线_{date}"
                    daily_data.to_excel(writer, sheet_name=sheet_name, index=False)
                    print(f"    {date}: {len(daily_data)} 个时间点")
                
                # 3. 统计汇总
                summary_data = output_data.groupby('日期').agg({
                    '电量(MWh)': ['sum', 'mean', 'min', 'max'],
                    '功率(MW)': ['mean', 'min', 'max']
                }).round(4)
                
                summary_data.columns = ['总电量', '平均电量', '最小电量', '最大电量', 
                                      '平均功率', '最小功率', '最大功率']
                summary_data = summary_data.reset_index()
                summary_data.to_excel(writer, sheet_name='日统计汇总', index=False)
                
                # 4. 原始数据对比
                if self.settlement_data is not None:
                    comparison_data = self.settlement_data[['时间', '结算电量']].copy()
                    comparison_data.to_excel(writer, sheet_name='原始半小时数据', index=False)
                
                # 5. 详细转换数据（用于验证）
                detailed_data = self.processed_data.copy()
                detailed_data.to_excel(writer, sheet_name='详细转换数据', index=False)
            
            print(f"✅ 结果已保存到: {output_path}")
            
            # 显示7月1日的前10个数据点用于验证
            print(f"\n🔍 7月1日前10个数据点验证:")
            july1_data = output_data[output_data['日期'] == '2025-07-01'].head(10)
            for _, row in july1_data.iterrows():
                print(f"   {row['时间']}: 电量={row['电量(MWh)']:.4f} MWh, 功率={row['功率(MW)']:.4f} MW")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False

def main():
    """
    主函数
    """
    print("中栋电厂出力曲线处理工具 - 最终版")
    print("="*60)
    
    # 文件路径
    settlement_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/现货结算表/中栋电厂2025-07-01-day_sbs_gen_pub_detail_25.xlsx"
    
    if not os.path.exists(settlement_file):
        print(f"❌ 现货结算表文件不存在: {settlement_file}")
        return
    
    processor = PowerCurveFinalProcessor()
    
    # 1. 加载数据
    if not processor.load_settlement_data(settlement_file):
        return
    
    # 2. 转换为15分钟格式
    if not processor.convert_to_15min_format():
        return
    
    # 3. 保存结果
    if processor.save_results():
        print(f"\n🎉 处理完成！")
        print(f"📊 成功将半小时电量数据转换为15分钟格式")
        print(f"⚡ 转换逻辑: 15分钟电量 = 半小时电量 / 2")
        print(f"⚡ 功率计算: 功率(MW) = 15分钟电量(MWh) / 0.25h")
        print(f"📁 结果已保存到Excel文件")
    else:
        print(f"\n❌ 处理失败！")

if __name__ == "__main__":
    main()
