#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将天气数据合并到指定的目标Excel文件中
"""

import pandas as pd
import os
from datetime import datetime, timedel<PERSON>

def main():
    """主函数"""
    print("天气数据合并工具 - 合并到目标文件")
    print("=" * 50)
    
    # 文件路径
    target_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/合并结果_6月用电量信息含地区(1).xlsx"
    weather_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/6月份全部地方天气.xlsx"
    output_file = "/Users/<USER>/RiderProjects/Solution3/合并结果_6月用电量信息含地区_含天气.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(target_file):
        print(f"错误: 目标文件不存在")
        print(f"路径: {target_file}")
        return False
    
    if not os.path.exists(weather_file):
        print(f"错误: 天气文件不存在")
        print(f"路径: {weather_file}")
        return False
    
    try:
        # 读取目标文件（用电量数据）
        print(f"正在读取目标文件...")
        target_df = pd.read_excel(target_file)
        print(f"目标文件读取成功，共 {len(target_df)} 行数据")
        print(f"目标文件列名: {list(target_df.columns)}")
        
        # 读取天气文件
        print(f"正在读取天气文件...")
        weather_df = pd.read_excel(weather_file)
        print(f"天气文件读取成功，共 {len(weather_df)} 行数据")
        print(f"天气文件列名: {list(weather_df.columns)}")
        
        # 预处理数据
        print(f"正在预处理数据...")
        
        # 标准化日期格式的函数
        def normalize_date(date_value):
            if pd.isna(date_value):
                return None
            try:
                if isinstance(date_value, datetime):
                    return date_value.strftime('%Y-%m-%d')
                else:
                    parsed_date = pd.to_datetime(date_value)
                    return parsed_date.strftime('%Y-%m-%d')
            except:
                return None
        
        # 标准化目标文件的日期格式
        target_df['标准化日期'] = target_df['时间'].apply(normalize_date)
        
        # 标准化天气文件的日期格式
        weather_df['标准化日期'] = weather_df['日期'].apply(normalize_date)
        
        # 显示数据概况
        print(f"\n目标文件中的地区分布:")
        target_regions = target_df['地区'].value_counts()
        print(target_regions)
        
        print(f"\n天气文件中的地区:")
        weather_regions = weather_df['城市'].dropna().unique()
        print(sorted([str(region) for region in weather_regions]))
        
        # 创建天气映射字典
        print(f"\n正在创建天气映射...")
        weather_mapping = {}
        for _, row in weather_df.iterrows():
            key = (row['城市'], row['标准化日期'])
            weather_mapping[key] = row['天气情况']
        
        print(f"天气映射创建完成，共 {len(weather_mapping)} 个条目")
        
        # 合并天气数据
        print(f"正在合并天气数据...")
        def get_weather(row):
            if pd.isna(row['地区']):
                return None
            key = (row['地区'], row['标准化日期'])
            return weather_mapping.get(key, None)
        
        target_df['天气'] = target_df.apply(get_weather, axis=1)
        
        # 统计初始匹配情况
        initial_matched = target_df['天气'].notna().sum()
        initial_unmatched = len(target_df) - initial_matched
        
        print(f"\n初始匹配结果:")
        print(f"总记录数: {len(target_df)}")
        print(f"成功匹配天气: {initial_matched}")
        print(f"未匹配天气: {initial_unmatched}")
        print(f"天气匹配率: {initial_matched/len(target_df)*100:.2f}%")
        
        # 处理缺失的天气数据
        if initial_unmatched > 0:
            print(f"\n正在处理缺失的天气数据...")
            
            # 分析未匹配的记录
            unmatched_df = target_df[target_df['天气'].isna()]
            unmatched_combinations = unmatched_df[['地区', '标准化日期']].drop_duplicates()
            
            print(f"未匹配的地区-日期组合:")
            for _, row in unmatched_combinations.head(10).iterrows():
                region = row['地区']
                date = row['标准化日期']
                if pd.notna(region):
                    print(f"  地区: {region}, 日期: {date}")
            
            # 特殊处理：诸暨6月1日使用6月2日的天气
            zhuji_june_1_mask = (target_df['地区'] == '诸暨') & (target_df['标准化日期'] == '2025-06-01')
            if zhuji_june_1_mask.any():
                zhuji_june_2_weather = weather_mapping.get(('诸暨', '2025-06-02'))
                if zhuji_june_2_weather:
                    target_df.loc[zhuji_june_1_mask, '天气'] = zhuji_june_2_weather
                    print(f"为诸暨6月1日填充天气数据: {zhuji_june_2_weather}")
            
            # 对于其他缺失数据，尝试使用相邻日期的天气
            for _, row in unmatched_combinations.iterrows():
                region = row['地区']
                date = row['标准化日期']
                
                if pd.isna(region) or pd.isna(date):
                    continue
                
                # 如果这个组合还没有天气数据，尝试找相邻日期
                if target_df[(target_df['地区'] == region) & (target_df['标准化日期'] == date) & (target_df['天气'].isna())].empty:
                    continue
                
                # 查找相邻日期的天气
                nearby_weather = find_nearby_weather(weather_mapping, region, date)
                if nearby_weather:
                    mask = (target_df['地区'] == region) & (target_df['标准化日期'] == date)
                    target_df.loc[mask, '天气'] = nearby_weather
                    print(f"为 {region} {date} 填充相邻日期天气: {nearby_weather}")
        
        # 统计最终结果
        final_matched = target_df['天气'].notna().sum()
        final_unmatched = len(target_df) - final_matched
        
        print(f"\n最终合并结果:")
        print(f"总记录数: {len(target_df)}")
        print(f"成功匹配天气: {final_matched}")
        print(f"未匹配天气: {final_unmatched}")
        print(f"天气匹配率: {final_matched/len(target_df)*100:.2f}%")
        
        # 清理数据并保存
        print(f"\n正在保存结果...")
        
        # 删除辅助列
        result_df = target_df.drop(columns=['标准化日期'])
        
        # 重新排列列的顺序，将天气列放在地区列后面
        cols = list(result_df.columns)
        if '天气' in cols:
            cols.remove('天气')
            if '地区' in cols:
                region_index = cols.index('地区')
                cols.insert(region_index + 1, '天气')
            else:
                cols.append('天气')
        
        result_df = result_df[cols]
        
        # 保存到Excel文件
        result_df.to_excel(output_file, index=False)
        print(f"结果已保存到: {output_file}")
        
        # 显示预览
        print(f"\n数据预览 (前10行):")
        preview_cols = ['地区', '天气', '时间', '总电量(kWh)']
        available_cols = [col for col in preview_cols if col in result_df.columns]
        print(result_df[available_cols].head(10))
        
        # 显示天气数据统计
        print(f"\n天气数据统计:")
        weather_stats = result_df['天气'].value_counts()
        print(weather_stats)
        
        print(f"\n处理完成！")
        return True
        
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def find_nearby_weather(weather_mapping, region, date_str):
    """
    查找相邻日期的天气数据
    
    Args:
        weather_mapping: 天气映射字典
        region: 地区
        date_str: 日期字符串
        
    Returns:
        str: 相邻日期的天气，如果没有找到则返回None
    """
    try:
        target_date = datetime.strptime(date_str, '%Y-%m-%d')
        
        # 查找前后3天的天气数据
        for days_diff in [1, -1, 2, -2, 3, -3]:
            nearby_date = target_date + timedelta(days=days_diff)
            nearby_date_str = nearby_date.strftime('%Y-%m-%d')
            
            weather = weather_mapping.get((region, nearby_date_str))
            if weather:
                return weather
        
        return None
        
    except Exception as e:
        print(f"查找相邻天气时发生错误: {e}")
        return None

if __name__ == "__main__":
    main()
