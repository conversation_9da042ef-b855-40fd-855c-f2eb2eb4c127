#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
周天天气数据格式化工具
将整理好的同一周天相同天气数据按指定格式添加到现有Excel文件的各个分表中
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from collections import defaultdict
from openpyxl import load_workbook
from openpyxl.utils.dataframe import dataframe_to_rows

class WeekdayWeatherFormatter:
    def __init__(self):
        """
        初始化格式化器
        """
        self.source_data = None
        self.target_workbook = None
        self.grouped_data = {}
        
        # 中文周天映射
        self.weekday_names = {
            0: '星期一',
            1: '星期二', 
            2: '星期三',
            3: '星期四',
            4: '星期五',
            5: '星期六',
            6: '星期日'
        }
        
        # 周天到列的映射
        self.weekday_to_column = {
            '星期一': 'B',
            '星期二': 'C',
            '星期三': 'D',
            '星期四': 'E',
            '星期五': 'F',
            '星期六': 'G',
            '星期日': 'H'
        }
    
    def load_source_data(self, source_file):
        """
        加载源数据文件（已经整理好的周天天气数据）

        Args:
            source_file: 源数据文件路径
        """
        try:
            print(f"正在读取源数据文件: {os.path.basename(source_file)}")

            # 读取完整数据表而不是汇总表
            self.source_data = pd.read_excel(source_file, sheet_name='完整数据')
            print(f"源数据读取成功，共 {len(self.source_data)} 行数据")
            print(f"源数据列名: {list(self.source_data.columns)}")

            return True

        except Exception as e:
            print(f"源数据加载失败: {e}")
            return False
    
    def load_target_file(self, target_file):
        """
        加载目标Excel文件
        
        Args:
            target_file: 目标Excel文件路径
        """
        try:
            print(f"正在读取目标文件: {os.path.basename(target_file)}")
            
            # 使用openpyxl读取所有工作表
            self.target_workbook = load_workbook(target_file)
            sheet_names = self.target_workbook.sheetnames
            print(f"发现工作表: {sheet_names}")
            
            return True
            
        except Exception as e:
            print(f"目标文件加载失败: {e}")
            return False
    
    def preprocess_source_data(self):
        """
        预处理源数据，按地区和周天天气组合分组
        """
        try:
            print(f"\n=== 预处理源数据 ===")
            
            # 确定时间列
            time_column = None
            if '时间' in self.source_data.columns:
                time_column = '时间'
            elif '日期' in self.source_data.columns:
                time_column = '日期'
            else:
                raise ValueError("未找到时间或日期列")
            
            # 确定天气列
            weather_column = None
            if '天气' in self.source_data.columns:
                weather_column = '天气'
            elif '天气状况' in self.source_data.columns:
                weather_column = '天气状况'
            else:
                raise ValueError("未找到天气列")
            
            # 确定用电量列
            power_column = None
            power_columns = [col for col in self.source_data.columns if any(keyword in col for keyword in ['电量', 'kWh'])]
            if power_columns:
                power_column = power_columns[0]
            else:
                raise ValueError("未找到用电量列")
            
            print(f"使用时间列: {time_column}")
            print(f"使用天气列: {weather_column}")
            print(f"使用用电量列: {power_column}")
            
            # 转换时间列并添加周天信息
            self.source_data['时间_处理'] = pd.to_datetime(self.source_data[time_column])
            self.source_data['日期'] = self.source_data['时间_处理'].dt.date
            self.source_data['周天数字'] = self.source_data['时间_处理'].dt.weekday
            self.source_data['周天'] = self.source_data['周天数字'].map(self.weekday_names)
            
            # 按地区、周天、天气分组
            self.grouped_data = {}
            
            for region in self.source_data['地区'].unique():
                region_data = self.source_data[self.source_data['地区'] == region]
                self.grouped_data[region] = {}
                
                # 按周天天气组合分组
                for _, row in region_data.iterrows():
                    weekday = row['周天']
                    weather = row[weather_column]
                    power = row[power_column]
                    date = row['日期']
                    
                    key = f"{weekday}_{weather}"
                    
                    if key not in self.grouped_data[region]:
                        self.grouped_data[region][key] = {
                            '周天': weekday,
                            '天气': weather,
                            '用电量列表': [],
                            '日期列表': []
                        }
                    
                    self.grouped_data[region][key]['用电量列表'].append(power)
                    self.grouped_data[region][key]['日期列表'].append(date)
            
            print(f"数据分组完成，共处理 {len(self.grouped_data)} 个地区")
            for region, data in self.grouped_data.items():
                print(f"  {region}: {len(data)} 个周天天气组合")
            
            return True
            
        except Exception as e:
            print(f"数据预处理失败: {e}")
            return False
    
    def create_formatted_data(self, region):
        """
        为指定地区创建格式化的数据

        Args:
            region: 地区名称

        Returns:
            dict: 按天气类型分组的格式化数据
        """
        if region not in self.grouped_data:
            print(f"警告: 未找到地区 {region} 的数据")
            return {}

        region_data = self.grouped_data[region]

        # 按天气类型分组
        weather_groups = defaultdict(lambda: {
            '周天用电量': {},
            '日期列表': []
        })

        for key, info in region_data.items():
            weather = info['天气']
            weekday = info['周天']
            total_power = sum(info['用电量列表'])

            weather_groups[weather]['周天用电量'][weekday] = total_power
            weather_groups[weather]['日期列表'].extend(info['日期列表'])

        # 去重日期列表
        for weather in weather_groups:
            weather_groups[weather]['日期列表'] = list(set(weather_groups[weather]['日期列表']))

        return dict(weather_groups)
    
    def add_data_to_sheet(self, sheet_name, formatted_data):
        """
        将格式化数据添加到指定工作表
        按照指定格式：
        日期    2025-06-23
        日期类型 星期一  星期二  星期三  星期四  星期五  星期六  星期日
        天气    雨     雨     雨     雨     雨     雨     雨
        用电量  57032.93

        Args:
            sheet_name: 工作表名称
            formatted_data: 格式化的数据
        """
        try:
            if sheet_name not in self.target_workbook.sheetnames:
                print(f"警告: 工作表 {sheet_name} 不存在")
                return False

            worksheet = self.target_workbook[sheet_name]

            # 找到最后一行
            last_row = worksheet.max_row

            print(f"在工作表 {sheet_name} 的第 {last_row + 2} 行开始添加数据")

            # 添加分隔行
            worksheet.cell(row=last_row + 2, column=1, value="=== 同一周天相同天气数据汇总 ===")

            current_row = last_row + 4

            # 为每种天气类型创建一个数据块
            for weather_type, weather_data in formatted_data.items():
                # 找到这种天气的代表日期
                sample_date = min(weather_data['日期列表'])

                # 添加日期行
                worksheet.cell(row=current_row, column=1, value="日期")
                worksheet.cell(row=current_row, column=2, value=sample_date.strftime('%Y-%m-%d'))
                current_row += 1

                # 添加日期类型行（周天标题）
                worksheet.cell(row=current_row, column=1, value="日期类型")
                for i, weekday in enumerate(['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'], 2):
                    worksheet.cell(row=current_row, column=i, value=weekday)
                current_row += 1

                # 添加天气行
                worksheet.cell(row=current_row, column=1, value="天气")
                for i in range(2, 9):  # B到H列
                    worksheet.cell(row=current_row, column=i, value=weather_type)
                current_row += 1

                # 添加用电量行
                worksheet.cell(row=current_row, column=1, value="用电量")
                weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
                for i, weekday in enumerate(weekdays, 2):
                    if weekday in weather_data['周天用电量']:
                        power_value = weather_data['周天用电量'][weekday]
                        worksheet.cell(row=current_row, column=i, value=round(power_value, 2))

                current_row += 2  # 空一行分隔不同天气类型

            print(f"成功添加 {len(formatted_data)} 种天气类型的数据到工作表 {sheet_name}")
            return True

        except Exception as e:
            print(f"添加数据到工作表 {sheet_name} 失败: {e}")
            return False
    
    def process(self, source_file, target_file, output_file):
        """
        完整的处理流程
        
        Args:
            source_file: 源数据文件路径
            target_file: 目标Excel文件路径
            output_file: 输出文件路径
        """
        print(f"周天天气数据格式化工具")
        print(f"=" * 80)
        print(f"源数据文件: {source_file}")
        print(f"目标文件: {target_file}")
        print(f"输出文件: {output_file}")
        
        # 执行处理步骤
        if not self.load_source_data(source_file):
            return False
        
        if not self.load_target_file(target_file):
            return False
        
        if not self.preprocess_source_data():
            return False
        
        # 处理各个地区的工作表
        regions = ['杭州', '金华', '宁波', '台州', '衢州']
        
        for region in regions:
            print(f"\n处理地区: {region}")
            formatted_data = self.create_formatted_data(region)
            
            if formatted_data:
                self.add_data_to_sheet(region, formatted_data)
            else:
                print(f"地区 {region} 没有数据需要添加")
        
        # 保存结果
        try:
            self.target_workbook.save(output_file)
            print(f"\n✅ 处理完成！结果已保存到: {output_file}")
            return True
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False

def main():
    """主函数"""
    # 源数据文件（之前生成的周天天气分组文件）
    source_file = "城市售电表（含气温+日期类型+天气）_周天天气分组.xlsx"
    
    # 目标文件
    target_file = "/Users/<USER>/Desktop/副本城市售电表（含气温+日期类型+天气）(1).xlsx"
    
    # 输出文件
    output_file = "副本城市售电表_含周天天气汇总.xlsx"
    
    if not os.path.exists(source_file):
        print(f"错误: 源数据文件不存在 - {source_file}")
        print("请先运行 weekday_weather_grouper.py 生成源数据文件")
        return
    
    if not os.path.exists(target_file):
        print(f"错误: 目标文件不存在 - {target_file}")
        return
    
    formatter = WeekdayWeatherFormatter()
    formatter.process(source_file, target_file, output_file)

if __name__ == "__main__":
    main()
