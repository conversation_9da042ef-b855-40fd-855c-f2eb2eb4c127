#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
非工作日用电量与天气的Spearman相关性分析
"""

import pandas as pd
import numpy as np
from scipy.stats import spearmanr
import matplotlib.pyplot as plt
import seaborn as sns
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class SpearmanWeatherAnalysis:
    def __init__(self, excel_file):
        """
        初始化Spearman相关性分析器
        
        Args:
            excel_file: 非工作日用电量Excel文件路径
        """
        self.excel_file = excel_file
        self.df = None
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        try:
            self.df = pd.read_excel(self.excel_file)
            print(f"数据加载成功，共 {len(self.df)} 行数据")
            print(f"数据列名: {list(self.df.columns)}")
            
            # 检查必要的列
            if '天气' not in self.df.columns or '总电量(kWh)' not in self.df.columns:
                print("错误: 缺少必要的列 '天气' 或 '总电量(kWh)'")
                return False
            
            # 显示数据基本信息
            print(f"\n数据时间范围:")
            if '时间' in self.df.columns:
                self.df['时间'] = pd.to_datetime(self.df['时间'])
                print(f"  开始时间: {self.df['时间'].min()}")
                print(f"  结束时间: {self.df['时间'].max()}")
                
                # 显示包含的日期
                dates = self.df['时间'].dt.date.unique()
                print(f"  包含日期: {sorted(dates)}")
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def prepare_weather_data(self):
        """
        准备天气数据进行相关性分析
        将天气状况转换为数值
        """
        # 天气状况编码（根据对用电量的潜在影响程度）
        weather_encoding = {
            '晴': 1,      # 晴天，可能用电量较低（自然光充足）
            '多云': 2,    # 多云，中等用电量
            '阴': 3,      # 阴天，可能需要更多照明
            '小雨': 4,    # 小雨，湿度高，可能影响用电
            '中雨': 5,    # 中雨，湿度更高
            '大雨': 6,    # 大雨，湿度最高，可能用电量最高
            '雷阵雨': 5,  # 雷阵雨，类似中雨
            '雪': 6,      # 雪天，寒冷，用电量可能较高
            '雾': 4,      # 雾天，能见度低，需要照明
            '霾': 4       # 霾天，空气质量差，可能需要净化设备
        }
        
        # 创建天气数值编码
        self.df['天气编码'] = self.df['天气'].map(weather_encoding)
        
        # 处理未知天气状况
        unknown_weather = self.df[self.df['天气编码'].isna()]['天气'].unique()
        if len(unknown_weather) > 0:
            print(f"发现未知天气状况: {unknown_weather}")
            # 对未知天气状况赋予中等值
            self.df['天气编码'] = self.df['天气编码'].fillna(3)
        
        print(f"\n天气状况分布:")
        weather_dist = self.df['天气'].value_counts()
        for weather, count in weather_dist.items():
            code = weather_encoding.get(weather, 3)
            print(f"  {weather} (编码:{code}): {count} 条记录")
        
        return weather_encoding
    
    def calculate_spearman_correlation(self):
        """
        计算Spearman相关系数
        """
        # 准备数据
        weather_encoding = self.prepare_weather_data()
        
        # 移除缺失值
        clean_data = self.df[['天气编码', '总电量(kWh)']].dropna()
        
        if len(clean_data) == 0:
            print("错误: 没有有效的数据进行相关性分析")
            return None
        
        print(f"\n用于分析的有效数据量: {len(clean_data)} 条")
        
        # 计算Spearman相关系数
        correlation, p_value = spearmanr(clean_data['天气编码'], clean_data['总电量(kWh)'])
        
        print(f"\n=== Spearman相关性分析结果 ===")
        print(f"相关系数 (ρ): {correlation:.4f}")
        print(f"p值: {p_value:.6f}")
        print(f"显著性水平: {'显著 (p < 0.05)' if p_value < 0.05 else '不显著 (p >= 0.05)'}")
        
        # 解释相关性强度
        if abs(correlation) >= 0.7:
            strength = "强"
        elif abs(correlation) >= 0.3:
            strength = "中等"
        elif abs(correlation) >= 0.1:
            strength = "弱"
        else:
            strength = "极弱"
        
        direction = "正" if correlation > 0 else "负"
        print(f"相关性强度: {strength}{direction}相关")
        
        return {
            'correlation': correlation,
            'p_value': p_value,
            'strength': strength,
            'direction': direction,
            'weather_encoding': weather_encoding,
            'sample_size': len(clean_data)
        }
    
    def detailed_analysis(self):
        """
        详细分析各天气状况下的用电量
        """
        print(f"\n=== 各天气状况用电量详细分析 ===")
        
        # 按天气状况分组统计
        weather_stats = self.df.groupby('天气')['总电量(kWh)'].agg([
            'count', 'mean', 'median', 'std', 'min', 'max'
        ]).round(2)
        
        print(f"\n各天气状况用电量统计:")
        print(weather_stats)
        
        # 按天气编码排序显示
        weather_with_code = self.df.groupby(['天气', '天气编码'])['总电量(kWh)'].agg([
            'count', 'mean', 'median'
        ]).round(2).reset_index()
        
        weather_with_code = weather_with_code.sort_values('天气编码')
        
        print(f"\n按天气影响程度排序的用电量:")
        for _, row in weather_with_code.iterrows():
            print(f"  {row['天气']} (编码{row['天气编码']}): 平均{row['mean']}kWh, 中位数{row['median']}kWh, 样本{row['count']}个")
        
        return weather_stats
    
    def regional_analysis(self):
        """
        按地区分析天气与用电量的关系
        """
        if '地区' not in self.df.columns:
            print("没有地区信息，跳过地区分析")
            return
        
        print(f"\n=== 各地区天气与用电量关系分析 ===")
        
        regions = self.df['地区'].unique()
        regional_correlations = {}
        
        for region in regions:
            region_data = self.df[self.df['地区'] == region]
            clean_region_data = region_data[['天气编码', '总电量(kWh)']].dropna()
            
            if len(clean_region_data) >= 3:  # 至少需要3个数据点
                corr, p_val = spearmanr(clean_region_data['天气编码'], clean_region_data['总电量(kWh)'])
                regional_correlations[region] = {
                    'correlation': corr,
                    'p_value': p_val,
                    'sample_size': len(clean_region_data)
                }
                
                significance = "显著" if p_val < 0.05 else "不显著"
                print(f"  {region}: ρ={corr:.4f}, p={p_val:.4f} ({significance}), 样本数={len(clean_region_data)}")
            else:
                print(f"  {region}: 数据不足 (仅{len(clean_region_data)}个数据点)")
        
        return regional_correlations
    
    def time_series_analysis(self):
        """
        时间序列分析：查看不同日期的天气与用电量关系
        """
        if '时间' not in self.df.columns:
            print("没有时间信息，跳过时间序列分析")
            return
        
        print(f"\n=== 各日期天气与用电量分析 ===")
        
        # 按日期分组分析
        self.df['日期'] = self.df['时间'].dt.date
        daily_stats = self.df.groupby(['日期', '天气']).agg({
            '总电量(kWh)': ['count', 'mean', 'sum'],
            '天气编码': 'first'
        }).round(2)
        
        daily_stats.columns = ['记录数', '平均用电量', '总用电量', '天气编码']
        daily_stats = daily_stats.reset_index()
        
        print(f"\n各日期天气与用电量统计:")
        for _, row in daily_stats.iterrows():
            weekday = pd.to_datetime(row['日期']).strftime('%A')
            print(f"  {row['日期']} ({weekday}): {row['天气']}, 总用电量{row['总用电量']}kWh, 平均{row['平均用电量']}kWh")
        
        return daily_stats
    
    def generate_summary_report(self):
        """
        生成综合分析报告
        """
        print(f"\n" + "="*60)
        print(f"非工作日用电量与天气Spearman相关性分析报告")
        print(f"="*60)
        
        # 基本信息
        print(f"\n数据概况:")
        print(f"  分析文件: {os.path.basename(self.excel_file)}")
        print(f"  数据量: {len(self.df)} 条记录")
        print(f"  时间范围: 非工作日数据")
        
        # 执行各项分析
        correlation_result = self.calculate_spearman_correlation()
        weather_stats = self.detailed_analysis()
        regional_correlations = self.regional_analysis()
        daily_stats = self.time_series_analysis()
        
        if correlation_result:
            print(f"\n主要发现:")
            print(f"  • 天气与用电量的Spearman相关系数: {correlation_result['correlation']:.4f}")
            print(f"  • 相关性强度: {correlation_result['strength']}{correlation_result['direction']}相关")
            print(f"  • 统计显著性: {'显著' if correlation_result['p_value'] < 0.05 else '不显著'}")
            print(f"  • 样本量: {correlation_result['sample_size']} 个有效数据点")
            
            # 业务解释
            print(f"\n业务解释:")
            if correlation_result['correlation'] > 0:
                print(f"  • 天气状况越恶劣（编码值越高），用电量趋向于越高")
                print(f"  • 这可能反映了恶劣天气下照明、除湿、空调等设备使用增加")
            else:
                print(f"  • 天气状况越恶劣，用电量趋向于越低")
                print(f"  • 这可能反映了恶劣天气下活动减少或其他因素影响")
            
            if correlation_result['p_value'] < 0.05:
                print(f"  • 相关性在统计学上显著，可以认为天气对非工作日用电量有影响")
            else:
                print(f"  • 相关性在统计学上不显著，天气对非工作日用电量的影响不明确")
        
        print(f"\n建议:")
        print(f"  • 在非工作日负荷预测中考虑天气因素")
        print(f"  • 针对不同天气状况制定差异化的能源管理策略")
        print(f"  • 关注恶劣天气对用电设备的影响")
        
        return {
            'correlation_result': correlation_result,
            'weather_stats': weather_stats,
            'regional_correlations': regional_correlations,
            'daily_stats': daily_stats
        }

def analyze_file(excel_file, file_type="数据"):
    """
    分析指定文件的天气与用电量相关性

    Args:
        excel_file: Excel文件路径
        file_type: 文件类型描述（如"工作日"、"非工作日"）
    """
    print(f"{file_type}用电量与天气Spearman相关性分析")
    print("="*60)

    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误: 文件不存在 - {excel_file}")
        return False

    try:
        # 创建分析器
        analyzer = SpearmanWeatherAnalysis(excel_file)

        if analyzer.df is not None:
            # 执行综合分析
            results = analyzer.generate_summary_report()

            print(f"\n✅ 分析完成！")
            print(f"📊 已完成{file_type}用电量与天气的Spearman相关性分析")
            return True
        else:
            print(f"❌ 数据加载失败，无法进行分析")
            return False

    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        excel_file = sys.argv[1]
        if "工作日" in excel_file:
            file_type = "工作日"
        elif "非工作日" in excel_file:
            file_type = "非工作日"
        else:
            file_type = "数据"
    else:
        # 默认分析非工作日数据
        excel_file = "/Users/<USER>/Desktop/合并结果_7月用电量信息含天气(2)_非工作日.xlsx"
        file_type = "非工作日"

    analyze_file(excel_file, file_type)

if __name__ == "__main__":
    main()
