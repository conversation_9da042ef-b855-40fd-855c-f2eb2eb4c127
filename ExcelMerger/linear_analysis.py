#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
6月份用电量线性分析工具
包含线性回归、残差分析、预测等功能
"""

import pandas as pd
import numpy as np
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class LinearAnalysis:
    def __init__(self, excel_file):
        """
        初始化线性分析器
        """
        self.excel_file = excel_file
        self.df = None
        self.clean_df = None
        self.load_and_clean_data()
    
    def load_and_clean_data(self):
        """加载并清洗数据"""
        try:
            self.df = pd.read_excel(self.excel_file)
            print(f"原始数据加载成功，共 {len(self.df)} 行数据")
            
            # 数据清洗
            self.clean_df = self.df[
                (self.df['总电量(kWh)'] > 0) &
                (self.df['总电量(kWh)'] < 100000) &
                (self.df['天气'].notna())
            ].copy()
            
            print(f"清洗后数据: {len(self.clean_df)} 行数据")
            print(f"数据保留率: {len(self.clean_df)/len(self.df)*100:.1f}%")
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def prepare_features(self):
        """准备特征变量"""
        # 天气编码
        weather_encoding = {
            '晴': 3, '晴天': 3,
            '多云': 2,
            '阴': 1, '阴天': 1,
            '雨': 1, '小雨': 1,
            '云': 2
        }
        
        self.clean_df['天气编码'] = self.clean_df['天气'].map(weather_encoding)
        
        # 时间特征
        self.clean_df['时间'] = pd.to_datetime(self.clean_df['时间'])
        self.clean_df['日期'] = self.clean_df['时间'].dt.date
        self.clean_df['月份'] = self.clean_df['时间'].dt.month
        self.clean_df['日'] = self.clean_df['时间'].dt.day
        self.clean_df['星期'] = self.clean_df['时间'].dt.dayofweek  # 0=周一
        
        # 工作日标识
        self.clean_df['是否工作日'] = (self.clean_df['星期'] < 5).astype(int)
        
        # 地区编码
        regions = self.clean_df['地区'].unique()
        region_encoding = {region: i for i, region in enumerate(regions)}
        self.clean_df['地区编码'] = self.clean_df['地区'].map(region_encoding)
        
        print(f"\n特征准备完成:")
        print(f"  天气编码: {dict(weather_encoding)}")
        print(f"  地区数量: {len(regions)}")
        print(f"  时间范围: {self.clean_df['时间'].min().date()} 至 {self.clean_df['时间'].max().date()}")
    
    def simple_linear_regression(self):
        """简单线性回归：天气 vs 用电量"""
        print(f"\n=== 简单线性回归分析：天气 vs 用电量 ===")
        
        X = self.clean_df[['天气编码']].values
        y = self.clean_df['总电量(kWh)'].values
        
        # 线性回归
        model = LinearRegression()
        model.fit(X, y)
        
        # 预测
        y_pred = model.predict(X)
        
        # 评估指标
        r2 = r2_score(y, y_pred)
        mse = mean_squared_error(y, y_pred)
        mae = mean_absolute_error(y, y_pred)
        rmse = np.sqrt(mse)
        
        # 统计检验
        n = len(y)
        k = 1  # 特征数
        f_statistic = (r2 / k) / ((1 - r2) / (n - k - 1))
        f_p_value = 1 - stats.f.cdf(f_statistic, k, n - k - 1)
        
        print(f"线性回归结果:")
        print(f"  回归方程: 用电量 = {model.intercept_:.2f} + {model.coef_[0]:.2f} × 天气编码")
        print(f"  R² (决定系数): {r2:.6f}")
        print(f"  调整R²: {1 - (1 - r2) * (n - 1) / (n - k - 1):.6f}")
        print(f"  均方误差 (MSE): {mse:.2f}")
        print(f"  均方根误差 (RMSE): {rmse:.2f}")
        print(f"  平均绝对误差 (MAE): {mae:.2f}")
        print(f"  F统计量: {f_statistic:.4f}")
        print(f"  F检验p值: {f_p_value:.6f}")
        print(f"  模型显著性: {'显著' if f_p_value < 0.05 else '不显著'}")
        
        return {
            'model': model,
            'r2': r2,
            'rmse': rmse,
            'mae': mae,
            'f_p_value': f_p_value,
            'predictions': y_pred
        }
    
    def multiple_linear_regression(self):
        """多元线性回归：多个特征 vs 用电量"""
        print(f"\n=== 多元线性回归分析 ===")
        
        # 选择特征
        feature_cols = ['天气编码', '日', '星期', '是否工作日', '地区编码']
        X = self.clean_df[feature_cols].values
        y = self.clean_df['总电量(kWh)'].values
        
        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 线性回归
        model = LinearRegression()
        model.fit(X_scaled, y)
        
        # 预测
        y_pred = model.predict(X_scaled)
        
        # 评估指标
        r2 = r2_score(y, y_pred)
        mse = mean_squared_error(y, y_pred)
        mae = mean_absolute_error(y, y_pred)
        rmse = np.sqrt(mse)
        
        # 统计检验
        n = len(y)
        k = len(feature_cols)
        f_statistic = (r2 / k) / ((1 - r2) / (n - k - 1))
        f_p_value = 1 - stats.f.cdf(f_statistic, k, n - k - 1)
        
        print(f"多元线性回归结果:")
        print(f"  特征数量: {k}")
        print(f"  R² (决定系数): {r2:.6f}")
        print(f"  调整R²: {1 - (1 - r2) * (n - 1) / (n - k - 1):.6f}")
        print(f"  均方误差 (MSE): {mse:.2f}")
        print(f"  均方根误差 (RMSE): {rmse:.2f}")
        print(f"  平均绝对误差 (MAE): {mae:.2f}")
        print(f"  F统计量: {f_statistic:.4f}")
        print(f"  F检验p值: {f_p_value:.6f}")
        print(f"  模型显著性: {'显著' if f_p_value < 0.05 else '不显著'}")
        
        # 特征重要性
        print(f"\n特征系数 (标准化后):")
        for i, feature in enumerate(feature_cols):
            print(f"  {feature}: {model.coef_[i]:.2f}")
        
        return {
            'model': model,
            'scaler': scaler,
            'r2': r2,
            'rmse': rmse,
            'mae': mae,
            'f_p_value': f_p_value,
            'predictions': y_pred,
            'feature_importance': dict(zip(feature_cols, model.coef_))
        }
    
    def residual_analysis(self, predictions):
        """残差分析"""
        print(f"\n=== 残差分析 ===")
        
        y_true = self.clean_df['总电量(kWh)'].values
        residuals = y_true - predictions
        
        # 残差统计
        print(f"残差统计:")
        print(f"  残差均值: {np.mean(residuals):.2f}")
        print(f"  残差标准差: {np.std(residuals):.2f}")
        print(f"  残差最小值: {np.min(residuals):.2f}")
        print(f"  残差最大值: {np.max(residuals):.2f}")
        
        # 正态性检验
        shapiro_stat, shapiro_p = stats.shapiro(residuals[:5000])  # 限制样本量
        print(f"\n残差正态性检验 (Shapiro-Wilk):")
        print(f"  统计量: {shapiro_stat:.4f}")
        print(f"  p值: {shapiro_p:.6f}")
        print(f"  正态性: {'符合' if shapiro_p > 0.05 else '不符合'}")
        
        # 异方差检验 (Breusch-Pagan)
        # 简化版本：残差平方与预测值的相关性
        residuals_squared = residuals ** 2
        bp_corr, bp_p = stats.pearsonr(predictions, residuals_squared)
        print(f"\n异方差检验:")
        print(f"  残差平方与预测值相关系数: {bp_corr:.4f}")
        print(f"  p值: {bp_p:.6f}")
        print(f"  同方差性: {'满足' if bp_p > 0.05 else '不满足'}")
        
        return {
            'residuals': residuals,
            'residual_mean': np.mean(residuals),
            'residual_std': np.std(residuals),
            'normality_p': shapiro_p,
            'heteroscedasticity_p': bp_p
        }
    
    def weather_effect_analysis(self):
        """天气效应分析"""
        print(f"\n=== 天气效应详细分析 ===")
        
        # 按天气状况分组分析
        weather_stats = self.clean_df.groupby('天气')['总电量(kWh)'].agg([
            'count', 'mean', 'std', 'median'
        ]).round(2)
        
        print(f"各天气状况用电量统计:")
        print(weather_stats)
        
        # 天气间差异检验 (ANOVA)
        weather_groups = []
        weather_names = []
        
        for weather in self.clean_df['天气'].unique():
            weather_data = self.clean_df[self.clean_df['天气'] == weather]['总电量(kWh)']
            if len(weather_data) >= 30:  # 至少30个样本
                weather_groups.append(weather_data.values)
                weather_names.append(weather)
        
        if len(weather_groups) >= 2:
            f_stat, anova_p = stats.f_oneway(*weather_groups)
            print(f"\n天气状况间差异检验 (ANOVA):")
            print(f"  F统计量: {f_stat:.4f}")
            print(f"  p值: {anova_p:.6f}")
            print(f"  显著性: {'显著' if anova_p < 0.05 else '不显著'}")
        
        # 天气编码的线性趋势
        weather_trend = self.clean_df.groupby('天气编码')['总电量(kWh)'].mean()
        print(f"\n天气编码与平均用电量:")
        for code, avg_power in weather_trend.items():
            weather_desc = [k for k, v in {'晴': 3, '多云': 2, '阴': 1, '雨': 1}.items() if v == code][0]
            print(f"  编码{code} ({weather_desc}): {avg_power:.0f} kWh")
    
    def prediction_analysis(self, model_results):
        """预测分析"""
        print(f"\n=== 预测性能分析 ===")
        
        y_true = self.clean_df['总电量(kWh)'].values
        y_pred = model_results['predictions']
        
        # 预测准确性分析
        relative_error = np.abs(y_true - y_pred) / y_true * 100
        
        print(f"预测准确性:")
        print(f"  平均相对误差: {np.mean(relative_error):.2f}%")
        print(f"  中位数相对误差: {np.median(relative_error):.2f}%")
        print(f"  90%分位数相对误差: {np.percentile(relative_error, 90):.2f}%")
        
        # 预测区间分析
        accuracy_ranges = [10, 20, 30, 50]
        for range_pct in accuracy_ranges:
            accurate_count = np.sum(relative_error <= range_pct)
            accuracy_rate = accurate_count / len(relative_error) * 100
            print(f"  误差≤{range_pct}%的预测占比: {accuracy_rate:.1f}%")
    
    def comprehensive_analysis(self):
        """综合分析"""
        print(f"6月份用电量线性分析报告")
        print("=" * 80)
        
        if self.clean_df is None or len(self.clean_df) == 0:
            print("没有有效数据进行分析")
            return
        
        print(f"\n数据概况:")
        print(f"  分析文件: {os.path.basename(self.excel_file)}")
        print(f"  有效数据: {len(self.clean_df)} 条记录")

        # 处理时间格式
        time_col = pd.to_datetime(self.clean_df['时间'])
        print(f"  时间范围: {time_col.min().date()} 至 {time_col.max().date()}")
        print(f"  涉及地区: {len(self.clean_df['地区'].unique())} 个")
        
        # 准备特征
        self.prepare_features()
        
        # 简单线性回归
        simple_results = self.simple_linear_regression()
        
        # 多元线性回归
        multiple_results = self.multiple_linear_regression()
        
        # 残差分析
        residual_results = self.residual_analysis(multiple_results['predictions'])
        
        # 天气效应分析
        self.weather_effect_analysis()
        
        # 预测分析
        self.prediction_analysis(multiple_results)
        
        # 综合结论
        print(f"\n" + "=" * 60)
        print(f"综合分析结论")
        print(f"=" * 60)
        
        print(f"\n线性关系强度:")
        print(f"  简单线性回归R²: {simple_results['r2']:.6f} ({'极弱' if simple_results['r2'] < 0.01 else '弱' if simple_results['r2'] < 0.1 else '中等'})")
        print(f"  多元线性回归R²: {multiple_results['r2']:.6f} ({'极弱' if multiple_results['r2'] < 0.01 else '弱' if multiple_results['r2'] < 0.1 else '中等'})")
        
        print(f"\n模型显著性:")
        print(f"  简单回归: {'显著' if simple_results['f_p_value'] < 0.05 else '不显著'} (p={simple_results['f_p_value']:.6f})")
        print(f"  多元回归: {'显著' if multiple_results['f_p_value'] < 0.05 else '不显著'} (p={multiple_results['f_p_value']:.6f})")
        
        print(f"\n预测性能:")
        print(f"  简单回归RMSE: {simple_results['rmse']:.0f} kWh")
        print(f"  多元回归RMSE: {multiple_results['rmse']:.0f} kWh")
        
        print(f"\n模型假设检验:")
        print(f"  残差正态性: {'满足' if residual_results['normality_p'] > 0.05 else '不满足'}")
        print(f"  同方差性: {'满足' if residual_results['heteroscedasticity_p'] > 0.05 else '不满足'}")
        
        return {
            'simple_regression': simple_results,
            'multiple_regression': multiple_results,
            'residual_analysis': residual_results
        }

def main():
    """主函数"""
    excel_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/OpenData/合并结果_6月用电量含更新天气信息.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"错误: 文件不存在 - {excel_file}")
        return
    
    try:
        analyzer = LinearAnalysis(excel_file)
        results = analyzer.comprehensive_analysis()
        
        print(f"\n✅ 线性分析完成！")
        print(f"📊 已完成6月份用电量的全面线性分析")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
