#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同一周天相同天气数据整理工具
将Excel文件中同一周天相同天气的数据进行分组整理
例如：所有周一晴天的数据、所有周二雨天的数据等
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
from collections import defaultdict

class WeekdayWeatherGrouper:
    def __init__(self):
        """
        初始化周天天气分组器
        """
        self.df = None
        self.grouped_data = {}
        self.weather_column = None
        self.time_column = None
        
        # 中文周天映射
        self.weekday_names = {
            0: '周一',
            1: '周二', 
            2: '周三',
            3: '周四',
            4: '周五',
            5: '周六',
            6: '周日'
        }
        
    def load_data(self, file_path):
        """
        加载Excel数据

        Args:
            file_path: Excel文件路径
        """
        try:
            print(f"正在读取文件: {os.path.basename(file_path)}")
            self.df = pd.read_excel(file_path)
            print(f"读取成功，共 {len(self.df)} 行数据")

            # 显示所有列名以便调试
            print(f"文件中的所有列: {list(self.df.columns)}")

            # 检查可能的时间/日期列
            time_columns = [col for col in self.df.columns if any(keyword in col for keyword in ['时间', '日期', 'date', 'time', '年', '月', '日'])]
            print(f"发现时间相关列: {time_columns}")

            # 确定时间列
            self.time_column = None
            if '时间' in self.df.columns:
                self.time_column = '时间'
            elif '日期' in self.df.columns:
                self.time_column = '日期'
            elif time_columns:
                self.time_column = time_columns[0]
            else:
                raise ValueError("未找到时间或日期相关列")

            print(f"使用时间列: {self.time_column}")

            # 检查天气相关列
            weather_columns = [col for col in self.df.columns if any(keyword in col for keyword in ['天气', '气温'])]
            if not weather_columns:
                raise ValueError("未找到天气相关列")

            print(f"发现天气相关列: {weather_columns}")

            # 确定主要天气列
            if '天气状况' in self.df.columns:
                self.weather_column = '天气状况'
            elif '天气' in self.df.columns:
                self.weather_column = '天气'
            elif '新天气' in self.df.columns:
                self.weather_column = '新天气'
            else:
                self.weather_column = weather_columns[0]

            print(f"使用天气列: {self.weather_column}")

            return True

        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def preprocess_data(self):
        """
        数据预处理
        """
        try:
            print(f"\n=== 数据预处理 ===")

            # 转换时间列
            self.df['时间_处理'] = pd.to_datetime(self.df[self.time_column])
            self.df['日期'] = self.df['时间_处理'].dt.date

            # 添加周天信息
            self.df['周天数字'] = self.df['时间_处理'].dt.weekday
            self.df['周天'] = self.df['周天数字'].map(self.weekday_names)
            
            # 清理天气数据
            self.df = self.df[self.df[self.weather_column].notna()].copy()
            print(f"清理后数据: {len(self.df)} 行")
            
            # 创建周天+天气组合
            self.df['周天天气组合'] = self.df['周天'] + '_' + self.df[self.weather_column].astype(str)
            
            # 统计信息
            unique_weekdays = self.df['周天'].unique()
            unique_weather = self.df[self.weather_column].unique()
            unique_combinations = self.df['周天天气组合'].unique()
            
            print(f"周天类型: {sorted(unique_weekdays)}")
            print(f"天气类型: {sorted(unique_weather)}")
            print(f"周天天气组合数量: {len(unique_combinations)}")
            
            return True
            
        except Exception as e:
            print(f"数据预处理失败: {e}")
            return False
    
    def group_by_weekday_weather(self):
        """
        按周天和天气进行分组
        """
        try:
            print(f"\n=== 按周天和天气分组 ===")
            
            self.grouped_data = {}
            
            # 按周天天气组合分组
            for combination in self.df['周天天气组合'].unique():
                group_data = self.df[self.df['周天天气组合'] == combination].copy()
                
                # 解析组合名称
                weekday, weather = combination.split('_', 1)
                
                # 统计信息
                total_records = len(group_data)
                date_range = f"{group_data['日期'].min()} 到 {group_data['日期'].max()}"
                unique_dates = group_data['日期'].nunique()
                
                # 计算用电量统计（如果存在相关列）
                power_stats = {}
                power_columns = [col for col in group_data.columns if any(keyword in col for keyword in ['电量', '用电', 'kWh'])]
                
                if power_columns:
                    main_power_col = power_columns[0]  # 使用第一个电量列
                    power_data = pd.to_numeric(group_data[main_power_col], errors='coerce')
                    power_stats = {
                        '总用电量': power_data.sum(),
                        '平均用电量': power_data.mean(),
                        '最大用电量': power_data.max(),
                        '最小用电量': power_data.min(),
                        '用电量标准差': power_data.std()
                    }
                
                self.grouped_data[combination] = {
                    '周天': weekday,
                    '天气': weather,
                    '数据': group_data,
                    '记录数量': total_records,
                    '日期范围': date_range,
                    '不同日期数': unique_dates,
                    '用电量统计': power_stats
                }
                
                print(f"  {weekday} {weather}: {total_records} 条记录, {unique_dates} 个不同日期")
            
            print(f"\n总共生成 {len(self.grouped_data)} 个周天天气组合")
            
            return True
            
        except Exception as e:
            print(f"分组失败: {e}")
            return False
    
    def generate_summary_report(self):
        """
        生成汇总报告
        """
        print(f"\n" + "="*80)
        print(f"同一周天相同天气数据汇总报告")
        print(f"="*80)
        
        if not self.grouped_data:
            print("没有分组数据")
            return
        
        # 按周天统计
        weekday_stats = defaultdict(list)
        for combination, info in self.grouped_data.items():
            weekday_stats[info['周天']].append(info)
        
        print(f"\n各周天的天气分布:")
        for weekday in ['周一', '周二', '周三', '周四', '周五', '周六', '周日']:
            if weekday in weekday_stats:
                weather_list = [info['天气'] for info in weekday_stats[weekday]]
                total_records = sum(info['记录数量'] for info in weekday_stats[weekday])
                print(f"\n{weekday} (共{total_records}条记录):")
                
                for info in sorted(weekday_stats[weekday], key=lambda x: x['记录数量'], reverse=True):
                    weather = info['天气']
                    count = info['记录数量']
                    dates = info['不同日期数']
                    print(f"  {weather}: {count}条记录, {dates}个不同日期")
                    
                    # 显示用电量统计（如果有）
                    if info['用电量统计']:
                        stats = info['用电量统计']
                        print(f"    用电量 - 总计:{stats['总用电量']:.1f}, 平均:{stats['平均用电量']:.1f}, 最大:{stats['最大用电量']:.1f}")
        
        # 按天气统计
        print(f"\n各天气类型的周天分布:")
        weather_stats = defaultdict(list)
        for combination, info in self.grouped_data.items():
            weather_stats[info['天气']].append(info)
        
        for weather, infos in weather_stats.items():
            total_records = sum(info['记录数量'] for info in infos)
            weekdays = [info['周天'] for info in infos]
            print(f"\n{weather} (共{total_records}条记录):")
            
            for info in sorted(infos, key=lambda x: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'].index(x['周天'])):
                weekday = info['周天']
                count = info['记录数量']
                dates = info['不同日期数']
                print(f"  {weekday}: {count}条记录, {dates}个不同日期")
    
    def create_grouped_excel(self, output_file):
        """
        创建分组后的Excel文件
        
        Args:
            output_file: 输出文件路径
        """
        try:
            print(f"\n=== 创建分组Excel文件 ===")
            
            if not self.grouped_data:
                print("没有分组数据")
                return False
            
            # 创建Excel写入器
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                
                # 1. 创建汇总表
                summary_data = []
                for combination, info in self.grouped_data.items():
                    row = {
                        '周天': info['周天'],
                        '天气': info['天气'],
                        '记录数量': info['记录数量'],
                        '不同日期数': info['不同日期数'],
                        '日期范围': info['日期范围']
                    }
                    
                    # 添加用电量统计
                    if info['用电量统计']:
                        row.update({
                            '总用电量': info['用电量统计']['总用电量'],
                            '平均用电量': info['用电量统计']['平均用电量'],
                            '最大用电量': info['用电量统计']['最大用电量'],
                            '最小用电量': info['用电量统计']['最小用电量']
                        })
                    
                    summary_data.append(row)
                
                summary_df = pd.DataFrame(summary_data)
                summary_df = summary_df.sort_values(['周天', '天气'])
                summary_df.to_excel(writer, sheet_name='汇总统计', index=False)
                
                # 2. 创建完整数据表（包含分组信息）
                complete_data = self.df.copy()
                complete_data.to_excel(writer, sheet_name='完整数据', index=False)
                
                # 3. 为每个主要的周天天气组合创建单独的工作表
                # 只为记录数量较多的组合创建单独表格
                major_combinations = [(k, v) for k, v in self.grouped_data.items() if v['记录数量'] >= 3]
                major_combinations.sort(key=lambda x: x[1]['记录数量'], reverse=True)
                
                sheet_count = 0
                max_sheets = 20  # 限制工作表数量
                
                for combination, info in major_combinations:
                    if sheet_count >= max_sheets:
                        break
                    
                    sheet_name = f"{info['周天']}_{info['天气']}"[:31]  # Excel工作表名称限制
                    
                    try:
                        info['数据'].to_excel(writer, sheet_name=sheet_name, index=False)
                        sheet_count += 1
                        print(f"  创建工作表: {sheet_name} ({info['记录数量']}条记录)")
                    except Exception as e:
                        print(f"  创建工作表 {sheet_name} 失败: {e}")
            
            print(f"\n✅ 分组Excel文件已保存到: {output_file}")
            print(f"包含 {sheet_count + 2} 个工作表")
            
            return True
            
        except Exception as e:
            print(f"创建Excel文件失败: {e}")
            return False
    
    def process(self, input_file, output_file):
        """
        完整的处理流程
        
        Args:
            input_file: 输入Excel文件路径
            output_file: 输出Excel文件路径
        """
        print(f"同一周天相同天气数据整理工具")
        print(f"=" * 80)
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        
        # 执行处理步骤
        if not self.load_data(input_file):
            return False
        
        if not self.preprocess_data():
            return False
        
        if not self.group_by_weekday_weather():
            return False
        
        self.generate_summary_report()
        
        if not self.create_grouped_excel(output_file):
            return False
        
        print(f"\n✅ 处理完成！")
        return True

def main():
    """主函数"""
    # 获取用户输入的文件路径
    input_file = input("请输入Excel文件路径: ").strip().strip('"')
    
    if not os.path.exists(input_file):
        print(f"错误: 文件不存在 - {input_file}")
        return
    
    # 生成输出文件名
    base_name = os.path.splitext(os.path.basename(input_file))[0]
    output_file = f"{base_name}_周天天气分组.xlsx"
    
    print(f"输出文件将保存为: {output_file}")
    
    grouper = WeekdayWeatherGrouper()
    grouper.process(input_file, output_file)

if __name__ == "__main__":
    main()
