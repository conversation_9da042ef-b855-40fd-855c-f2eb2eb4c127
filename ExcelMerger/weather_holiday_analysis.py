#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气和节假日工作日对用户电量影响分析工具
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class WeatherHolidayAnalysis:
    def __init__(self):
        """
        初始化分析器
        """
        self.df1 = None  # 详细用电量数据
        self.df2 = None  # 日电量汇总数据
        self.merged_df = None  # 合并后的数据
        
    def load_data(self):
        """
        加载两个Excel文件
        """
        try:
            # 文件路径
            file1 = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/合并结果_6月用电量含更新天气信息包含星期(1).xlsx'
            file2 = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/9e20f478899dc29eb19741386f9343c8/File/6月数据.xlsx'
            
            print("正在加载数据文件...")
            
            # 加载文件1 - 详细用电量数据
            if os.path.exists(file1):
                self.df1 = pd.read_excel(file1)
                print(f"文件1加载成功: {len(self.df1)} 条记录")
            else:
                print("错误: 文件1不存在")
                return False
            
            # 加载文件2 - 日电量汇总数据
            if os.path.exists(file2):
                # 先读取原始数据查看结构
                raw_df2 = pd.read_excel(file2)

                # 找到包含"日期"的行作为列名行
                header_row = None
                for i, row in raw_df2.iterrows():
                    if '日期' in str(row.iloc[0]):
                        header_row = i
                        break

                if header_row is not None:
                    # 使用找到的行作为列名，跳过之前的行
                    self.df2 = pd.read_excel(file2, skiprows=header_row)

                    # 清理列名
                    new_columns = []
                    for col in self.df2.columns:
                        if pd.isna(col) or 'Unnamed' in str(col):
                            new_columns.append(f'col_{len(new_columns)}')
                        else:
                            new_columns.append(str(col))

                    self.df2.columns = new_columns

                    # 删除第一行（原来的列名行）
                    self.df2 = self.df2.iloc[1:].reset_index(drop=True)

                    # 只保留有效的数据行（日期不为空且能解析的）
                    valid_rows = []
                    for i, row in self.df2.iterrows():
                        try:
                            date_val = row.iloc[0]
                            if pd.notna(date_val) and isinstance(date_val, (str, pd.Timestamp)) and '2025' in str(date_val):
                                valid_rows.append(i)
                        except:
                            continue

                    self.df2 = self.df2.iloc[valid_rows].reset_index(drop=True)
                    print(f"文件2加载成功: {len(self.df2)} 条记录")
                else:
                    print("错误: 无法找到有效的列名行")
                    return False
            else:
                print("错误: 文件2不存在")
                return False
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def preprocess_data(self):
        """
        数据预处理
        """
        try:
            print("\n=== 数据预处理 ===")
            
            # 处理文件1
            self.df1['时间'] = pd.to_datetime(self.df1['时间'])
            self.df1['日期'] = self.df1['时间'].dt.date
            
            # 处理文件2
            # 找到日期列和实际日电量列
            date_col = None
            power_col = None
            holiday_col = None

            for col in self.df2.columns:
                if '日期' in str(col):
                    date_col = col
                elif '实际' in str(col) and '电量' in str(col):
                    power_col = col
                elif '节假日' in str(col):
                    holiday_col = col

            if date_col:
                self.df2['日期'] = pd.to_datetime(self.df2[date_col], errors='coerce').dt.date
            if power_col:
                self.df2['实际日电量'] = pd.to_numeric(self.df2[power_col], errors='coerce')
            if holiday_col:
                self.df2['节假日'] = self.df2[holiday_col]

            # 如果没有找到实际日电量列，尝试其他可能的列名
            if power_col is None:
                for col in self.df2.columns:
                    if any(keyword in str(col) for keyword in ['电量', 'power', '负荷']):
                        try:
                            self.df2['实际日电量'] = pd.to_numeric(self.df2[col], errors='coerce')
                            power_col = col
                            break
                        except:
                            continue
            
            # 创建每日汇总数据（从文件1）
            daily_summary = self.df1.groupby(['日期', '地区', '天气', '日期类型']).agg({
                '总电量(kWh)': ['sum', 'mean', 'count'],
                '尖电量(kWh)': 'sum',
                '峰电量(kWh)': 'sum',
                '平电量(kWh)': 'sum',
                '谷电量(kWh)': 'sum'
            }).round(2)
            
            # 重命名列
            daily_summary.columns = ['总电量_日总和', '总电量_平均', '记录数', '尖电量_日总和', '峰电量_日总和', '平电量_日总和', '谷电量_日总和']
            daily_summary = daily_summary.reset_index()
            
            print(f"每日汇总数据: {len(daily_summary)} 条记录")
            print(f"文件2日电量数据: {len(self.df2)} 条记录")
            
            # 合并数据
            merge_cols = ['日期']
            if '实际日电量' in self.df2.columns:
                merge_cols.append('实际日电量')
            if '节假日' in self.df2.columns:
                merge_cols.append('节假日')

            # 只合并存在的列
            available_cols = [col for col in merge_cols if col in self.df2.columns]

            if len(available_cols) > 1:
                self.merged_df = pd.merge(daily_summary, self.df2[available_cols],
                                        on='日期', how='left')
            else:
                self.merged_df = daily_summary.copy()
                print("警告: 无法找到合适的列进行合并，使用原始汇总数据")
            
            print(f"合并后数据: {len(self.merged_df)} 条记录")
            
            return True
            
        except Exception as e:
            print(f"数据预处理失败: {e}")
            return False
    
    def analyze_weather_impact(self):
        """
        分析天气对用电量的影响
        """
        print(f"\n" + "="*60)
        print(f"天气对用电量影响分析")
        print(f"="*60)
        
        # 按天气类型统计
        agg_dict = {'总电量_日总和': ['count', 'mean', 'std', 'min', 'max']}
        if '实际日电量' in self.merged_df.columns:
            agg_dict['实际日电量'] = ['mean', 'std']

        weather_stats = self.merged_df.groupby('天气').agg(agg_dict).round(2)
        
        print(f"\n各天气类型用电量统计:")
        print(weather_stats)
        
        # 天气类型用电量分布
        weather_power = self.merged_df.groupby('天气')['总电量_日总和'].agg(['mean', 'std', 'count']).round(2)
        weather_power = weather_power.sort_values('mean', ascending=False)
        
        print(f"\n天气类型用电量排序 (按平均值):")
        for weather, row in weather_power.iterrows():
            print(f"  {weather}: 平均 {row['mean']:.1f} kWh, 标准差 {row['std']:.1f}, 样本数 {row['count']}")
        
        # 统计检验 - 不同天气类型间的用电量差异
        weather_groups = []
        weather_names = []
        for weather in self.merged_df['天气'].unique():
            weather_data = self.merged_df[self.merged_df['天气'] == weather]['总电量_日总和'].dropna()
            if len(weather_data) >= 3:  # 至少3个样本
                weather_groups.append(weather_data.values)
                weather_names.append(weather)
        
        if len(weather_groups) >= 2:
            try:
                f_stat, p_value = stats.f_oneway(*weather_groups)
                print(f"\n天气类型间用电量差异检验 (ANOVA):")
                print(f"  F统计量: {f_stat:.4f}")
                print(f"  p值: {p_value:.6f}")
                print(f"  结论: {'显著差异' if p_value < 0.05 else '无显著差异'}")
            except:
                print("统计检验失败")
        
        return weather_stats
    
    def analyze_holiday_impact(self):
        """
        分析节假日工作日对用电量的影响
        """
        print(f"\n" + "="*60)
        print(f"节假日工作日对用电量影响分析")
        print(f"="*60)
        
        # 按日期类型统计
        agg_dict = {'总电量_日总和': ['count', 'mean', 'std', 'min', 'max']}
        if '实际日电量' in self.merged_df.columns:
            agg_dict['实际日电量'] = ['mean', 'std']

        date_type_stats = self.merged_df.groupby('日期类型').agg(agg_dict).round(2)
        
        print(f"\n各日期类型用电量统计:")
        print(date_type_stats)
        
        # 工作日 vs 周末 vs 节假日分析
        def classify_day_type(row):
            # 检查是否有节假日列
            if '节假日' in row.index and pd.notna(row['节假日']):
                return '节假日'
            # 根据日期类型判断是否为端午节
            elif '端午节' in str(row['日期类型']):
                return '节假日'
            elif row['日期类型'] in ['星期六', '星期日']:
                return '周末'
            else:
                return '工作日'
        
        self.merged_df['日类型'] = self.merged_df.apply(classify_day_type, axis=1)
        
        day_type_stats = self.merged_df.groupby('日类型')['总电量_日总和'].agg(['mean', 'std', 'count']).round(2)
        day_type_stats = day_type_stats.sort_values('mean', ascending=False)
        
        print(f"\n日类型用电量对比:")
        for day_type, row in day_type_stats.iterrows():
            print(f"  {day_type}: 平均 {row['mean']:.1f} kWh, 标准差 {row['std']:.1f}, 样本数 {row['count']}")
        
        # 统计检验
        day_groups = []
        day_names = []
        for day_type in self.merged_df['日类型'].unique():
            day_data = self.merged_df[self.merged_df['日类型'] == day_type]['总电量_日总和'].dropna()
            if len(day_data) >= 3:
                day_groups.append(day_data.values)
                day_names.append(day_type)
        
        if len(day_groups) >= 2:
            try:
                f_stat, p_value = stats.f_oneway(*day_groups)
                print(f"\n日类型间用电量差异检验 (ANOVA):")
                print(f"  F统计量: {f_stat:.4f}")
                print(f"  p值: {p_value:.6f}")
                print(f"  结论: {'显著差异' if p_value < 0.05 else '无显著差异'}")
            except:
                print("统计检验失败")
        
        return date_type_stats
    
    def analyze_combined_impact(self):
        """
        分析天气和节假日的综合影响
        """
        print(f"\n" + "="*60)
        print(f"天气和节假日综合影响分析")
        print(f"="*60)
        
        # 天气 × 日类型交叉分析
        cross_stats = self.merged_df.groupby(['天气', '日类型'])['总电量_日总和'].agg(['mean', 'count']).round(2)
        cross_stats = cross_stats.reset_index()
        
        print(f"\n天气 × 日类型交叉分析:")
        for _, row in cross_stats.iterrows():
            if row['count'] >= 2:  # 至少2个样本
                print(f"  {row['天气']} × {row['日类型']}: 平均 {row['mean']:.1f} kWh (样本数: {row['count']})")
        
        # 寻找最高和最低用电量组合
        cross_stats_filtered = cross_stats[cross_stats['count'] >= 2]
        if len(cross_stats_filtered) > 0:
            highest = cross_stats_filtered.loc[cross_stats_filtered['mean'].idxmax()]
            lowest = cross_stats_filtered.loc[cross_stats_filtered['mean'].idxmin()]
            
            print(f"\n用电量最高组合: {highest['天气']} × {highest['日类型']} = {highest['mean']:.1f} kWh")
            print(f"用电量最低组合: {lowest['天气']} × {lowest['日类型']} = {lowest['mean']:.1f} kWh")
            print(f"差异: {highest['mean'] - lowest['mean']:.1f} kWh ({(highest['mean'] - lowest['mean'])/lowest['mean']*100:.1f}%)")
        
        return cross_stats
    
    def analyze_regional_differences(self):
        """
        分析地区差异
        """
        print(f"\n" + "="*60)
        print(f"地区差异分析")
        print(f"="*60)
        
        # 按地区统计
        region_stats = self.merged_df.groupby('地区').agg({
            '总电量_日总和': ['mean', 'std', 'count'],
            '天气': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else 'Unknown'
        }).round(2)
        
        region_stats.columns = ['平均用电量', '标准差', '样本数', '主要天气']
        region_stats = region_stats.sort_values('平均用电量', ascending=False)
        
        print(f"\n各地区用电量统计:")
        for region, row in region_stats.iterrows():
            print(f"  {region}: 平均 {row['平均用电量']:.1f} kWh, 标准差 {row['标准差']:.1f}, 主要天气 {row['主要天气']}")
        
        # 地区 × 天气分析
        region_weather = self.merged_df.groupby(['地区', '天气'])['总电量_日总和'].mean().round(2)
        
        print(f"\n地区 × 天气用电量分析 (前10个):")
        top_combinations = region_weather.sort_values(ascending=False).head(10)
        for (region, weather), power in top_combinations.items():
            print(f"  {region} × {weather}: {power:.1f} kWh")
        
        return region_stats
    
    def generate_summary_report(self):
        """
        生成综合分析报告
        """
        print(f"\n" + "="*80)
        print(f"天气和节假日工作日对用电量影响综合报告")
        print(f"="*80)
        
        # 数据概况
        print(f"\n数据概况:")
        print(f"  分析时间段: 2025年6月1日 - 6月30日")
        print(f"  总记录数: {len(self.df1)} 条详细记录")
        print(f"  日汇总记录: {len(self.merged_df)} 条")
        print(f"  覆盖地区: {len(self.merged_df['地区'].unique())} 个")
        print(f"  天气类型: {len(self.merged_df['天气'].unique())} 种")
        
        # 主要发现
        print(f"\n主要发现:")
        
        # 天气影响
        weather_avg = self.merged_df.groupby('天气')['总电量_日总和'].mean().sort_values(ascending=False)
        print(f"  1. 天气影响:")
        print(f"     - 用电量最高天气: {weather_avg.index[0]} ({weather_avg.iloc[0]:.1f} kWh)")
        print(f"     - 用电量最低天气: {weather_avg.index[-1]} ({weather_avg.iloc[-1]:.1f} kWh)")
        print(f"     - 天气影响幅度: {weather_avg.iloc[0] - weather_avg.iloc[-1]:.1f} kWh")
        
        # 日类型影响
        day_type_avg = self.merged_df.groupby('日类型')['总电量_日总和'].mean().sort_values(ascending=False)
        print(f"  2. 日类型影响:")
        for day_type, avg_power in day_type_avg.items():
            print(f"     - {day_type}: {avg_power:.1f} kWh")
        
        # 地区差异
        region_avg = self.merged_df.groupby('地区')['总电量_日总和'].mean().sort_values(ascending=False)
        print(f"  3. 地区差异:")
        print(f"     - 用电量最高地区: {region_avg.index[0]} ({region_avg.iloc[0]:.1f} kWh)")
        print(f"     - 用电量最低地区: {region_avg.index[-1]} ({region_avg.iloc[-1]:.1f} kWh)")
        
        # 建议
        print(f"\n建议:")
        print(f"  1. 电力调度: 根据天气预报和日历安排优化电力供应")
        print(f"  2. 需求预测: 结合天气和节假日信息提高预测准确性")
        print(f"  3. 负荷管理: 在高用电量天气和日期类型时加强负荷管理")
        print(f"  4. 区域规划: 考虑地区差异制定差异化的电力供应策略")
    
    def run_analysis(self):
        """
        运行完整分析
        """
        print("天气和节假日工作日对用户电量影响分析")
        print("="*80)
        
        # 加载数据
        if not self.load_data():
            return False
        
        # 预处理数据
        if not self.preprocess_data():
            return False
        
        # 执行各项分析
        weather_stats = self.analyze_weather_impact()
        holiday_stats = self.analyze_holiday_impact()
        combined_stats = self.analyze_combined_impact()
        regional_stats = self.analyze_regional_differences()
        
        # 生成综合报告
        self.generate_summary_report()
        
        print(f"\n✅ 分析完成！")
        return True

def main():
    """主函数"""
    analyzer = WeatherHolidayAnalysis()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
