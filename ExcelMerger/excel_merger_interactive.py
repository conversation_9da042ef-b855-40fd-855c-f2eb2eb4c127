#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式Excel文件合并工具
允许用户手动选择列名进行匹配
"""

import pandas as pd
import os
import sys
from pathlib import Path

def get_user_input(prompt, options=None):
    """
    获取用户输入
    
    Args:
        prompt: 提示信息
        options: 可选项列表
    
    Returns:
        str: 用户输入
    """
    if options:
        print(f"\n{prompt}")
        for i, option in enumerate(options, 1):
            print(f"{i}. {option}")
        
        while True:
            try:
                choice = int(input("请选择 (输入数字): ")) - 1
                if 0 <= choice < len(options):
                    return options[choice]
                else:
                    print("无效选择，请重新输入")
            except ValueError:
                print("请输入有效数字")
    else:
        return input(f"{prompt}: ")

def read_and_preview_excel(file_path):
    """
    读取Excel文件并显示预览
    
    Args:
        file_path: Excel文件路径
    
    Returns:
        DataFrame: 读取的数据
    """
    try:
        df = pd.read_excel(file_path)
        print(f"\n文件: {os.path.basename(file_path)}")
        print(f"数据行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        print(f"\n前3行数据预览:")
        print(df.head(3))
        return df
    except Exception as e:
        print(f"错误: 无法读取文件 {os.path.basename(file_path)} - {e}")
        return None

def main():
    """
    主函数 - 交互式版本
    """
    print("交互式Excel文件合并工具")
    print("=" * 50)
    
    # 指定文件路径
    customer_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/售电签约客户(2).xlsx"
    usage_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/c51dc8148f20825284f78da3c1eae1a9/File/110kV以下用户历史用电量信息查询20250701-20250713(1).xlsx"
    output_file = "/Users/<USER>/RiderProjects/Solution3/合并结果_用电量信息含地区.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(customer_file):
        print(f"错误: 找不到客户文件")
        print(f"路径: {customer_file}")
        return
    
    if not os.path.exists(usage_file):
        print(f"错误: 找不到用电量文件")
        print(f"路径: {usage_file}")
        return
    
    # 读取并预览文件
    print("\n正在读取客户文件...")
    customer_df = read_and_preview_excel(customer_file)
    if customer_df is None:
        return
    
    print("\n正在读取用电量文件...")
    usage_df = read_and_preview_excel(usage_file)
    if usage_df is None:
        return
    
    # 让用户选择列名
    print("\n请选择客户文件中的户号列:")
    customer_account_col = get_user_input("客户文件户号列", list(customer_df.columns))
    
    print("\n请选择客户文件中的地区列:")
    customer_region_col = get_user_input("客户文件地区列", list(customer_df.columns))
    
    print("\n请选择用电量文件中的户号列:")
    usage_account_col = get_user_input("用电量文件户号列", list(usage_df.columns))
    
    # 显示选择的列
    print(f"\n您的选择:")
    print(f"客户文件户号列: {customer_account_col}")
    print(f"客户文件地区列: {customer_region_col}")
    print(f"用电量文件户号列: {usage_account_col}")
    
    confirm = input("\n确认以上选择? (y/n): ").lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    try:
        # 创建映射字典
        print("\n正在处理数据...")
        customer_mapping = customer_df.set_index(customer_account_col)[customer_region_col].to_dict()
        
        # 添加地区列
        usage_df['地区'] = usage_df[usage_account_col].map(customer_mapping)
        
        # 统计匹配情况
        matched_count = usage_df['地区'].notna().sum()
        total_count = len(usage_df)
        
        print(f"\n匹配结果:")
        print(f"总记录数: {total_count}")
        print(f"成功匹配: {matched_count}")
        print(f"未匹配: {total_count - matched_count}")
        print(f"匹配率: {matched_count/total_count*100:.2f}%")
        
        # 显示未匹配的户号（前10个）
        unmatched = usage_df[usage_df['地区'].isna()][usage_account_col].head(10)
        if len(unmatched) > 0:
            print(f"\n未匹配的户号示例 (前10个):")
            for account in unmatched:
                print(f"  {account}")
        
        # 保存结果
        usage_df.to_excel(output_file, index=False)
        print(f"\n结果已保存到: {output_file}")
        
        # 显示结果预览
        print(f"\n结果预览 (前5行):")
        # 将地区列移到前面显示
        cols = ['地区'] + [col for col in usage_df.columns if col != '地区']
        print(usage_df[cols].head())
        
        print(f"\n处理完成！")
        
    except Exception as e:
        print(f"错误: 处理数据时发生异常 - {e}")

if __name__ == "__main__":
    main()
