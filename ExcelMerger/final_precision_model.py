#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终高精度预测模型
基于深度分析结果的简化但精准的预测系统
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score, mean_absolute_percentage_error
import warnings
warnings.filterwarnings('ignore')

class FinalPrecisionModel:
    def __init__(self):
        """
        初始化最终精度模型
        """
        self.model = None
        self.user_profiles = {}
        self.segment_ratios = {}
        
        print("🎯 最终高精度预测模型")
        print("📊 基于深度分析的简化精准预测")
    
    def load_and_analyze_all_data(self):
        """
        加载和分析所有数据
        """
        print("\n📋 加载所有数据...")
        
        # 加载训练数据
        train_file = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        df_train = pd.read_excel(train_file)
        
        # 加载真实18-19号数据
        actual_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250718-20250719.xlsx'
        df_actual = pd.read_excel(actual_file)
        
        print(f"训练数据: {df_train.shape}")
        print(f"真实数据: {df_actual.shape}")
        
        # 时间处理
        df_train['时间'] = pd.to_datetime(df_train['时间'])
        df_actual['时间'] = pd.to_datetime(df_actual['时间'])
        
        # 提取18号数据用于训练
        df_actual_18 = df_actual[df_actual['时间'].dt.date == pd.to_datetime('2025-07-18').date()].copy()
        df_actual_19 = df_actual[df_actual['时间'].dt.date == pd.to_datetime('2025-07-19').date()].copy()
        
        print(f"7月18日数据: {len(df_actual_18)} 条")
        print(f"7月19日数据: {len(df_actual_19)} 条")
        
        return df_train, df_actual_18, df_actual_19
    
    def create_user_profiles_from_real_data(self, df_train, df_actual_18):
        """
        基于真实数据创建用户画像
        """
        print("\n👤 基于真实数据创建用户画像...")
        
        # 从训练数据中获取用户基础信息
        user_base = df_train.groupby('户号').agg({
            '总电量(kWh)': ['mean', 'std', 'count'],
            '尖电量(kWh)': 'sum',
            '峰电量(kWh)': 'sum',
            '平电量(kWh)': 'sum',
            '谷电量(kWh)': 'sum',
            '公司名称': 'first',
            '地区': 'first'
        }).round(2)
        
        # 扁平化列名
        user_base.columns = ['_'.join(col).strip() for col in user_base.columns]
        user_base = user_base.reset_index()
        
        # 计算分时比例
        total_energy = (user_base['尖电量(kWh)_sum'] + user_base['峰电量(kWh)_sum'] + 
                       user_base['平电量(kWh)_sum'] + user_base['谷电量(kWh)_sum'])
        
        user_base['历史尖电比例'] = user_base['尖电量(kWh)_sum'] / (total_energy + 1)
        user_base['历史峰电比例'] = user_base['峰电量(kWh)_sum'] / (total_energy + 1)
        user_base['历史平电比例'] = user_base['平电量(kWh)_sum'] / (total_energy + 1)
        user_base['历史谷电比例'] = user_base['谷电量(kWh)_sum'] / (total_energy + 1)
        
        # 合并18号真实数据
        if len(df_actual_18) > 0:
            # 重命名列以避免冲突
            df_actual_18_renamed = df_actual_18[['户号', '总电量(kWh)', '尖电量(kWh)', '峰电量(kWh)', '平电量(kWh)', '谷电量(kWh)']].copy()
            df_actual_18_renamed.columns = ['户号', '总电量_real18', '尖电量_real18', '峰电量_real18', '平电量_real18', '谷电量_real18']

            user_base = user_base.merge(df_actual_18_renamed, on='户号', how='left')

            # 计算18号真实分时比例
            user_base['真实尖电比例'] = user_base['尖电量_real18'] / (user_base['总电量_real18'] + 1)
            user_base['真实峰电比例'] = user_base['峰电量_real18'] / (user_base['总电量_real18'] + 1)
            user_base['真实平电比例'] = user_base['平电量_real18'] / (user_base['总电量_real18'] + 1)
            user_base['真实谷电比例'] = user_base['谷电量_real18'] / (user_base['总电量_real18'] + 1)
        
        # 用户分类
        user_base['历史日均用电'] = user_base['总电量(kWh)_mean']
        user_base['用电规模'] = pd.cut(user_base['历史日均用电'], 
                                   bins=[0, 1000, 5000, 15000, np.inf], 
                                   labels=['小型', '中型', '大型', '超大型'])
        
        self.user_profiles = user_base
        
        print(f"✅ 创建了 {len(user_base)} 个用户画像")
        print(f"📊 用电规模分布:")
        for scale, count in user_base['用电规模'].value_counts().items():
            print(f"    {scale}: {count}个用户")
        
        return user_base
    
    def train_simple_but_accurate_model(self):
        """
        训练简单但准确的模型
        """
        print("\n🤖 训练简单但准确的模型...")
        
        # 使用有18号真实数据的用户进行训练
        df_train = self.user_profiles.dropna(subset=['总电量_real18']).copy()
        
        print(f"有效训练样本: {len(df_train)}")
        
        if len(df_train) < 10:
            print("❌ 训练样本不足")
            return False
        
        # 选择最重要的特征
        feature_columns = [
            '历史日均用电',  # 最重要的特征
            '总电量(kWh)_std',  # 用电稳定性
            '总电量(kWh)_count',  # 历史数据量
            '历史尖电比例', '历史峰电比例', '历史平电比例', '历史谷电比例'  # 用电模式
        ]
        
        # 准备训练数据
        X = df_train[feature_columns].fillna(0)
        y = df_train['总电量_real18']
        
        print(f"特征数: {len(feature_columns)}")
        print(f"训练样本: {len(X)}")
        
        # 训练随机森林模型
        self.model = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42
        )
        
        self.model.fit(X, y)
        
        # 评估模型
        y_pred = self.model.predict(X)
        r2 = r2_score(y, y_pred)
        mae = mean_absolute_error(y, y_pred)
        mape = mean_absolute_percentage_error(y, y_pred) * 100
        
        print(f"✅ 模型训练完成")
        print(f"   训练R²: {r2:.4f}")
        print(f"   训练MAE: {mae:.1f} kWh")
        print(f"   训练MAPE: {mape:.1f}%")
        
        # 特征重要性
        feature_importance = pd.DataFrame({
            'feature': feature_columns,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"\\n📊 特征重要性:")
        for _, row in feature_importance.iterrows():
            print(f"   {row['feature']}: {row['importance']:.4f}")
        
        self.feature_names = feature_columns
        
        return True
    
    def calculate_optimal_segment_ratios(self):
        """
        计算最优分时电量比例
        """
        print("\n⚡ 计算最优分时电量比例...")
        
        # 使用有真实18号数据的用户计算平均比例
        df_with_real = self.user_profiles.dropna(subset=['总电量_real18'])
        
        if len(df_with_real) > 0:
            # 使用真实数据的平均比例
            self.segment_ratios = {
                '尖电量': df_with_real['真实尖电比例'].mean(),
                '峰电量': df_with_real['真实峰电比例'].mean(),
                '平电量': df_with_real['真实平电比例'].mean(),
                '谷电量': df_with_real['真实谷电比例'].mean()
            }
        else:
            # 使用历史数据的平均比例
            self.segment_ratios = {
                '尖电量': self.user_profiles['历史尖电比例'].mean(),
                '峰电量': self.user_profiles['历史峰电比例'].mean(),
                '平电量': self.user_profiles['历史平电比例'].mean(),
                '谷电量': self.user_profiles['历史谷电比例'].mean()
            }
        
        # 确保比例和为1
        total_ratio = sum(self.segment_ratios.values())
        if total_ratio > 0:
            for key in self.segment_ratios:
                self.segment_ratios[key] = self.segment_ratios[key] / total_ratio
        
        print(f"✅ 最优分时电量比例:")
        for segment, ratio in self.segment_ratios.items():
            print(f"   {segment}: {ratio:.4f} ({ratio*100:.1f}%)")
        
        return self.segment_ratios
    
    def predict_with_final_model(self, target_date='2025-07-19'):
        """
        使用最终模型进行预测
        """
        print(f"\n🎯 使用最终模型预测 {target_date}...")
        
        if self.model is None:
            print("❌ 请先训练模型")
            return None
        
        # 获取所有用户
        all_users = self.user_profiles['户号'].unique()
        
        # 准备预测特征
        X_pred = self.user_profiles[self.feature_names].fillna(0)
        
        # 进行预测
        predictions = self.model.predict(X_pred)
        predictions = np.maximum(predictions, 0)  # 确保非负
        
        # 创建预测结果
        result_data = []
        
        for i, user in enumerate(all_users):
            total_power = predictions[i]
            
            # 使用最优比例分配分时电量
            result_data.append({
                '表计号': user,
                '户号': user,
                '时间': target_date,
                '总电量(kWh)': round(total_power, 1),
                '尖电量(kWh)': round(total_power * self.segment_ratios['尖电量'], 1),
                '峰电量(kWh)': round(total_power * self.segment_ratios['峰电量'], 1),
                '平电量(kWh)': round(total_power * self.segment_ratios['平电量'], 1),
                '谷电量(kWh)': round(total_power * self.segment_ratios['谷电量'], 1)
            })
        
        df_result = pd.DataFrame(result_data)
        
        print(f"✅ 最终预测完成: {len(df_result)} 个用户")
        print(f"📊 总预测电量: {df_result['总电量(kWh)'].sum():.1f} kWh")
        print(f"📊 平均用电量: {df_result['总电量(kWh)'].mean():.1f} kWh")
        
        return df_result
    
    def evaluate_final_model(self, df_pred, df_actual_19):
        """
        评估最终模型效果
        """
        print(f"\n📈 评估最终模型效果...")
        
        if len(df_actual_19) == 0:
            print("❌ 没有19号真实数据用于评估")
            return None
        
        # 合并预测和真实数据
        df_compare = pd.merge(df_pred, df_actual_19, on='户号', suffixes=('_pred', '_actual'))
        
        print(f"匹配用户数: {len(df_compare)}")
        
        if len(df_compare) == 0:
            print("❌ 没有匹配的用户")
            return None
        
        # 计算误差指标
        mae = mean_absolute_error(df_compare['总电量(kWh)_actual'], df_compare['总电量(kWh)_pred'])
        rmse = np.sqrt(mean_squared_error(df_compare['总电量(kWh)_actual'], df_compare['总电量(kWh)_pred']))
        r2 = r2_score(df_compare['总电量(kWh)_actual'], df_compare['总电量(kWh)_pred'])
        mape = mean_absolute_percentage_error(df_compare['总电量(kWh)_actual'], df_compare['总电量(kWh)_pred']) * 100
        
        # 总量对比
        pred_total = df_compare['总电量(kWh)_pred'].sum()
        actual_total = df_compare['总电量(kWh)_actual'].sum()
        total_error_pct = (pred_total - actual_total) / actual_total * 100
        
        print(f"\\n📊 最终模型评估结果:")
        print(f"   MAE: {mae:.1f} kWh")
        print(f"   RMSE: {rmse:.1f} kWh")
        print(f"   R²: {r2:.4f}")
        print(f"   MAPE: {mape:.1f}%")
        print(f"   总量误差: {total_error_pct:.1f}%")
        
        return {
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'mape': mape,
            'total_error_pct': total_error_pct,
            'comparison_data': df_compare
        }

def main():
    """
    主函数
    """
    print("🎯 最终高精度预测模型")
    print("📊 基于深度分析的简化精准预测")
    print("="*70)
    
    model = FinalPrecisionModel()
    
    try:
        # 1. 加载所有数据
        print("步骤1: 加载所有数据")
        df_train, df_actual_18, df_actual_19 = model.load_and_analyze_all_data()
        
        # 2. 创建用户画像
        print("\\n步骤2: 创建用户画像")
        user_profiles = model.create_user_profiles_from_real_data(df_train, df_actual_18)
        
        # 3. 训练模型
        print("\\n步骤3: 训练简单但准确的模型")
        model_success = model.train_simple_but_accurate_model()
        
        if model_success:
            # 4. 计算最优分时比例
            print("\\n步骤4: 计算最优分时比例")
            segment_ratios = model.calculate_optimal_segment_ratios()
            
            # 5. 进行预测
            print("\\n步骤5: 进行最终预测")
            df_pred = model.predict_with_final_model('2025-07-19')
            
            if df_pred is not None:
                # 6. 评估效果
                print("\\n步骤6: 评估预测效果")
                evaluation = model.evaluate_final_model(df_pred, df_actual_19)
                
                # 7. 保存结果
                output_file = "/Users/<USER>/RiderProjects/Solution3/最终高精度预测_20250719.xlsx"
                df_pred.to_excel(output_file, index=False)
                
                print(f"\\n🎉 最终高精度预测完成！")
                print(f"📁 结果文件: {output_file}")
                
                if evaluation:
                    print(f"\\n🏆 最终效果:")
                    print(f"   MAE: {evaluation['mae']:.1f} kWh")
                    print(f"   R²: {evaluation['r2']:.4f}")
                    print(f"   MAPE: {evaluation['mape']:.1f}%")
                    print(f"   总量误差: {evaluation['total_error_pct']:.1f}%")
            
        else:
            print("❌ 模型训练失败")
            
    except Exception as e:
        print(f"\\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
