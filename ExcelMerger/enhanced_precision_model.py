#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强精度预测模型
解决MAE过高问题，在保持R²和总量精度的同时提升个体预测精度
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from real_tianqi24_scraper import RealTianqi24Scraper
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge, ElasticNet, HuberRegressor
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

class EnhancedPrecisionModel:
    def __init__(self):
        """
        初始化增强精度预测模型
        """
        self.weather_scraper = RealTianqi24Scraper()
        self.user_models = {}
        self.ensemble_models = {}
        self.user_clusters = {}
        self.outlier_handlers = {}
        self.precision_weights = {}
        
        print("🎯 增强精度预测模型")
        print("📊 专注解决MAE过高问题")
    
    def analyze_mae_problems(self):
        """
        深度分析MAE过高的根本原因
        """
        print("\n🔍 深度分析MAE过高的根本原因...")
        
        # 加载数据
        train_file = '/Users/<USER>/RiderProjects/Solution3/户号用电量含公司名称数据分表.xlsx'
        actual_file = '/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/110kV以下用户历史用电量信息查询20250718-20250719.xlsx'
        
        df_train = pd.read_excel(train_file)
        df_actual = pd.read_excel(actual_file)
        
        # 时间处理
        df_train['时间'] = pd.to_datetime(df_train['时间'])
        df_actual['时间'] = pd.to_datetime(df_actual['时间'])
        
        df_actual_18 = df_actual[df_actual['时间'].dt.date == pd.to_datetime('2025-07-18').date()]
        df_actual_19 = df_actual[df_actual['时间'].dt.date == pd.to_datetime('2025-07-19').date()]
        
        print(f"训练数据: {df_train.shape}")
        print(f"18号数据: {df_actual_18.shape}")
        print(f"19号数据: {df_actual_19.shape}")
        
        # 分析用电量分布和异常值
        print(f"\n📊 用电量分布分析:")
        
        # 训练数据分析
        train_stats = df_train['总电量(kWh)'].describe()
        print(f"训练数据统计: {train_stats}")
        
        # 18-19号数据分析
        actual_18_stats = df_actual_18['总电量(kWh)'].describe()
        actual_19_stats = df_actual_19['总电量(kWh)'].describe()
        print(f"18号数据统计: {actual_18_stats}")
        print(f"19号数据统计: {actual_19_stats}")
        
        # 识别异常用户
        q75_train = df_train['总电量(kWh)'].quantile(0.75)
        q25_train = df_train['总电量(kWh)'].quantile(0.25)
        iqr_train = q75_train - q25_train
        
        outlier_threshold_high = q75_train + 1.5 * iqr_train
        outlier_threshold_low = q25_train - 1.5 * iqr_train
        
        print(f"\n🚨 异常值分析:")
        print(f"异常值阈值: {outlier_threshold_low:.1f} - {outlier_threshold_high:.1f} kWh")
        
        train_outliers = df_train[(df_train['总电量(kWh)'] > outlier_threshold_high) | 
                                 (df_train['总电量(kWh)'] < outlier_threshold_low)]
        print(f"训练数据异常值: {len(train_outliers)} 条 ({len(train_outliers)/len(df_train)*100:.1f}%)")
        
        # 分析18-19号变化
        if len(df_actual_18) > 0 and len(df_actual_19) > 0:
            df_change = pd.merge(df_actual_18[['户号', '总电量(kWh)']], 
                               df_actual_19[['户号', '总电量(kWh)']], 
                               on='户号', suffixes=('_18', '_19'))
            
            df_change['变化量'] = df_change['总电量(kWh)_19'] - df_change['总电量(kWh)_18']
            df_change['变化率'] = df_change['变化量'] / (df_change['总电量(kWh)_18'] + 1)
            
            print(f"\n📈 18-19号用电变化分析:")
            print(f"平均变化量: {df_change['变化量'].mean():.1f} kWh")
            print(f"变化量标准差: {df_change['变化量'].std():.1f} kWh")
            print(f"变化率范围: {df_change['变化率'].min():.3f} ~ {df_change['变化率'].max():.3f}")
            
            # 识别变化异常的用户
            large_change_users = df_change[abs(df_change['变化量']) > 5000]
            print(f"大幅变化用户(>5000kWh): {len(large_change_users)} 个")
            
            self.problematic_users = {
                'large_change': large_change_users['户号'].tolist(),
                'outliers': train_outliers['户号'].unique().tolist()
            }
        
        return df_train, df_actual_18, df_actual_19
    
    def create_precision_focused_clusters(self, df_train):
        """
        创建专注于精度的用户聚类
        """
        print("\n👥 创建精度导向的用户聚类...")
        
        # 计算用户精度相关特征
        user_features = df_train.groupby('户号').agg({
            '总电量(kWh)': ['mean', 'std', 'min', 'max', 'count', 'median'],
            '尖电量(kWh)': ['mean', 'std'],
            '峰电量(kWh)': ['mean', 'std'],
            '平电量(kWh)': ['mean', 'std'],
            '谷电量(kWh)': ['mean', 'std'],
            '最高温度(°C)': 'mean',
            '最低温度(°C)': 'mean',
            '湿度(%)': 'mean',
            'AQI': 'mean',
            '公司名称': 'first',
            '地区': 'first'
        }).round(2)
        
        # 扁平化列名
        user_features.columns = ['_'.join(col).strip() for col in user_features.columns]
        user_features = user_features.reset_index()
        
        # 计算精度相关指标
        user_features['用电稳定性'] = user_features['总电量(kWh)_std'] / (user_features['总电量(kWh)_mean'] + 1)
        user_features['用电规律性'] = user_features['总电量(kWh)_median'] / (user_features['总电量(kWh)_mean'] + 1)
        user_features['数据充足性'] = user_features['总电量(kWh)_count'] / 30  # 相对于30天的数据充足度
        user_features['用电范围'] = user_features['总电量(kWh)_max'] - user_features['总电量(kWh)_min']
        
        # 温度敏感性
        user_features['温度敏感性'] = (user_features['最高温度(°C)_mean'] - user_features['最低温度(°C)_mean']) / 10
        
        # 分时电量稳定性
        user_features['尖电稳定性'] = user_features['尖电量(kWh)_std'] / (user_features['尖电量(kWh)_mean'] + 1)
        user_features['峰电稳定性'] = user_features['峰电量(kWh)_std'] / (user_features['峰电量(kWh)_mean'] + 1)
        
        # 预测难度评分
        user_features['预测难度'] = (
            user_features['用电稳定性'] * 0.4 +
            (1 - user_features['用电规律性']) * 0.3 +
            (1 - user_features['数据充足性']) * 0.2 +
            user_features['温度敏感性'] * 0.1
        )
        
        # 基于预测难度的聚类
        clustering_features = [
            '总电量(kWh)_mean', '用电稳定性', '用电规律性', '数据充足性',
            '预测难度', '温度敏感性', '尖电稳定性', '峰电稳定性'
        ]
        
        X_cluster = user_features[clustering_features].fillna(0)
        
        # 标准化
        scaler = StandardScaler()
        X_cluster_scaled = scaler.fit_transform(X_cluster)
        
        # K-means聚类 - 增加聚类数以获得更精细的分组
        kmeans = KMeans(n_clusters=12, random_state=42, n_init=10)
        user_features['精度群体'] = kmeans.fit_predict(X_cluster_scaled)
        
        # 分析每个群体的预测难度
        print(f"✅ 创建了12个精度导向群体:")
        for cluster in range(12):
            cluster_data = user_features[user_features['精度群体'] == cluster]
            if len(cluster_data) > 0:
                avg_power = cluster_data['总电量(kWh)_mean'].mean()
                avg_difficulty = cluster_data['预测难度'].mean()
                stability = cluster_data['用电稳定性'].mean()
                count = len(cluster_data)
                
                difficulty_level = "简单" if avg_difficulty < 0.3 else "中等" if avg_difficulty < 0.6 else "困难"
                
                print(f"  群体{cluster}: {count}个用户, 平均{avg_power:.0f}kWh, 预测{difficulty_level}(难度{avg_difficulty:.3f})")
        
        self.user_clusters = user_features
        
        return user_features
    
    def train_precision_ensemble_models(self, df_train, df_actual_18):
        """
        训练精度导向的集成模型
        """
        print("\n🎯 训练精度导向的集成模型...")
        
        if not hasattr(self, 'user_clusters'):
            print("❌ 请先创建用户聚类")
            return False
        
        # 准备训练数据
        df_train_enhanced = df_train.merge(
            self.user_clusters[['户号', '精度群体', '预测难度', '用电稳定性']], 
            on='户号', how='left'
        )
        
        # 添加18号真实数据作为目标
        if len(df_actual_18) > 0:
            df_actual_18_target = df_actual_18[['户号', '总电量(kWh)']].copy()
            df_actual_18_target.columns = ['户号', '目标电量']
            
            df_train_with_target = df_train_enhanced.merge(
                df_actual_18_target, on='户号', how='inner'
            )
        else:
            print("❌ 没有18号数据作为训练目标")
            return False
        
        print(f"精度训练数据: {len(df_train_with_target)} 条")
        
        # 为每个群体训练集成模型
        for cluster in range(12):
            cluster_data = df_train_with_target[df_train_with_target['精度群体'] == cluster]
            
            if len(cluster_data) < 20:  # 提高最小数据要求
                print(f"  群体{cluster}: 数据不足({len(cluster_data)}条)，跳过")
                continue
            
            print(f"  训练精度群体{cluster}集成模型: {len(cluster_data)}条数据")
            
            # 按用户聚合特征
            user_data = cluster_data.groupby('户号').agg({
                '总电量(kWh)': ['mean', 'std', 'count', 'min', 'max', 'median'],
                '最高温度(°C)': ['mean', 'std'],
                '最低温度(°C)': ['mean', 'std'],
                '湿度(%)': ['mean', 'std'],
                'AQI': ['mean', 'std'],
                '降水量(mm)': 'mean',
                '预测难度': 'first',
                '用电稳定性': 'first',
                '目标电量': 'first'
            }).round(2)
            
            # 扁平化列名
            user_data.columns = ['_'.join(col).strip() for col in user_data.columns]
            user_data = user_data.reset_index()
            
            # 增强特征工程
            user_data['历史变异系数'] = user_data['总电量(kWh)_std'] / (user_data['总电量(kWh)_mean'] + 1)
            user_data['历史范围'] = user_data['总电量(kWh)_max'] - user_data['总电量(kWh)_min']
            user_data['温度变化'] = user_data['最高温度(°C)_std'] + user_data['最低温度(°C)_std']
            user_data['环境稳定性'] = 1 / (user_data['湿度(%)_std'] + user_data['AQI_std'] + 1)
            
            # 精选特征
            feature_columns = [
                '总电量(kWh)_mean', '总电量(kWh)_std', '总电量(kWh)_median',
                '历史变异系数', '历史范围',
                '最高温度(°C)_mean', '最低温度(°C)_mean', '温度变化',
                '湿度(%)_mean', 'AQI_mean', '环境稳定性',
                '降水量(mm)_mean',
                '预测难度_first', '用电稳定性_first'
            ]
            
            X = user_data[feature_columns].fillna(0)
            y = user_data['目标电量_first']
            
            if len(X) < 10:
                print(f"    群体{cluster}: 用户数不足({len(X)}个)，跳过")
                continue
            
            # 训练集成模型
            models = {}
            
            # 1. 随机森林 - 处理非线性关系
            rf_model = RandomForestRegressor(
                n_estimators=200, 
                max_depth=12, 
                min_samples_split=3,
                min_samples_leaf=2,
                random_state=42
            )
            rf_model.fit(X, y)
            models['rf'] = rf_model
            
            # 2. 梯度提升 - 处理复杂模式
            gb_model = GradientBoostingRegressor(
                n_estimators=150,
                max_depth=8,
                learning_rate=0.08,
                subsample=0.8,
                random_state=42
            )
            gb_model.fit(X, y)
            models['gb'] = gb_model
            
            # 3. 极端随机树 - 减少过拟合
            et_model = ExtraTreesRegressor(
                n_estimators=150,
                max_depth=10,
                min_samples_split=3,
                random_state=42
            )
            et_model.fit(X, y)
            models['et'] = et_model
            
            # 4. 鲁棒回归 - 处理异常值
            scaler = RobustScaler()
            X_scaled = scaler.fit_transform(X)
            
            huber_model = HuberRegressor(epsilon=1.35, alpha=0.01)
            huber_model.fit(X_scaled, y)
            models['huber'] = huber_model
            
            # 计算集成权重
            weights = self.calculate_ensemble_weights(models, X, X_scaled, y)
            
            self.ensemble_models[cluster] = {
                'models': models,
                'scaler': scaler,
                'weights': weights,
                'features': feature_columns
            }
            
            # 评估集成效果
            ensemble_pred = self.predict_ensemble(models, weights, X, X_scaled)
            r2 = r2_score(y, ensemble_pred)
            mae = mean_absolute_error(y, ensemble_pred)
            
            print(f"    精度群体{cluster}: R²={r2:.4f}, MAE={mae:.1f}kWh")
        
        print(f"✅ 完成 {len(self.ensemble_models)} 个精度集成模型训练")
        
        return True
