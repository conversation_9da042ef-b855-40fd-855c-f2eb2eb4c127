#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中栋电厂实时电能出力曲线处理工具
从现货结算表提取中栋电厂实时电能数据，生成15分钟间隔出力曲线
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

class ZhongdongRealtimePowerProcessor:
    def __init__(self):
        """
        初始化中栋电厂实时电能处理器
        """
        self.realtime_data = None
        self.processed_data = None
        
        # 15分钟时间点列表 (96个点)
        self.time_points_15min = []
        for hour in range(24):
            self.time_points_15min.append(f"{hour:02d}:15")
            self.time_points_15min.append(f"{hour:02d}:30")
            self.time_points_15min.append(f"{hour:02d}:45")
            if hour < 23:
                self.time_points_15min.append(f"{hour+1:02d}:00")
        self.time_points_15min.append("24:00")
    
    def load_realtime_data(self, file_path):
        """
        加载中栋电厂实时电能数据
        """
        print("正在加载中栋电厂实时电能数据...")
        
        try:
            # 跳过4行，从表头开始读取
            df = pd.read_excel(file_path, skiprows=4)
            df.columns = ['项目名称', '结算单元', '空列1', '时间', '计量电量', '结算电量', '出清电价', '结算电价', '空列2', '结算电费', '空列3']
            
            # 过滤掉表头行
            df = df[df['结算单元'] != '结算单元']
            df = df.dropna(subset=['项目名称', '结算单元'])
            
            # 筛选中栋电厂的实时电能数据
            realtime_data = df[(df['项目名称'] == '实时电能') & (df['结算单元'] == '中栋电厂')]
            
            if len(realtime_data) == 0:
                print("❌ 未找到中栋电厂实时电能数据")
                return False
            
            # 转换数据类型
            realtime_data = realtime_data.copy()
            realtime_data['时间'] = pd.to_datetime(realtime_data['时间'])
            realtime_data['计量电量'] = pd.to_numeric(realtime_data['计量电量'], errors='coerce')
            realtime_data['结算电量'] = pd.to_numeric(realtime_data['结算电量'], errors='coerce')
            
            # 排序
            realtime_data = realtime_data.sort_values('时间').reset_index(drop=True)
            
            self.realtime_data = realtime_data
            
            print(f"✅ 成功加载中栋电厂实时电能数据: {len(realtime_data)} 条记录")
            print(f"   时间范围: {realtime_data['时间'].min()} 到 {realtime_data['时间'].max()}")
            print(f"   计量电量范围: {realtime_data['计量电量'].min():.4f} - {realtime_data['计量电量'].max():.4f} MWh")
            
            # 显示原始数据
            print(f"\n📊 原始实时电能数据:")
            for i, row in realtime_data.iterrows():
                time_str = row['时间'].strftime('%H:%M:%S')
                energy = row['计量电量']
                power = energy / 0.5  # 假设是半小时数据，计算平均功率
                print(f"   {time_str}: {energy:.4f} MWh → {power:.4f} MW")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载实时电能数据失败: {e}")
            return False
    
    def generate_15min_curve(self):
        """
        基于实时电能数据生成15分钟出力曲线
        """
        print("\n正在生成15分钟出力曲线...")
        
        if self.realtime_data is None:
            print("❌ 请先加载实时电能数据")
            return False
        
        results = []
        
        # 处理7月1日数据
        date = datetime(2025, 7, 1).date()
        date_str = date.strftime('%Y-%m-%d')
        
        print(f"  处理日期: {date_str}")
        
        # 为每个15分钟时间点生成数据
        for time_point in self.time_points_15min:
            if time_point == "24:00":
                # 24:00 特殊处理
                results.append({
                    '时间': time_point,
                    '电量(MWh)': 0.0,
                    '功率(MW)': 0.0,
                    '日期': date_str,
                    '数据来源': '边界值'
                })
                continue
            
            # 解析时间点
            hour, minute = map(int, time_point.split(':'))
            current_time = datetime.combine(date, datetime.strptime(time_point, "%H:%M").time())
            
            # 查找最近的实时电能数据点
            energy_mwh = 0.0
            power_mw = 0.0
            data_source = '插值估算'
            
            # 查找匹配的实时数据
            for _, row in self.realtime_data.iterrows():
                data_time = row['时间']
                data_hour = data_time.hour
                data_minute = data_time.minute
                
                # 如果是半小时数据点，则对应的15分钟时间点都使用相同的功率
                if (data_hour == hour and data_minute == 30 and minute in [15, 30]) or \
                   (data_hour == hour and data_minute == 0 and minute in [45]) or \
                   (data_hour == hour-1 and data_minute == 30 and minute == 0):
                    
                    # 使用实时电能数据
                    half_hour_energy = row['计量电量']  # 半小时电量
                    quarter_hour_energy = half_hour_energy / 2  # 15分钟电量
                    quarter_hour_power = quarter_hour_energy / 0.25  # 15分钟功率
                    
                    energy_mwh = quarter_hour_energy
                    power_mw = quarter_hour_power
                    data_source = f'实时数据({data_time.strftime("%H:%M")})'
                    break
            
            # 如果没有找到对应的实时数据，使用插值或默认值
            if energy_mwh == 0.0 and len(self.realtime_data) > 0:
                # 使用线性插值
                if len(self.realtime_data) >= 2:
                    # 在两个实时数据点之间插值
                    first_data = self.realtime_data.iloc[0]
                    last_data = self.realtime_data.iloc[-1]
                    
                    first_time = first_data['时间']
                    last_time = last_data['时间']
                    
                    if current_time <= first_time:
                        # 使用第一个数据点
                        half_hour_energy = first_data['计量电量']
                    elif current_time >= last_time:
                        # 使用最后一个数据点
                        half_hour_energy = last_data['计量电量']
                    else:
                        # 线性插值
                        time_ratio = (current_time - first_time).total_seconds() / (last_time - first_time).total_seconds()
                        half_hour_energy = first_data['计量电量'] + time_ratio * (last_data['计量电量'] - first_data['计量电量'])
                    
                    quarter_hour_energy = half_hour_energy / 2
                    quarter_hour_power = quarter_hour_energy / 0.25
                    
                    energy_mwh = quarter_hour_energy
                    power_mw = quarter_hour_power
                    data_source = '线性插值'
                else:
                    # 只有一个数据点，使用该数据点
                    half_hour_energy = self.realtime_data.iloc[0]['计量电量']
                    quarter_hour_energy = half_hour_energy / 2
                    quarter_hour_power = quarter_hour_energy / 0.25
                    
                    energy_mwh = quarter_hour_energy
                    power_mw = quarter_hour_power
                    data_source = '单点扩展'
            
            results.append({
                '时间': time_point,
                '电量(MWh)': energy_mwh,
                '功率(MW)': power_mw,
                '日期': date_str,
                '数据来源': data_source
            })
        
        # 转换为DataFrame
        self.processed_data = pd.DataFrame(results)
        
        print(f"✅ 15分钟出力曲线生成完成: {len(self.processed_data)} 个时间点")
        
        # 显示统计信息
        total_energy = self.processed_data['电量(MWh)'].sum()
        avg_power = self.processed_data['功率(MW)'].mean()
        print(f"   总电量: {total_energy:.2f} MWh")
        print(f"   平均功率: {avg_power:.2f} MW")
        print(f"   功率范围: {self.processed_data['功率(MW)'].min():.2f} - {self.processed_data['功率(MW)'].max():.2f} MW")
        
        return True
    
    def save_results(self, output_file="中栋电厂实时电能15分钟出力曲线.xlsx"):
        """
        保存处理结果
        """
        print(f"\n正在保存结果...")
        
        if self.processed_data is None:
            print("❌ 没有数据可保存")
            return False
        
        try:
            output_path = f"/Users/<USER>/RiderProjects/Solution3/{output_file}"
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 1. 完整15分钟数据
                output_data = self.processed_data[['时间', '电量(MWh)', '功率(MW)', '日期']].copy()
                output_data.to_excel(writer, sheet_name='15分钟出力曲线', index=False)
                
                # 2. 按您的格式输出
                format_data = []
                for _, row in self.processed_data.iterrows():
                    format_data.append({
                        '7月1号实时出力_时间': row['时间'],
                        '7月1号实时出力_MWH': f"{row['电量(MWh)']:.4f}",
                        '7月1号实时出力_MW_时间': row['时间'],
                        '7月1号实时出力_MW': f"{row['功率(MW)']:.4f}",
                        '总功率=总电量/总时长': f"{row['功率(MW)']:.4f}"
                    })
                
                format_df = pd.DataFrame(format_data)
                format_df.to_excel(writer, sheet_name='按要求格式', index=False)
                
                # 3. 原始实时电能数据
                if self.realtime_data is not None:
                    original_data = self.realtime_data[['时间', '计量电量', '结算电量']].copy()
                    original_data.to_excel(writer, sheet_name='原始实时电能数据', index=False)
                
                # 4. 详细数据（包含数据来源）
                detailed_data = self.processed_data.copy()
                detailed_data.to_excel(writer, sheet_name='详细数据', index=False)
                
                # 5. 统计汇总
                summary_data = {
                    '项目': ['总电量', '平均功率', '最大功率', '最小功率', '数据点数'],
                    '数值': [
                        f"{self.processed_data['电量(MWh)'].sum():.4f} MWh",
                        f"{self.processed_data['功率(MW)'].mean():.4f} MW",
                        f"{self.processed_data['功率(MW)'].max():.4f} MW",
                        f"{self.processed_data['功率(MW)'].min():.4f} MW",
                        f"{len(self.processed_data)} 个"
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='统计汇总', index=False)
            
            print(f"✅ 结果已保存到: {output_path}")
            
            # 显示前10个数据点用于验证
            print(f"\n🔍 前10个数据点验证:")
            for _, row in self.processed_data.head(10).iterrows():
                print(f"   {row['时间']}: 电量={row['电量(MWh)']:.4f} MWh, 功率={row['功率(MW)']:.4f} MW ({row['数据来源']})")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False

def main():
    """
    主函数
    """
    print("中栋电厂实时电能15分钟出力曲线处理工具")
    print("="*60)
    
    # 文件路径
    settlement_file = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/92b3c6718e39b5a723650db5a4190157/Message/MessageTemp/2ededf57e097bef03acef47db0b054f6/File/现货结算表/中栋电厂2025-07-01-day_sbs_gen_pub_detail_25.xlsx"
    
    if not os.path.exists(settlement_file):
        print(f"❌ 现货结算表文件不存在: {settlement_file}")
        return
    
    processor = ZhongdongRealtimePowerProcessor()
    
    # 1. 加载实时电能数据
    if not processor.load_realtime_data(settlement_file):
        return
    
    # 2. 生成15分钟出力曲线
    if not processor.generate_15min_curve():
        return
    
    # 3. 保存结果
    if processor.save_results():
        print(f"\n🎉 处理完成！")
        print(f"📊 成功从中栋电厂实时电能数据生成15分钟出力曲线")
        print(f"⚡ 基于 {len(processor.realtime_data)} 条实时电能记录")
        print(f"📁 结果已保存到Excel文件")
    else:
        print(f"\n❌ 处理失败！")

if __name__ == "__main__":
    main()
